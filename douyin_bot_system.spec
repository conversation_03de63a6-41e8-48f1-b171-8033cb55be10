# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['start_system.py'],
    pathex=[],
    binaries=[],
    datas=[('config', 'config'), ('templates', 'templates'), ('protobuf', 'protobuf'), ('data', 'data')],
    hiddenimports=['websockets', 'flask', 'requests', 'threading', 'asyncio', 'json', 'logging', 'user_management', 'leaderboard_stats', 'manage_processes', 'shared_data', 'protobuf.douyin'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='douyin_bot_system',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
