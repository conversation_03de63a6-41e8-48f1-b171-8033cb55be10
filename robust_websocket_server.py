#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强健的WebSocket服务器
专门处理ESP32连接和握手失败问题
"""

import asyncio
import websockets
import json
import logging
import traceback
from datetime import datetime
from typing import Set, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('websocket_server.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
connected_clients: Set[websockets.WebSocketServerProtocol] = set()
connected_devices: Dict[str, Dict[str, Any]] = {}
connection_stats = {
    'total_connections': 0,
    'successful_connections': 0,
    'failed_handshakes': 0,
    'active_connections': 0
}

class WebSocketHandler:
    """WebSocket连接处理器"""
    
    @staticmethod
    async def handle_client(websocket, path):
        """处理客户端连接"""
        client_addr = "unknown"
        
        try:
            client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
            connection_stats['total_connections'] += 1
            connection_stats['active_connections'] += 1
            
            logger.info(f"🔗 新连接: {client_addr} (路径: {path})")
            
            # 添加到连接集合
            connected_clients.add(websocket)
            
            # 发送欢迎消息
            welcome_msg = {
                "type": "connection_established",
                "data": {
                    "client_id": client_addr,
                    "timestamp": datetime.now().isoformat(),
                    "server": "Robust WebSocket Server v1.0",
                    "path": path
                }
            }
            
            await websocket.send(json.dumps(welcome_msg, ensure_ascii=False))
            logger.info(f"📨 欢迎消息已发送: {client_addr}")
            
            connection_stats['successful_connections'] += 1
            
            # 消息处理循环
            await WebSocketHandler.message_loop(websocket, client_addr)
            
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 连接正常关闭: {client_addr}")
        except websockets.exceptions.InvalidMessage as e:
            logger.warning(f"⚠️ 无效消息 {client_addr}: {e}")
            connection_stats['failed_handshakes'] += 1
        except websockets.exceptions.ProtocolError as e:
            logger.warning(f"⚠️ 协议错误 {client_addr}: {e}")
            connection_stats['failed_handshakes'] += 1
        except EOFError as e:
            logger.warning(f"⚠️ 连接意外关闭 {client_addr}: {e}")
            connection_stats['failed_handshakes'] += 1
        except ConnectionResetError as e:
            logger.warning(f"⚠️ 连接被重置 {client_addr}: {e}")
        except Exception as e:
            logger.error(f"❌ 连接处理异常 {client_addr}: {e}")
            logger.error(f"详细错误: {traceback.format_exc()}")
        finally:
            # 清理连接
            await WebSocketHandler.cleanup_connection(websocket, client_addr)
    
    @staticmethod
    async def message_loop(websocket, client_addr):
        """消息处理循环"""
        try:
            async for message in websocket:
                try:
                    await WebSocketHandler.process_message(websocket, client_addr, message)
                except Exception as e:
                    logger.error(f"❌ 消息处理错误 {client_addr}: {e}")
                    # 发送错误响应但不断开连接
                    await WebSocketHandler.send_error_response(websocket, str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"📱 消息循环结束: {client_addr}")
        except Exception as e:
            logger.error(f"❌ 消息循环异常 {client_addr}: {e}")
    
    @staticmethod
    async def process_message(websocket, client_addr, message):
        """处理单条消息"""
        logger.debug(f"📥 收到消息 [{client_addr}]: {message[:100]}...")
        
        # 忽略简单的数字消息（可能是心跳）
        if message.strip().isdigit() and len(message.strip()) <= 3:
            return
        
        # 忽略空消息
        if not message.strip():
            return
        
        # 尝试解析JSON消息
        if message.startswith('{'):
            try:
                msg_data = json.loads(message)
                msg_type = msg_data.get('type', 'unknown')
                msg_payload = msg_data.get('data', {})
                
                logger.info(f"📋 消息类型: {msg_type} 来自: {client_addr}")
                
                # 路由消息到相应处理器
                await WebSocketHandler.route_message(websocket, client_addr, msg_type, msg_payload)
                
            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ JSON解析失败 {client_addr}: {e}")
                await WebSocketHandler.send_error_response(websocket, "Invalid JSON format")
        else:
            logger.debug(f"📝 非JSON消息 {client_addr}: {message[:50]}...")
    
    @staticmethod
    async def route_message(websocket, client_addr, msg_type, msg_payload):
        """路由消息到相应处理器"""
        handlers = {
            'device_register': WebSocketHandler.handle_device_register,
            'device_status': WebSocketHandler.handle_device_status,
            'speak_message': WebSocketHandler.handle_speak_message,
            'test_ping': WebSocketHandler.handle_test_ping,
            'heartbeat': WebSocketHandler.handle_heartbeat,
        }
        
        handler = handlers.get(msg_type)
        if handler:
            await handler(websocket, client_addr, msg_payload)
        else:
            logger.warning(f"⚠️ 未知消息类型: {msg_type}")
            await WebSocketHandler.send_error_response(websocket, f"Unknown message type: {msg_type}")
    
    @staticmethod
    async def handle_device_register(websocket, client_addr, data):
        """处理设备注册"""
        device_id = data.get('device_id', f'device_{client_addr}')
        device_name = data.get('device_name', device_id)
        device_type = data.get('device_type', 'unknown')
        capabilities = data.get('capabilities', '')
        
        logger.info(f"🤖 设备注册: {device_id} ({device_name}) 来自: {client_addr}")
        
        # 保存设备信息
        connected_devices[device_id] = {
            'device_name': device_name,
            'device_type': device_type,
            'capabilities': capabilities,
            'websocket': websocket,
            'client_addr': client_addr,
            'registered_at': datetime.now().isoformat(),
            'status': 'online'
        }
        
        # 发送注册成功响应
        response = {
            "type": "device_register_response",
            "data": {
                "success": True,
                "message": "设备注册成功",
                "device_id": device_id,
                "server_time": datetime.now().isoformat()
            }
        }
        
        await websocket.send(json.dumps(response, ensure_ascii=False))
        logger.info(f"✅ 设备注册成功: {device_id}")
        
        # 广播设备上线消息
        await WebSocketHandler.broadcast_device_status(device_id, 'online')
    
    @staticmethod
    async def handle_device_status(websocket, client_addr, data):
        """处理设备状态更新"""
        device_id = data.get('device_id', 'unknown')
        status = data.get('status', 'unknown')
        
        logger.info(f"📊 设备状态更新: {device_id} -> {status}")
        
        # 更新设备状态
        if device_id in connected_devices:
            connected_devices[device_id]['status'] = status
            connected_devices[device_id]['last_update'] = datetime.now().isoformat()
            
            # 更新其他状态信息
            for key, value in data.items():
                if key not in ['device_id']:
                    connected_devices[device_id][key] = value
    
    @staticmethod
    async def handle_speak_message(websocket, client_addr, data):
        """处理语音消息"""
        target_device_id = data.get('target_device_id', '')
        message = data.get('message', '')
        
        logger.info(f"🔊 语音消息: {message[:50]}... -> {target_device_id}")
        
        if target_device_id in connected_devices:
            target_device = connected_devices[target_device_id]
            target_websocket = target_device['websocket']
            
            # 转发消息到目标设备
            forward_msg = {
                "type": "speak_message",
                "data": data
            }
            
            try:
                await target_websocket.send(json.dumps(forward_msg, ensure_ascii=False))
                
                # 发送成功响应
                response = {
                    "type": "speak_response",
                    "data": {
                        "success": True,
                        "message": "消息已发送到设备",
                        "target_device": target_device_id
                    }
                }
                await websocket.send(json.dumps(response, ensure_ascii=False))
                
            except Exception as e:
                logger.error(f"❌ 转发消息失败: {e}")
                await WebSocketHandler.send_error_response(websocket, f"转发失败: {str(e)}")
        else:
            await WebSocketHandler.send_error_response(websocket, f"设备 {target_device_id} 未找到")
    
    @staticmethod
    async def handle_test_ping(websocket, client_addr, data):
        """处理测试ping"""
        logger.debug(f"🏓 收到ping: {client_addr}")
        
        response = {
            "type": "test_pong",
            "data": {
                "message": "pong",
                "timestamp": datetime.now().isoformat(),
                "original_data": data
            }
        }
        
        await websocket.send(json.dumps(response, ensure_ascii=False))
    
    @staticmethod
    async def handle_heartbeat(websocket, client_addr, data):
        """处理心跳消息"""
        logger.debug(f"💓 心跳: {client_addr}")
        
        response = {
            "type": "heartbeat_response",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "status": "alive"
            }
        }
        
        await websocket.send(json.dumps(response, ensure_ascii=False))
    
    @staticmethod
    async def send_error_response(websocket, error_message):
        """发送错误响应"""
        response = {
            "type": "error",
            "data": {
                "message": error_message,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        try:
            await websocket.send(json.dumps(response, ensure_ascii=False))
        except Exception as e:
            logger.error(f"❌ 发送错误响应失败: {e}")
    
    @staticmethod
    async def broadcast_device_status(device_id, status):
        """广播设备状态变化"""
        message = {
            "type": "device_status_broadcast",
            "data": {
                "device_id": device_id,
                "status": status,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # 向所有连接的客户端广播
        disconnected_clients = set()
        for client in connected_clients:
            try:
                await client.send(json.dumps(message, ensure_ascii=False))
            except Exception:
                disconnected_clients.add(client)
        
        # 清理断开的连接
        connected_clients.difference_update(disconnected_clients)
    
    @staticmethod
    async def cleanup_connection(websocket, client_addr):
        """清理连接"""
        try:
            # 从连接集合中移除
            connected_clients.discard(websocket)
            connection_stats['active_connections'] = max(0, connection_stats['active_connections'] - 1)
            
            # 清理设备注册
            devices_to_remove = []
            for device_id, device_info in connected_devices.items():
                if device_info.get('websocket') == websocket:
                    devices_to_remove.append(device_id)
            
            for device_id in devices_to_remove:
                del connected_devices[device_id]
                logger.info(f"🗑️ 清理设备注册: {device_id}")
                # 广播设备离线消息
                await WebSocketHandler.broadcast_device_status(device_id, 'offline')
            
            logger.info(f"🧹 连接清理完成: {client_addr}")
            
        except Exception as e:
            logger.error(f"❌ 清理连接异常: {e}")

async def print_stats():
    """定期打印统计信息"""
    while True:
        await asyncio.sleep(30)  # 每30秒打印一次
        logger.info(f"📊 连接统计: 总连接={connection_stats['total_connections']}, "
                   f"成功={connection_stats['successful_connections']}, "
                   f"失败握手={connection_stats['failed_handshakes']}, "
                   f"当前活跃={connection_stats['active_connections']}, "
                   f"注册设备={len(connected_devices)}")

async def main():
    """主函数"""
    host = "0.0.0.0"
    port = 15000
    
    logger.info("🚀 启动强健WebSocket服务器...")
    logger.info(f"📍 监听地址: {host}:{port}")
    
    try:
        # 启动统计任务
        stats_task = asyncio.create_task(print_stats())
        
        # 启动WebSocket服务器
        async with websockets.serve(
            WebSocketHandler.handle_client,
            host,
            port,
            ping_interval=30,      # 30秒ping间隔
            ping_timeout=10,       # 10秒ping超时
            close_timeout=10,      # 10秒关闭超时
            max_size=2**20,        # 1MB最大消息大小
            max_queue=32,          # 32消息队列大小
            compression=None       # 禁用压缩以提高兼容性
        ):
            logger.info(f"✅ WebSocket服务器启动成功: {host}:{port}")
            logger.info("🔧 服务器配置: ping_interval=30s, ping_timeout=10s, max_size=1MB")
            logger.info("⏳ 等待客户端连接...")
            logger.info("💡 提示: 握手失败错误是正常的，通常由浏览器访问或无效连接引起")
            
            # 保持服务器运行
            await asyncio.Future()
            
    except OSError as e:
        logger.error(f"❌ 无法启动服务器: {e}")
        logger.error(f"💡 请检查端口 {port} 是否被占用")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 服务器被用户停止")
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}")
        logger.error(f"详细错误: {traceback.format_exc()}")
