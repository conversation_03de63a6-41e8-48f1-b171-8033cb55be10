#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复用户管理和邀请码管理系统
"""

import os
import json
import shutil
from datetime import datetime

def check_and_create_directories():
    """检查并创建必要的目录"""
    print("🔍 检查系统目录结构...")
    
    directories = [
        'data',
        'data/runtime',
        'data/stats',
        'data/logs',
        'config',
        'config/devices',
        'config/rooms',
        'templates'
    ]
    
    created_dirs = []
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            created_dirs.append(directory)
            print(f"📁 创建目录: {directory}")
    
    if created_dirs:
        print(f"✅ 创建了 {len(created_dirs)} 个目录")
    else:
        print("✅ 所有必要目录已存在")

def check_user_data_file():
    """检查用户数据文件"""
    print("\n🔍 检查用户数据文件...")
    
    runtime_users_file = "data/runtime/users.json"
    default_users_file = "users.json"
    
    if os.path.exists(runtime_users_file):
        print(f"✅ 运行时用户数据文件存在: {runtime_users_file}")
        
        # 检查文件内容
        try:
            with open(runtime_users_file, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
            print(f"✅ 用户数据文件格式正确，包含 {len(users_data)} 个用户")
            
            # 检查是否有管理员账户
            if 'admin' in users_data:
                admin_user = users_data['admin']
                print(f"✅ 管理员账户存在: {admin_user.get('display_name', 'admin')}")
            else:
                print("⚠️ 管理员账户不存在，将在启动时自动创建")
                
        except Exception as e:
            print(f"❌ 用户数据文件格式错误: {e}")
            return False
            
    elif os.path.exists(default_users_file):
        print(f"⚠️ 使用旧的用户数据文件: {default_users_file}")
        print("💡 建议迁移到新位置")
        
        # 迁移到新位置
        try:
            shutil.copy2(default_users_file, runtime_users_file)
            print(f"✅ 已迁移用户数据到: {runtime_users_file}")
        except Exception as e:
            print(f"❌ 迁移用户数据失败: {e}")
            return False
    else:
        print("⚠️ 用户数据文件不存在，将在启动时自动创建")
    
    return True

def check_invitation_codes_file():
    """检查邀请码数据文件"""
    print("\n🔍 检查邀请码数据文件...")
    
    invitation_codes_file = "data/invitation_codes.json"
    
    if os.path.exists(invitation_codes_file):
        print(f"✅ 邀请码数据文件存在: {invitation_codes_file}")
        
        # 检查文件内容
        try:
            with open(invitation_codes_file, 'r', encoding='utf-8') as f:
                codes_data = json.load(f)
            print(f"✅ 邀请码数据文件格式正确，包含 {len(codes_data)} 个邀请码")
            
            # 统计邀请码状态
            active_count = sum(1 for code in codes_data.values() if code.get('status') == 'active')
            print(f"📊 活跃邀请码: {active_count} 个")
            
        except Exception as e:
            print(f"❌ 邀请码数据文件格式错误: {e}")
            return False
    else:
        print("⚠️ 邀请码数据文件不存在，将在启动时自动创建")
    
    return True

def fix_user_registration_validation():
    """修复用户注册验证问题"""
    print("\n🔍 检查用户注册验证逻辑...")
    
    user_management_file = "user_management.py"
    
    if not os.path.exists(user_management_file):
        print(f"❌ 用户管理文件不存在: {user_management_file}")
        return False
    
    # 读取文件内容
    try:
        with open(user_management_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查用户名验证逻辑
        if 'username.isalnum()' in content:
            print("⚠️ 发现严格的用户名验证（只允许字母和数字）")
            print("💡 建议：允许用户名包含下划线和连字符")
            
            # 提供修复建议
            print("\n🔧 修复建议:")
            print("   在 user_management.py 中找到用户名验证部分")
            print("   将 'username.isalnum()' 改为更灵活的验证")
            print("   例如：允许字母、数字、下划线和连字符")
        else:
            print("✅ 用户名验证逻辑看起来正常")
            
    except Exception as e:
        print(f"❌ 读取用户管理文件失败: {e}")
        return False
    
    return True

def check_flask_server_config():
    """检查Flask服务器配置"""
    print("\n🔍 检查Flask服务器配置...")
    
    flask_server_file = "flask_web_server.py"
    
    if not os.path.exists(flask_server_file):
        print(f"❌ Flask服务器文件不存在: {flask_server_file}")
        return False
    
    print("✅ Flask服务器文件存在")
    
    # 检查模板文件
    template_files = [
        'templates/users.html',
        'templates/invitation-codes.html',
        'templates/login.html'
    ]
    
    missing_templates = []
    for template_file in template_files:
        if not os.path.exists(template_file):
            missing_templates.append(template_file)
    
    if missing_templates:
        print(f"⚠️ 缺少模板文件: {missing_templates}")
        return False
    else:
        print("✅ 所有必要的模板文件都存在")
    
    return True

def create_test_invitation_code():
    """创建测试邀请码"""
    print("\n🔍 创建测试邀请码...")
    
    try:
        from invitation_codes import invitation_manager
        
        # 创建一个测试邀请码
        result = invitation_manager.create_invitation_code(
            created_by="system",
            valid_days=30,
            max_uses=5,
            note="系统修复测试邀请码"
        )
        
        if result['success']:
            test_code = result['code']
            print(f"✅ 创建测试邀请码成功: {test_code}")
            print(f"   有效期: 30天")
            print(f"   使用次数: 5次")
            return test_code
        else:
            print(f"❌ 创建测试邀请码失败: {result['message']}")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试邀请码异常: {e}")
        return None

def run_system_test():
    """运行系统测试"""
    print("\n🔍 运行系统功能测试...")
    
    try:
        # 测试用户管理器
        from user_management import user_manager
        
        # 检查管理员账户
        admin_info = user_manager.get_user_info('admin')
        if admin_info:
            print(f"✅ 管理员账户正常: {admin_info.get('display_name')}")
        else:
            print("⚠️ 管理员账户不存在")
        
        # 获取所有用户
        all_users = user_manager.get_all_users()
        print(f"✅ 用户管理器正常，共 {len(all_users)} 个用户")
        
        # 测试邀请码管理器
        from invitation_codes import invitation_manager
        
        # 获取所有邀请码
        all_codes = invitation_manager.list_codes()
        print(f"✅ 邀请码管理器正常，共 {len(all_codes)} 个邀请码")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主修复函数"""
    print("🔧 AI机器人自动化直播控制台 - 用户管理和邀请码管理系统修复工具")
    print("=" * 80)
    
    # 1. 检查目录结构
    check_and_create_directories()
    
    # 2. 检查用户数据文件
    if not check_user_data_file():
        print("❌ 用户数据文件检查失败")
        return
    
    # 3. 检查邀请码数据文件
    if not check_invitation_codes_file():
        print("❌ 邀请码数据文件检查失败")
        return
    
    # 4. 检查用户注册验证
    fix_user_registration_validation()
    
    # 5. 检查Flask服务器配置
    if not check_flask_server_config():
        print("❌ Flask服务器配置检查失败")
        return
    
    # 6. 运行系统测试
    if not run_system_test():
        print("❌ 系统功能测试失败")
        return
    
    # 7. 创建测试邀请码
    test_code = create_test_invitation_code()
    
    print("\n" + "=" * 80)
    print("✅ 系统修复和检查完成！")
    print("\n📋 系统状态总结:")
    print("   ✅ 目录结构正常")
    print("   ✅ 用户数据文件正常")
    print("   ✅ 邀请码数据文件正常")
    print("   ✅ Flask服务器配置正常")
    print("   ✅ 系统功能测试通过")
    
    if test_code:
        print(f"\n🎁 测试邀请码: {test_code}")
        print("   可以使用此邀请码测试用户注册功能")
    
    print("\n🚀 启动建议:")
    print("   1. 运行: python flask_web_server.py")
    print("   2. 访问: http://localhost:15008")
    print("   3. 使用管理员账户登录: admin / admin123")
    print("   4. 测试用户管理和邀请码管理功能")

if __name__ == "__main__":
    main()