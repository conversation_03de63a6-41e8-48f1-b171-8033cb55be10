{"reset_time": "2025-08-06T01:55:00.000000", "operation": "complete_system_reset", "backup_location": "backup_20250806_014058/", "cleanup_results": {"users_before": 114, "users_after": 4, "rooms_before": 74, "rooms_after": 4, "stats_cleared": true, "logs_cleared": true}, "current_users": [{"username": "admin", "password": "admin123", "role": "admin", "display_name": "系统管理员", "member_status": "permanent", "note": "系统管理员，永久会员"}, {"username": "demo001", "password": "123456", "role": "user", "display_name": "演示用户001", "member_status": "active", "member_days": 90, "note": "演示用户，90天会员"}, {"username": "demo002", "password": "123456", "role": "user", "display_name": "演示用户002", "member_status": "active", "member_days": 180, "note": "演示用户，180天会员"}, {"username": "demo003", "password": "123456", "role": "user", "display_name": "演示用户003", "member_status": "none", "member_days": 0, "note": "演示用户，普通用户"}], "current_rooms": [{"room_id": "100001", "room_name": "演示直播间001", "owner": "demo001", "device_id": "device1", "status": "stopped", "note": "演示直播间，分配给demo001"}, {"room_id": "100002", "room_name": "演示直播间002", "owner": "demo002", "device_id": "device2", "status": "stopped", "note": "演示直播间，分配给demo002"}, {"room_id": "100003", "room_name": "测试直播间", "owner": "admin", "device_id": "device3", "status": "stopped", "note": "管理员测试直播间"}, {"room_id": "template_with_new_features", "room_name": "示例直播间", "owner": "system", "device_id": "template", "status": "template", "note": "房间配置模板文件"}], "invitation_codes": {"total_created": 9, "used": 3, "remaining": 6, "note": "已为演示用户使用3个邀请码，剩余6个可用"}, "system_features": {"user_registration": "invitation_code_required", "member_management": "enabled", "room_management": "enabled", "invitation_code_management": "enabled", "admin_panel": "integrated_in_navigation"}, "access_information": {"web_interface": "http://localhost:15008", "admin_login": {"username": "admin", "password": "admin123", "permissions": ["user_management", "invitation_codes", "room_management", "system_admin"]}, "demo_accounts": [{"username": "demo001", "password": "123456", "permissions": ["room_access", "member_features"], "member_until": "2025-11-04"}, {"username": "demo002", "password": "123456", "permissions": ["room_access", "member_features"], "member_until": "2026-02-02"}, {"username": "demo003", "password": "123456", "permissions": ["limited_access"], "member_until": "none"}]}, "next_steps": ["启动系统: python flask_web_server.py", "访问网页界面: http://localhost:15008", "使用admin账户登录管理系统", "在用户管理页面查看所有用户", "在邀请码管理页面管理邀请码", "测试演示账户功能", "根据需要创建更多用户和直播间"], "available_scripts": [{"script": "create_users.py", "description": "批量创建用户脚本", "usage": "python create_users.py"}, {"script": "create_rooms.py", "description": "批量创建直播间脚本", "usage": "python create_rooms.py"}, {"script": "create_demo_data.py", "description": "创建演示数据脚本", "usage": "python create_demo_data.py"}, {"script": "cleanup_data.py", "description": "清理系统数据脚本", "usage": "python cleanup_data.py"}], "notes": ["系统已完全重置，只保留必要的管理员账户", "所有演示数据都已创建完成", "邀请码机制已启用，新用户注册需要邀请码", "会员权限系统已配置，demo001和demo002为会员用户", "所有原始数据已备份到backup_20250806_014058目录", "系统现在处于干净的初始状态，可以开始正常使用"]}