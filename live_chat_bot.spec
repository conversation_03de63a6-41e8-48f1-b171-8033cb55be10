# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['live_chat_bot.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('templates', 'templates'),
        ('static', 'static'),
    ],
    hiddenimports=[
        'websockets',
        'requests',
        'flask',
        'threading',
        'json',
        'time',
        'datetime',
        'asyncio',
        'logging',
        're',
        'os',
        'sys',
        'traceback',
        'queue',
        'collections',
        'functools',
        'urllib3',
        'ssl',
        'socket',
        'hashlib',
        'base64',
        'uuid',
        'random',
        'math',
        'copy',
        'weakref',
        'gc',
        'signal',
        'atexit',
        'platform',
        'subprocess',
        'multiprocessing',
        'concurrent.futures',
        'user_management',
        'leaderboard_stats',
        'manage_processes',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='live_chat_bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)