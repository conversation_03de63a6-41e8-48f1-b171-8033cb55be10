#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键初始化系统脚本
"""

import subprocess
import sys
import json
from datetime import datetime

def run_script(script_name, description):
    """运行脚本"""
    print(f"\n🚀 {description}...")
    print("-" * 40)
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True, 
                              input="YES\n3\n20\n0.7\ny\n3\n15\ny\n",  # 自动输入
                              cwd=".")
        
        if result.returncode == 0:
            print(f"✅ {description}完成")
            return True
        else:
            print(f"❌ {description}失败")
            return False
    except Exception as e:
        print(f"❌ 运行脚本失败: {e}")
        return False

def create_summary_report():
    """创建总结报告"""
    print("\n📊 生成系统初始化报告...")
    
    report = {
        "initialization_time": datetime.now().isoformat(),
        "system_status": "initialized",
        "components": {
            "users": {
                "admin_users": 1,
                "demo_users": 3,
                "random_users": 20,
                "total_users": 24,
                "member_ratio": 0.7
            },
            "rooms": {
                "demo_rooms": 3,
                "random_rooms": 15,
                "total_rooms": 18
            },
            "invitation_codes": {
                "created": 30,
                "used": 23,
                "remaining": 7
            }
        },
        "default_credentials": {
            "admin": {
                "username": "admin",
                "password": "admin123",
                "role": "admin",
                "member_status": "permanent"
            },
            "demo_users": [
                {"username": "demo001", "password": "123456", "member_days": 90},
                {"username": "demo002", "password": "123456", "member_days": 180},
                {"username": "demo003", "password": "123456", "member_days": 0}
            ]
        },
        "demo_rooms": [
            {"room_id": "100001", "room_name": "演示直播间001", "owner": "demo001"},
            {"room_id": "100002", "room_name": "演示直播间002", "owner": "demo002"},
            {"room_id": "100003", "room_name": "测试直播间", "owner": "admin"}
        ],
        "next_steps": [
            "访问 http://localhost:15008 进入系统",
            "使用 admin/admin123 登录管理员账户",
            "在用户管理页面查看所有创建的用户",
            "在邀请码管理页面查看剩余邀请码",
            "在主页面查看所有创建的直播间",
            "可以使用演示账户 demo001/123456 测试普通用户功能"
        ]
    }
    
    try:
        with open("system_initialization_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 初始化报告已保存到: system_initialization_report.json")
        return report
    except Exception as e:
        print(f"❌ 保存初始化报告失败: {e}")
        return None

def print_summary(report):
    """打印总结信息"""
    print("\n" + "=" * 60)
    print("🎉 系统初始化完成！")
    print("=" * 60)
    
    if report:
        print(f"📊 初始化统计:")
        print(f"   👥 用户总数: {report['components']['users']['total_users']} 个")
        print(f"      - 管理员: {report['components']['users']['admin_users']} 个")
        print(f"      - 演示用户: {report['components']['users']['demo_users']} 个")
        print(f"      - 随机用户: {report['components']['users']['random_users']} 个")
        print(f"      - 会员比例: {report['components']['users']['member_ratio']*100}%")
        
        print(f"   🏠 直播间总数: {report['components']['rooms']['total_rooms']} 个")
        print(f"      - 演示直播间: {report['components']['rooms']['demo_rooms']} 个")
        print(f"      - 随机直播间: {report['components']['rooms']['random_rooms']} 个")
        
        print(f"   🎫 邀请码: {report['components']['invitation_codes']['created']} 个")
        print(f"      - 已使用: {report['components']['invitation_codes']['used']} 个")
        print(f"      - 剩余: {report['components']['invitation_codes']['remaining']} 个")
    
    print(f"\n🔑 默认账户信息:")
    print(f"   管理员: admin / admin123")
    print(f"   演示用户1: demo001 / 123456 (会员90天)")
    print(f"   演示用户2: demo002 / 123456 (会员180天)")
    print(f"   演示用户3: demo003 / 123456 (普通用户)")
    print(f"   其他用户: 密码统一为 123456")
    
    print(f"\n🏠 演示直播间:")
    print(f"   100001 - 演示直播间001 (demo001)")
    print(f"   100002 - 演示直播间002 (demo002)")
    print(f"   100003 - 测试直播间 (admin)")
    
    print(f"\n🚀 下一步操作:")
    print(f"   1. 启动系统: python flask_web_server.py")
    print(f"   2. 访问: http://localhost:15008")
    print(f"   3. 登录管理员账户开始管理")
    
    print(f"\n📁 相关文件:")
    print(f"   - created_users.json: 创建的用户列表")
    print(f"   - created_rooms.json: 创建的直播间列表")
    print(f"   - system_initialization_report.json: 完整初始化报告")
    print(f"   - backup_20250806_014058/: 原始数据备份")

def main():
    """主函数"""
    print("🚀 AI机器人自动化直播控制台 - 系统初始化")
    print("=" * 60)
    print("此脚本将自动完成以下操作:")
    print("1. 创建演示用户和随机用户")
    print("2. 为用户开通会员权限")
    print("3. 创建演示直播间和随机直播间")
    print("4. 生成初始化报告")
    print("\n⚠️  注意: 系统数据已在之前清理，现在将创建全新的测试数据")
    
    confirm = input("\n确定要开始初始化吗？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 初始化已取消")
        return
    
    success_count = 0
    total_steps = 2
    
    # 步骤1: 创建用户
    if run_script("create_users.py", "创建用户数据"):
        success_count += 1
    
    # 步骤2: 创建直播间
    if run_script("create_rooms.py", "创建直播间数据"):
        success_count += 1
    
    # 生成报告
    report = create_summary_report()
    
    # 打印总结
    if success_count == total_steps:
        print_summary(report)
    else:
        print(f"\n⚠️  初始化部分完成: {success_count}/{total_steps} 个步骤成功")
        print("请检查错误信息并手动完成剩余步骤")

if __name__ == "__main__":
    main()
