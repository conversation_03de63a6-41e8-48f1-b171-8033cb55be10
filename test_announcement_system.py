#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公告系统测试脚本
测试公告的创建、管理和用户阅读状态功能
"""

import json
import os
from announcement_manager import announcement_manager

def test_create_announcements():
    """测试创建公告"""
    print("=" * 50)
    print("测试1: 创建公告")
    print("=" * 50)
    
    test_announcements = [
        {
            "title": "系统维护通知",
            "content": "系统将于今晚23:00-01:00进行维护，期间服务可能中断，请提前做好准备。",
            "status": "published"
        },
        {
            "title": "新功能上线",
            "content": "我们很高兴地宣布，新的公告系统已经上线！现在您可以及时收到重要通知。",
            "status": "published"
        },
        {
            "title": "会员优惠活动",
            "content": "限时优惠！现在购买会员可享受8折优惠，活动截止到本月底。",
            "status": "draft"
        }
    ]
    
    created_announcements = []
    
    for i, announcement_data in enumerate(test_announcements):
        print(f"\n创建公告 {i+1}: {announcement_data['title']}")
        
        result = announcement_manager.create_announcement(
            title=announcement_data['title'],
            content=announcement_data['content'],
            created_by="test_admin",
            status=announcement_data['status']
        )
        
        if result['success']:
            print(f"✅ 创建成功: {result['announcement']['id']}")
            created_announcements.append(result['announcement'])
        else:
            print(f"❌ 创建失败: {result['message']}")
    
    return created_announcements

def test_get_announcements():
    """测试获取公告列表"""
    print("\n" + "=" * 50)
    print("测试2: 获取公告列表")
    print("=" * 50)
    
    # 获取所有公告
    all_announcements = announcement_manager.get_announcements(include_drafts=True)
    print(f"✅ 获取所有公告: {len(all_announcements)} 个")
    
    # 获取已发布公告
    published_announcements = announcement_manager.get_announcements(status="published")
    print(f"✅ 获取已发布公告: {len(published_announcements)} 个")
    
    # 获取草稿公告
    draft_announcements = announcement_manager.get_announcements(status="draft")
    print(f"✅ 获取草稿公告: {len(draft_announcements)} 个")
    
    # 显示公告详情
    print("\n公告列表:")
    for i, announcement in enumerate(all_announcements[:3]):  # 只显示前3个
        print(f"{i+1}. [{announcement['status']}] {announcement['title']}")
        print(f"   内容: {announcement['content'][:50]}...")
        print(f"   创建时间: {announcement['created_at']}")
    
    return all_announcements

def test_user_read_status():
    """测试用户阅读状态"""
    print("\n" + "=" * 50)
    print("测试3: 用户阅读状态")
    print("=" * 50)
    
    test_users = ["user1", "user2", "admin"]
    published_announcements = announcement_manager.get_announcements(status="published")
    
    if not published_announcements:
        print("⚠️ 没有已发布的公告，跳过阅读状态测试")
        return
    
    for username in test_users:
        print(f"\n测试用户: {username}")
        
        # 获取未读数量
        unread_count = announcement_manager.get_unread_count(username)
        print(f"  未读公告数量: {unread_count}")
        
        # 获取未读公告列表
        unread_announcements = announcement_manager.get_unread_announcements(username)
        print(f"  未读公告列表: {len(unread_announcements)} 个")
        
        # 标记第一个公告为已读
        if unread_announcements:
            first_announcement = unread_announcements[0]
            success = announcement_manager.mark_as_read(username, first_announcement['id'])
            if success:
                print(f"  ✅ 标记已读: {first_announcement['title']}")
                
                # 重新检查未读数量
                new_unread_count = announcement_manager.get_unread_count(username)
                print(f"  更新后未读数量: {new_unread_count}")
            else:
                print(f"  ❌ 标记已读失败")

def test_update_announcement():
    """测试更新公告"""
    print("\n" + "=" * 50)
    print("测试4: 更新公告")
    print("=" * 50)
    
    announcements = announcement_manager.get_announcements(include_drafts=True)
    if not announcements:
        print("⚠️ 没有公告可更新")
        return
    
    # 更新第一个公告
    announcement = announcements[0]
    print(f"更新公告: {announcement['title']}")
    
    result = announcement_manager.update_announcement(
        announcement_id=announcement['id'],
        title=announcement['title'] + " (已更新)",
        content=announcement['content'] + "\n\n更新时间: " + announcement_manager.announcements_data[announcement['id']]['updated_at']
    )
    
    if result['success']:
        print(f"✅ 更新成功")
        print(f"  新标题: {result['announcement']['title']}")
    else:
        print(f"❌ 更新失败: {result['message']}")

def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 50)
    print("测试5: 文件结构")
    print("=" * 50)
    
    # 检查公告文件
    announcements_file = "data/announcements.json"
    if os.path.exists(announcements_file):
        with open(announcements_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 公告文件存在: {len(data)} 个公告")
        
        # 显示文件大小
        file_size = os.path.getsize(announcements_file)
        print(f"  文件大小: {file_size} 字节")
    else:
        print(f"❌ 公告文件不存在: {announcements_file}")
    
    # 检查阅读状态文件
    read_status_file = "data/announcement_read_status.json"
    if os.path.exists(read_status_file):
        with open(read_status_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 阅读状态文件存在: {len(data)} 个用户")
        
        # 显示用户阅读状态
        for username, status in list(data.items())[:3]:  # 只显示前3个
            print(f"  {username}: {len(status)} 个已读公告")
    else:
        print(f"❌ 阅读状态文件不存在: {read_status_file}")

def test_api_simulation():
    """模拟API调用"""
    print("\n" + "=" * 50)
    print("测试6: 模拟API调用")
    print("=" * 50)
    
    # 模拟获取公告列表API
    print("模拟 GET /api/announcements")
    announcements = announcement_manager.get_announcements(status="published")
    api_response = {
        'success': True,
        'announcements': announcements,
        'total': len(announcements)
    }
    print(f"✅ API响应: success={api_response['success']}, total={api_response['total']}")
    
    # 模拟获取未读数量API
    print("\n模拟 GET /api/announcements/unread-count")
    test_username = "test_user"
    unread_count = announcement_manager.get_unread_count(test_username)
    api_response = {
        'success': True,
        'unread_count': unread_count
    }
    print(f"✅ API响应: {api_response}")
    
    # 模拟标记已读API
    if announcements:
        print("\n模拟 POST /api/announcements/{id}/read")
        first_announcement_id = announcements[0]['id']
        success = announcement_manager.mark_as_read(test_username, first_announcement_id)
        api_response = {
            'success': success,
            'message': '标记已读成功' if success else '标记已读失败'
        }
        print(f"✅ API响应: {api_response}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n" + "=" * 50)
    print("清理测试数据")
    print("=" * 50)
    
    try:
        # 获取所有公告
        announcements = announcement_manager.get_announcements(include_drafts=True)
        test_announcements = []
        
        for announcement in announcements:
            # 删除测试创建的公告
            if (announcement['created_by'] == 'test_admin' or 
                '测试' in announcement['title'] or 
                'test' in announcement['title'].lower()):
                test_announcements.append(announcement['id'])
        
        if test_announcements:
            print(f"找到 {len(test_announcements)} 个测试公告")
            for announcement_id in test_announcements:
                result = announcement_manager.delete_announcement(announcement_id)
                if result['success']:
                    print(f"✅ 删除成功: {announcement_id}")
                else:
                    print(f"❌ 删除失败: {announcement_id}")
        else:
            print("没有找到测试公告")
            
    except Exception as e:
        print(f"❌ 清理异常: {e}")

def main():
    """主测试函数"""
    print("🔍 公告系统完整测试")
    print("时间:", json.dumps({"timestamp": "2025-01-14"}, ensure_ascii=False))
    
    # 测试1: 创建公告
    created_announcements = test_create_announcements()
    
    # 测试2: 获取公告列表
    all_announcements = test_get_announcements()
    
    # 测试3: 用户阅读状态
    test_user_read_status()
    
    # 测试4: 更新公告
    test_update_announcement()
    
    # 测试5: 文件结构
    test_file_structure()
    
    # 测试6: API模拟
    test_api_simulation()
    
    # 清理测试数据
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print("🎯 公告系统测试完成")
    print("=" * 50)
    print("如果所有测试都通过，说明公告系统功能正常。")
    print("现在可以启动Flask服务器测试Web界面功能。")

if __name__ == "__main__":
    main()
