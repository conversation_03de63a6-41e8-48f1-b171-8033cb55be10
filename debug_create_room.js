// 调试创建房间功能的JavaScript代码
// 在浏览器控制台中运行这些代码来测试功能

console.log('🔍 开始调试创建房间功能');

// 1. 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    console.log('🔍 认证令牌:', token ? '存在' : '不存在');
    if (token) {
        console.log('🔍 令牌长度:', token.length);
    }
    return token;
}

// 2. 测试API连接
async function testAPIConnection() {
    const token = checkAuth();
    if (!token) {
        console.log('❌ 没有认证令牌');
        return false;
    }

    try {
        const response = await fetch('/api/status', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('🔍 API状态响应:', response.status);
        const data = await response.json();
        console.log('🔍 API状态数据:', data);
        return response.ok;
    } catch (error) {
        console.error('❌ API连接失败:', error);
        return false;
    }
}

// 3. 测试获取设备列表
async function testGetDevices() {
    const token = checkAuth();
    if (!token) return;

    try {
        const response = await fetch('/api/devices', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('🔍 设备列表响应:', response.status);
        const data = await response.json();
        console.log('🔍 设备列表数据:', data);
        
        if (data.success && data.devices) {
            console.log('🔍 可用设备数量:', data.devices.length);
            data.devices.forEach((device, index) => {
                console.log(`🔍 设备${index + 1}:`, device.device_id, device.device_name);
            });
        }
        
        return data;
    } catch (error) {
        console.error('❌ 获取设备列表失败:', error);
        return null;
    }
}

// 4. 测试获取激活码
async function testGetActivationCodes() {
    const token = checkAuth();
    if (!token) return;

    try {
        const response = await fetch('/api/activation-codes', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('🔍 激活码列表响应:', response.status);
        const data = await response.json();
        console.log('🔍 激活码列表数据:', data);
        
        if (data.success && data.codes) {
            const unusedCodes = data.codes.filter(code => code.status === 'unused');
            console.log('🔍 可用激活码数量:', unusedCodes.length);
            if (unusedCodes.length > 0) {
                console.log('🔍 第一个可用激活码:', unusedCodes[0].code);
            }
        }
        
        return data;
    } catch (error) {
        console.error('❌ 获取激活码失败:', error);
        return null;
    }
}

// 5. 测试创建房间
async function testCreateRoom() {
    const token = checkAuth();
    if (!token) {
        console.log('❌ 请先登录');
        return;
    }

    // 获取设备和激活码
    const devicesData = await testGetDevices();
    const codesData = await testGetActivationCodes();
    
    if (!devicesData || !devicesData.success || devicesData.devices.length === 0) {
        console.log('❌ 没有可用设备');
        return;
    }
    
    if (!codesData || !codesData.success) {
        console.log('❌ 无法获取激活码');
        return;
    }
    
    const unusedCodes = codesData.codes.filter(code => code.status === 'unused');
    if (unusedCodes.length === 0) {
        console.log('❌ 没有可用激活码');
        return;
    }

    const testRoomData = {
        live_id: 'TEST_ROOM_' + Date.now(),
        room_name: '测试房间_' + new Date().toLocaleTimeString(),
        target_device_id: devicesData.devices[0].device_id,
        activation_code: unusedCodes[0].code
    };
    
    console.log('🔍 测试房间数据:', testRoomData);

    try {
        const response = await fetch('/api/rooms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(testRoomData)
        });
        
        console.log('🔍 创建房间响应:', response.status);
        const data = await response.json();
        console.log('🔍 创建房间结果:', data);
        
        if (data.success) {
            console.log('✅ 房间创建成功！');
        } else {
            console.log('❌ 房间创建失败:', data.message);
        }
        
        return data;
    } catch (error) {
        console.error('❌ 创建房间请求失败:', error);
        return null;
    }
}

// 6. 完整测试流程
async function runFullTest() {
    console.log('🚀 开始完整测试流程');
    
    console.log('1. 检查认证状态...');
    const hasAuth = checkAuth();
    if (!hasAuth) {
        console.log('❌ 需要先登录');
        return;
    }
    
    console.log('2. 测试API连接...');
    const apiOk = await testAPIConnection();
    if (!apiOk) {
        console.log('❌ API连接失败');
        return;
    }
    
    console.log('3. 测试获取设备列表...');
    await testGetDevices();
    
    console.log('4. 测试获取激活码...');
    await testGetActivationCodes();
    
    console.log('5. 测试创建房间...');
    await testCreateRoom();
    
    console.log('🏁 测试完成');
}

// 7. 简单的登录函数
async function quickLogin() {
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });

        const data = await response.json();
        if (data.success) {
            localStorage.setItem('auth_token', data.token);
            console.log('✅ 登录成功');
            return true;
        } else {
            console.log('❌ 登录失败:', data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ 登录请求失败:', error);
        return false;
    }
}

// 使用说明
console.log(`
🔧 调试工具使用说明:
1. quickLogin() - 快速登录为管理员
2. checkAuth() - 检查认证状态
3. testAPIConnection() - 测试API连接
4. testGetDevices() - 测试获取设备列表
5. testGetActivationCodes() - 测试获取激活码
6. testCreateRoom() - 测试创建房间
7. runFullTest() - 运行完整测试流程

建议执行顺序:
await quickLogin();
await runFullTest();
`);
