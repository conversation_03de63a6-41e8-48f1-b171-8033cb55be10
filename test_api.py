#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本 - 测试自定义回复和增强版防冷场功能
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:15008"
ROOM_ID = "200382305200"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def login(self, username="admin", password="admin123"):
        """登录获取token"""
        print("🔐 正在登录...")
        try:
            response = self.session.post(
                f"{BASE_URL}/api/auth/login",
                json={"username": username, "password": password}
            )
            data = response.json()
            if data.get('success'):
                self.token = data.get('token')
                print(f"✅ 登录成功，token: {self.token[:20]}...")
                return True
            else:
                print(f"❌ 登录失败: {data.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_headers(self):
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
    
    def test_custom_reply_get(self):
        """测试获取自定义回复配置"""
        print("\n📋 测试获取自定义回复配置...")
        try:
            # 先测试用户信息
            user_response = self.session.get(
                f"{BASE_URL}/api/user/info",
                headers=self.get_headers()
            )
            print(f"用户信息: {user_response.json()}")

            response = self.session.get(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/custom-reply",
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_custom_reply_post(self):
        """测试更新自定义回复基础配置"""
        print("\n📝 测试更新自定义回复基础配置...")
        config = {
            "enabled": True,
            "match_mode": "contains",
            "case_sensitive": False,
            "cooldown_seconds": 10,
            "priority_weight": 9
        }
        try:
            response = self.session.post(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/custom-reply",
                json=config,
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_custom_reply_add_rule(self):
        """测试添加自定义回复规则"""
        print("\n➕ 测试添加自定义回复规则...")
        rule = {
            "id": "test_rule",
            "keywords": ["测试", "test"],
            "responses": ["这是测试回复1", "这是测试回复2"],
            "enabled": True
        }
        try:
            response = self.session.post(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/custom-reply/rules",
                json=rule,
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_custom_reply_update_rule(self):
        """测试更新自定义回复规则"""
        print("\n✏️ 测试更新自定义回复规则...")
        rule = {
            "keywords": ["测试更新", "test update"],
            "responses": ["这是更新后的回复"],
            "enabled": True
        }
        try:
            response = self.session.put(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/custom-reply/rules/test_rule",
                json=rule,
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_custom_reply_delete_rule(self):
        """测试删除自定义回复规则"""
        print("\n🗑️ 测试删除自定义回复规则...")
        try:
            response = self.session.delete(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/custom-reply/rules/test_rule",
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_anti_silence_get(self):
        """测试获取防冷场配置"""
        print("\n📋 测试获取防冷场配置...")
        try:
            response = self.session.get(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/anti-silence",
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_anti_silence_add_personalized_template(self):
        """测试添加个性化防冷场模板"""
        print("\n➕ 测试添加个性化防冷场模板...")
        template = {
            "template": "嘿{user_name}，这是测试个性化模板！"
        }
        try:
            response = self.session.post(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/anti-silence/personalized-templates",
                json=template,
                headers=self.get_headers()
            )
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data.get('success', False)
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_anti_silence_delete_personalized_template(self):
        """测试删除个性化防冷场模板"""
        print("\n🗑️ 测试删除个性化防冷场模板...")
        try:
            # 先获取当前配置，找到最后一个模板的索引
            response = self.session.get(
                f"{BASE_URL}/api/rooms/{ROOM_ID}/anti-silence",
                headers=self.get_headers()
            )
            data = response.json()
            if data.get('success'):
                templates = data['config'].get('personalized_templates', [])
                if templates:
                    last_index = len(templates) - 1
                    response = self.session.delete(
                        f"{BASE_URL}/api/rooms/{ROOM_ID}/anti-silence/personalized-templates/{last_index}",
                        headers=self.get_headers()
                    )
                    data = response.json()
                    print(f"状态码: {response.status_code}")
                    print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    return data.get('success', False)
                else:
                    print("没有个性化模板可删除")
                    return True
            return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        
        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 测试结果统计
        results = []
        
        # 自定义回复API测试
        print("\n" + "="*50)
        print("🎯 自定义回复API测试")
        print("="*50)
        
        results.append(("获取自定义回复配置", self.test_custom_reply_get()))
        results.append(("更新自定义回复基础配置", self.test_custom_reply_post()))
        results.append(("添加自定义回复规则", self.test_custom_reply_add_rule()))
        results.append(("更新自定义回复规则", self.test_custom_reply_update_rule()))
        results.append(("删除自定义回复规则", self.test_custom_reply_delete_rule()))
        
        # 防冷场API测试
        print("\n" + "="*50)
        print("🎯 增强版防冷场API测试")
        print("="*50)
        
        results.append(("获取防冷场配置", self.test_anti_silence_get()))
        results.append(("添加个性化防冷场模板", self.test_anti_silence_add_personalized_template()))
        results.append(("删除个性化防冷场模板", self.test_anti_silence_delete_personalized_template()))
        
        # 输出测试结果
        print("\n" + "="*50)
        print("📊 测试结果汇总")
        print("="*50)
        
        passed = 0
        failed = 0
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1
        
        print(f"\n总计: {len(results)} 个测试")
        print(f"通过: {passed} 个")
        print(f"失败: {failed} 个")
        print(f"成功率: {passed/len(results)*100:.1f}%")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
