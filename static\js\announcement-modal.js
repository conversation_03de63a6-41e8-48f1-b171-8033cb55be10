/**
 * 公告模态窗口组件
 * 用于在所有页面显示公告列表和详情
 */

class AnnouncementModal {
    constructor() {
        this.modal = null;
        this.announcements = [];
        this.unreadCount = 0;
        this.currentUser = null;
        this.isInitialized = false;
    }

    // 初始化公告系统
    async init() {
        if (this.isInitialized) return;
        
        try {
            // 获取当前用户信息
            await this.loadCurrentUser();
            
            // 创建模态窗口
            this.createModal();
            
            // 加载公告数据
            await this.loadAnnouncements();

            // 加载未读公告列表
            await this.loadUnreadAnnouncements();

            // 更新未读数量
            await this.updateUnreadCount();
            
            this.isInitialized = true;
            console.log('✅ 公告系统初始化完成');
        } catch (error) {
            console.error('❌ 公告系统初始化失败:', error);
        }
    }

    // 获取当前用户信息
    async loadCurrentUser() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch('/api/auth/verify', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.currentUser = data.user;
                }
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }

    // 创建模态窗口HTML
    createModal() {
        const modalHTML = `
            <div id="announcementModal" class="announcement-modal" style="display: none;">
                <div class="announcement-modal-overlay" onclick="announcementModal.close()"></div>
                <div class="announcement-modal-content">
                    <div class="announcement-modal-header">
                        <h3>
                            <i class="bi bi-megaphone-fill"></i>
                            系统公告
                        </h3>
                        <button class="announcement-modal-close" onclick="announcementModal.close()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="announcement-modal-body">
                        <div id="announcementList" class="announcement-list">
                            <div class="loading-state">
                                <i class="bi bi-hourglass-split"></i>
                                <p>正在加载公告...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('announcementModal');

        // 添加样式
        this.addStyles();
    }

    // 添加模态窗口样式
    addStyles() {
        if (document.getElementById('announcement-modal-styles')) return;

        const styles = `
            <style id="announcement-modal-styles">
                .announcement-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .announcement-modal-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(4px);
                }

                .announcement-modal-content {
                    position: relative;
                    background: var(--color-canvas-default, #ffffff);
                    border: 1px solid var(--color-border-default, #d0d7de);
                    border-radius: 12px;
                    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    display: flex;
                    flex-direction: column;
                    animation: modalSlideIn 0.3s ease-out;
                }

                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: scale(0.9) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                    }
                }

                .announcement-modal-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 20px 24px;
                    border-bottom: 1px solid var(--color-border-default, #d0d7de);
                }

                .announcement-modal-header h3 {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: var(--color-fg-default, #24292f);
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .announcement-modal-close {
                    background: none;
                    border: none;
                    font-size: 1.5rem;
                    cursor: pointer;
                    color: var(--color-fg-muted, #656d76);
                    padding: 4px;
                    border-radius: 6px;
                    transition: all 0.2s;
                }

                .announcement-modal-close:hover {
                    background: var(--color-canvas-subtle, #f6f8fa);
                    color: var(--color-fg-default, #24292f);
                }

                .announcement-modal-body {
                    flex: 1;
                    overflow-y: auto;
                    padding: 0;
                }

                .announcement-list {
                    max-height: 60vh;
                    overflow-y: auto;
                }

                .announcement-item {
                    padding: 20px 24px;
                    border-bottom: 1px solid var(--color-border-muted, #d8dee4);
                }

                .announcement-item:last-child {
                    border-bottom: none;
                }

                .announcement-item.unread {
                    background: var(--color-accent-subtle, #ddf4ff);
                    border-left: 4px solid var(--color-accent-emphasis, #0969da);
                }

                .announcement-title {
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: var(--color-fg-default, #24292f);
                    margin: 0 0 8px 0;
                    line-height: 1.4;
                }

                .announcement-content {
                    color: var(--color-fg-muted, #656d76);
                    line-height: 1.6;
                    margin-bottom: 12px;
                    white-space: pre-wrap;
                }

                .announcement-meta {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 0.875rem;
                    color: var(--color-fg-muted, #656d76);
                }

                .announcement-date {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .announcement-actions {
                    display: flex;
                    gap: 8px;
                }

                .mark-read-btn {
                    background: var(--color-btn-bg, #f6f8fa);
                    border: 1px solid var(--color-btn-border, #d0d7de);
                    color: var(--color-btn-text, #24292f);
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 0.75rem;
                    cursor: pointer;
                    transition: all 0.2s;
                }

                .mark-read-btn:hover {
                    background: var(--color-btn-hover-bg, #f3f4f6);
                    border-color: var(--color-btn-hover-border, #d0d7de);
                }

                .loading-state, .empty-state {
                    text-align: center;
                    padding: 40px 20px;
                    color: var(--color-fg-muted, #656d76);
                }

                .loading-state i, .empty-state i {
                    font-size: 2rem;
                    margin-bottom: 12px;
                    opacity: 0.6;
                }

                .announcement-badge {
                    position: relative;
                    display: inline-block;
                }

                .announcement-badge .badge {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    background: var(--color-danger-emphasis, #cf222e);
                    color: white;
                    border-radius: 10px;
                    padding: 2px 6px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    min-width: 18px;
                    text-align: center;
                    line-height: 1.2;
                    animation: badgePulse 2s infinite;
                }

                @keyframes badgePulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }

                .announcement-badge .badge.hidden {
                    display: none;
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // 加载公告列表
    async loadAnnouncements() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch('/api/announcements?status=published', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.announcements = data.announcements;
                    this.renderAnnouncements();
                }
            }
        } catch (error) {
            console.error('加载公告失败:', error);
        }
    }

    // 渲染公告列表
    renderAnnouncements() {
        const container = document.getElementById('announcementList');
        if (!container) return;

        if (this.announcements.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-megaphone"></i>
                    <p>暂无公告</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.announcements.map(announcement => {
            const isUnread = this.isAnnouncementUnread(announcement.id);
            return `
                <div class="announcement-item ${isUnread ? 'unread' : ''}" data-id="${announcement.id}">
                    <h4 class="announcement-title">${this.escapeHtml(announcement.title)}</h4>
                    <div class="announcement-content">${this.escapeHtml(announcement.content)}</div>
                    <div class="announcement-meta">
                        <div class="announcement-date">
                            <i class="bi bi-calendar3"></i>
                            ${this.formatDate(announcement.published_at || announcement.created_at)}
                        </div>
                        <div class="announcement-actions">
                            ${isUnread ? `
                                <button class="mark-read-btn" onclick="announcementModal.markAsRead('${announcement.id}')">
                                    标记已读
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 检查公告是否未读
    isAnnouncementUnread(announcementId) {
        // 从未读公告列表中检查
        return this.unreadAnnouncementIds && this.unreadAnnouncementIds.includes(announcementId);
    }

    // 加载未读公告列表
    async loadUnreadAnnouncements() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch('/api/announcements/unread', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.unreadAnnouncementIds = data.announcements.map(a => a.id);
                }
            }
        } catch (error) {
            console.error('加载未读公告失败:', error);
        }
    }

    // 更新未读数量
    async updateUnreadCount() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch('/api/announcements/unread-count', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.unreadCount = data.unread_count;
                    this.updateBadge();
                }
            }
        } catch (error) {
            console.error('获取未读数量失败:', error);
        }
    }

    // 更新徽章显示
    updateBadge() {
        const badges = document.querySelectorAll('.announcement-badge .badge');
        badges.forEach(badge => {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        });
    }

    // 标记公告为已读
    async markAsRead(announcementId) {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch(`/api/announcements/${announcementId}/read`, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // 更新UI
                    const announcementElement = document.querySelector(`[data-id="${announcementId}"]`);
                    if (announcementElement) {
                        announcementElement.classList.remove('unread');
                        const markReadBtn = announcementElement.querySelector('.mark-read-btn');
                        if (markReadBtn) {
                            markReadBtn.remove();
                        }
                    }
                    
                    // 更新未读数量
                    await this.updateUnreadCount();
                }
            }
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    }

    // 显示模态窗口
    async show() {
        if (this.modal) {
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // 重新加载数据
            await this.loadAnnouncements();
            await this.loadUnreadAnnouncements();
            this.renderAnnouncements();
        }
    }

    // 关闭模态窗口
    close() {
        if (this.modal) {
            this.modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }

    // 工具函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleString('zh-CN');
    }
}

// 创建全局实例
window.announcementModal = new AnnouncementModal();
