<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化直播系统 - GitHub风格演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="static/css/github-style.css" rel="stylesheet">
</head>
<body>
    <!-- GitHub风格导航栏 -->
    <nav class="github-navbar">
        <div class="github-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <a class="navbar-brand-github" href="#">
                    <i class="bi bi-grid-3x3-gap-fill"></i>
                    <span>自动化直播系统</span>
                </a>
                <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                    <div style="font-family: var(--fontStack-monospace); font-size: 12px; color: var(--color-fg-muted); background-color: var(--color-canvas-subtle); padding: 4px 8px; border-radius: var(--borderRadius-small); border: 1px solid var(--color-border-default);">
                        <i class="bi bi-clock"></i>
                        <span id="currentTime"></span>
                    </div>
                    <button class="btn-github btn-secondary" style="padding: 4px 8px; font-size: 12px;">
                        <i class="bi bi-gear"></i>
                        设置
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 统计面板 -->
    <div class="github-container" style="margin-top: var(--base-size-24);">
        <div class="github-grid github-grid-4">
            <div class="github-stat-card">
                <div class="github-stat-number">12</div>
                <div class="github-stat-label">
                    <i class="bi bi-house-door-fill"></i>
                    直播间总数
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number">8</div>
                <div class="github-stat-label">
                    <i class="bi bi-play-circle-fill"></i>
                    活跃直播间
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number">24</div>
                <div class="github-stat-label">
                    <i class="bi bi-cpu-fill"></i>
                    设备总数
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number">20</div>
                <div class="github-stat-label">
                    <i class="bi bi-wifi"></i>
                    在线设备
                </div>
            </div>
        </div>
    </div>

    <!-- 主布局 -->
    <div class="github-container">
        <div class="github-layout">
            <!-- 侧边栏 -->
            <div class="github-sidebar">
                <div class="github-sidebar-section">
                    <div class="github-sidebar-title">
                        <i class="bi bi-gear-fill"></i>
                        快速操作
                    </div>
                    <button class="btn-github btn-secondary" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新状态
                    </button>
                    <button class="btn-github btn-secondary" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-journal-text"></i>
                        系统日志
                    </button>
                    <button class="btn-github btn-secondary" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-download"></i>
                        导出配置
                    </button>
                    <button class="btn-github btn-secondary" style="width: 100%;">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        紧急停止
                    </button>
                </div>
                
                <div class="github-sidebar-section">
                    <div class="github-sidebar-title">
                        <i class="bi bi-activity"></i>
                        系统状态
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">WebSocket</span>
                        <span class="github-label label-success">在线</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">数据库</span>
                        <span class="github-label label-success">正常</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">AI服务</span>
                        <span class="github-label label-success">运行中</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">系统</span>
                        <span class="github-label label-success">正常</span>
                    </div>
                </div>
                
                <div class="github-sidebar-section">
                    <div class="github-sidebar-title">
                        <i class="bi bi-link-45deg"></i>
                        导航
                    </div>
                    <a href="#" class="github-nav-link active">
                        <i class="bi bi-house-fill"></i>
                        <span>仪表板</span>
                    </a>
                    <a href="#" class="github-nav-link">
                        <i class="bi bi-people-fill"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="github-nav-link">
                        <i class="bi bi-trophy-fill"></i>
                        <span>排行榜</span>
                    </a>
                    <a href="#" class="github-nav-link">
                        <i class="bi bi-key-fill"></i>
                        <span>激活码</span>
                    </a>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div style="display: flex; flex-direction: column; gap: var(--base-size-16);">
                <!-- 直播间管理 -->
                <div class="github-card">
                    <div class="github-card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="github-card-title">
                                <i class="bi bi-house-door-fill"></i>
                                直播间管理
                            </div>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <button class="btn-github btn-primary">
                                    <i class="bi bi-plus-circle-fill"></i>
                                    添加直播间
                                </button>
                                <button class="btn-github btn-secondary">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="github-card-body">
                        <table class="github-table">
                            <thead>
                                <tr>
                                    <th>直播间ID</th>
                                    <th>状态</th>
                                    <th>观众数</th>
                                    <th>最后活动</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>ROOM_001</strong></td>
                                    <td><span class="github-label label-success">在线</span></td>
                                    <td>1,234</td>
                                    <td>2分钟前</td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">管理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>ROOM_002</strong></td>
                                    <td><span class="github-label label-warning">待机</span></td>
                                    <td>567</td>
                                    <td>10分钟前</td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">管理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>ROOM_003</strong></td>
                                    <td><span class="github-label label-success">在线</span></td>
                                    <td>892</td>
                                    <td>刚刚</td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">管理</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 设备管理 -->
                <div class="github-card">
                    <div class="github-card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="github-card-title">
                                <i class="bi bi-cpu-fill"></i>
                                设备管理
                            </div>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <button class="btn-github btn-primary">
                                    <i class="bi bi-plus-circle-fill"></i>
                                    添加设备
                                </button>
                                <button class="btn-github btn-secondary">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    刷新
                                </button>
                                <button class="btn-github btn-secondary">
                                    <i class="bi bi-search"></i>
                                    扫描
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="github-card-body">
                        <table class="github-table">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>IP地址</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>DEV_001</strong></td>
                                    <td>主控制器</td>
                                    <td><span class="github-label label-success">在线</span></td>
                                    <td><code>*************</code></td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">监控</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>DEV_002</strong></td>
                                    <td>传感器</td>
                                    <td><span class="github-label label-success">在线</span></td>
                                    <td><code>192.168.1.101</code></td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">监控</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>DEV_003</strong></td>
                                    <td>摄像头</td>
                                    <td><span class="github-label label-warning">离线</span></td>
                                    <td><code>192.168.1.102</code></td>
                                    <td>
                                        <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;">监控</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 演示区域 -->
                <div class="github-card">
                    <div class="github-card-header">
                        <div class="github-card-title">
                            <i class="bi bi-star-fill"></i>
                            GitHub风格演示
                        </div>
                    </div>
                    <div class="github-card-body">
                        <p style="color: var(--color-fg-muted); margin-bottom: var(--base-size-16);">
                            这是一个采用GitHub设计语言的专业界面，具有清晰的层次结构、一致的交互模式和优秀的可读性。
                        </p>
                        
                        <div style="display: flex; flex-wrap: wrap; gap: var(--base-size-8); margin-bottom: var(--base-size-16);">
                            <button class="btn-github btn-primary">
                                <i class="bi bi-check-circle-fill"></i>
                                主要操作
                            </button>
                            <button class="btn-github btn-secondary">
                                <i class="bi bi-gear"></i>
                                次要操作
                            </button>
                            <span class="github-label label-success">成功状态</span>
                            <span class="github-label label-warning">警告状态</span>
                            <span class="github-label label-danger">错误状态</span>
                        </div>
                        
                        <div style="background-color: var(--color-canvas-subtle); padding: var(--base-size-16); border-radius: var(--borderRadius-medium); border: 1px solid var(--color-border-default);">
                            <h4 style="margin: 0 0 var(--base-size-8) 0; font-size: 16px; font-weight: 600;">特性亮点</h4>
                            <ul style="margin: 0; padding-left: var(--base-size-20); color: var(--color-fg-muted);">
                                <li>GitHub原生设计语言</li>
                                <li>响应式布局和组件</li>
                                <li>一致的颜色和间距系统</li>
                                <li>优秀的可访问性支持</li>
                                <li>专业的企业级外观</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
