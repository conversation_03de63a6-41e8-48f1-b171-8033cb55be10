/* GitHub风格设计系统 */

:root {
  /* GitHub色彩系统 */
  --color-canvas-default: #ffffff;
  --color-canvas-subtle: #f6f8fa;
  --color-canvas-inset: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: #d8dee4;
  --color-neutral-muted: rgba(175,184,193,0.2);
  
  /* 文字颜色 */
  --color-fg-default: #1f2328;
  --color-fg-muted: #656d76;
  --color-fg-subtle: #6e7781;
  --color-fg-on-emphasis: #ffffff;
  
  /* 品牌色 */
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-accent-muted: rgba(84,174,255,0.4);
  --color-accent-subtle: #ddf4ff;
  
  /* 状态色 */
  --color-success-fg: #1a7f37;
  --color-success-emphasis: #1f883d;
  --color-success-muted: rgba(74,194,107,0.4);
  --color-success-subtle: #dafbe1;
  
  --color-attention-fg: #9a6700;
  --color-attention-emphasis: #bf8700;
  --color-attention-muted: rgba(212,167,44,0.4);
  --color-attention-subtle: #fff8c5;
  
  --color-danger-fg: #d1242f;
  --color-danger-emphasis: #cf222e;
  --color-danger-muted: rgba(255,129,130,0.4);
  --color-danger-subtle: #ffebe9;
  
  /* 阴影 */
  --color-shadow-small: rgba(31,35,40,0.04);
  --color-shadow-medium: rgba(31,35,40,0.15);
  --color-shadow-large: rgba(31,35,40,0.12);
  --color-shadow-extra-large: rgba(31,35,40,0.20);
  
  /* 字体 */
  --fontStack-monospace: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --fontStack-system: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  
  /* 间距 */
  --base-size-4: 0.25rem;
  --base-size-8: 0.5rem;
  --base-size-12: 0.75rem;
  --base-size-16: 1rem;
  --base-size-20: 1.25rem;
  --base-size-24: 1.5rem;
  --base-size-32: 2rem;
  --base-size-40: 2.5rem;
  --base-size-48: 3rem;
  
  /* 圆角 */
  --borderRadius-small: 3px;
  --borderRadius-medium: 6px;
  --borderRadius-large: 8px;
  --borderRadius-full: 50%;
}

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--fontStack-system);
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-fg-default);
  background-color: var(--color-canvas-default);
  margin: 0;
  padding: 0;
}

/* GitHub导航栏 */
.github-navbar {
  background-color: var(--color-canvas-default);
  border-bottom: 1px solid var(--color-border-default);
  padding: var(--base-size-16) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 0 var(--color-neutral-muted);
}

.navbar-brand-github {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-fg-default);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--base-size-8);
}

.navbar-brand-github:hover {
  color: var(--color-accent-fg);
}

.navbar-brand-github .octicon {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

/* GitHub按钮系统 */
.btn-github {
  position: relative;
  display: inline-block;
  padding: 5px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid;
  border-radius: var(--borderRadius-medium);
  appearance: none;
  text-decoration: none;
  text-align: center;
  transition: 80ms cubic-bezier(0.33, 1, 0.68, 1);
  transition-property: color, background-color, box-shadow, border-color;
}

.btn-github:focus {
  outline: 2px solid var(--color-accent-fg);
  outline-offset: -2px;
  box-shadow: none;
}

.btn-github:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 主要按钮 */
.btn-primary {
  color: var(--color-fg-on-emphasis);
  background-color: var(--color-accent-emphasis);
  border-color: rgba(31,35,40,0.15);
  box-shadow: 0 1px 0 rgba(31,35,40,0.1), inset 0 1px 0 rgba(255,255,255,0.03);
}

.btn-primary:hover {
  background-color: #0860ca;
  border-color: rgba(31,35,40,0.15);
}

/* 次要按钮 */
.btn-secondary {
  color: var(--color-fg-default);
  background-color: var(--color-canvas-default);
  border-color: var(--color-border-default);
  box-shadow: 0 1px 0 rgba(31,35,40,0.04), inset 0 1px 0 rgba(255,255,255,0.25);
}

.btn-secondary:hover {
  background-color: var(--color-canvas-subtle);
  border-color: var(--color-border-default);
}

/* GitHub卡片 */
.github-card {
  background-color: var(--color-canvas-default);
  border: 1px solid var(--color-border-default);
  border-radius: var(--borderRadius-medium);
  margin-bottom: var(--base-size-16);
}

.github-card-header {
  padding: var(--base-size-16);
  background-color: var(--color-canvas-subtle);
  border-bottom: 1px solid var(--color-border-default);
  border-radius: var(--borderRadius-medium) var(--borderRadius-medium) 0 0;
}

.github-card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-fg-default);
  display: flex;
  align-items: center;
  gap: var(--base-size-8);
}

.github-card-body {
  padding: var(--base-size-16);
}

/* GitHub表格 */
.github-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
}

.github-table th {
  padding: 8px 16px;
  font-weight: 600;
  text-align: left;
  background-color: var(--color-canvas-subtle);
  border: 1px solid var(--color-border-default);
  border-bottom: 1px solid var(--color-border-default);
}

.github-table td {
  padding: 8px 16px;
  border: 1px solid var(--color-border-default);
  border-top: 0;
}

.github-table tbody tr:hover {
  background-color: var(--color-canvas-subtle);
}

/* GitHub标签 */
.github-label {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  border-radius: 2em;
  text-decoration: none;
  white-space: nowrap;
}

.label-success {
  color: var(--color-success-fg);
  background-color: var(--color-success-subtle);
  border: 1px solid var(--color-success-muted);
}

.label-warning {
  color: var(--color-attention-fg);
  background-color: var(--color-attention-subtle);
  border: 1px solid var(--color-attention-muted);
}

.label-danger {
  color: var(--color-danger-fg);
  background-color: var(--color-danger-subtle);
  border: 1px solid var(--color-danger-muted);
}

/* GitHub统计卡片 */
.github-stat-card {
  background-color: var(--color-canvas-default);
  border: 1px solid var(--color-border-default);
  border-radius: var(--borderRadius-medium);
  padding: var(--base-size-16);
  text-align: center;
  transition: border-color 0.2s ease;
}

.github-stat-card:hover {
  border-color: var(--color-accent-emphasis);
}

.github-stat-number {
  font-size: 32px;
  font-weight: 600;
  color: var(--color-fg-default);
  margin-bottom: var(--base-size-4);
  font-family: var(--fontStack-monospace);
}

.github-stat-label {
  font-size: 14px;
  color: var(--color-fg-muted);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--base-size-4);
}

/* GitHub侧边栏 */
.github-sidebar {
  background-color: var(--color-canvas-default);
  border: 1px solid var(--color-border-default);
  border-radius: var(--borderRadius-medium);
  padding: var(--base-size-16);
}

.github-sidebar-section {
  margin-bottom: var(--base-size-24);
}

.github-sidebar-section:last-child {
  margin-bottom: 0;
}

.github-sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-fg-default);
  margin-bottom: var(--base-size-8);
  padding-bottom: var(--base-size-8);
  border-bottom: 1px solid var(--color-border-default);
}

.github-nav-link {
  display: flex;
  align-items: center;
  gap: var(--base-size-8);
  padding: var(--base-size-8);
  color: var(--color-fg-default);
  text-decoration: none;
  border-radius: var(--borderRadius-medium);
  font-weight: 500;
  margin-bottom: var(--base-size-4);
  transition: background-color 0.2s ease;
}

.github-nav-link:hover {
  background-color: var(--color-canvas-subtle);
  text-decoration: none;
  color: var(--color-fg-default);
}

.github-nav-link.active {
  background-color: var(--color-accent-subtle);
  color: var(--color-accent-fg);
  font-weight: 600;
}

/* 状态指示器 */
.github-status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--base-size-8);
  margin-bottom: var(--base-size-4);
  background-color: var(--color-canvas-subtle);
  border-radius: var(--borderRadius-small);
}

.github-status-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-fg-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 响应式网格 */
.github-grid {
  display: grid;
  gap: var(--base-size-16);
}

.github-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.github-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.github-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 容器 */
.github-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--base-size-16);
}

.github-layout {
  display: grid;
  grid-template-columns: 296px 1fr;
  gap: var(--base-size-24);
  margin-top: var(--base-size-24);
}

/* 响应式设计 */
@media (max-width: 1012px) {
  .github-layout {
    grid-template-columns: 1fr;
    gap: var(--base-size-16);
  }
  
  .github-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .github-container {
    padding: 0 var(--base-size-12);
  }
  
  .github-grid-2,
  .github-grid-3,
  .github-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .github-card-header,
  .github-card-body {
    padding: var(--base-size-12);
  }
}
