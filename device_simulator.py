#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ESP32设备模拟器 - 用于本地测试WebSocket连接和语音功能
"""

import asyncio
import websockets
import json
import time

class DeviceSimulator:
    def __init__(self, device_id="30:ed:a0:29:f0:a4", device_name="硅灵造物直播设备"):
        self.device_id = device_id
        self.device_name = device_name
        self.websocket = None
        self.running = False
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 设备 {self.device_id} 连接到WebSocket服务器...")
            uri = 'ws://localhost:15000'
            self.websocket = await websockets.connect(uri)
            print(f"✅ 设备 {self.device_id} 连接成功")
            
            # 注册设备
            await self.register_device()
            
            # 开始监听消息
            self.running = True
            await self.listen_messages()
            
        except Exception as e:
            print(f"❌ 设备 {self.device_id} 连接失败: {e}")
    
    async def register_device(self):
        """注册设备"""
        register_message = {
            'type': 'device_register',
            'data': {
                'device_id': self.device_id,
                'device_name': self.device_name,
                'device_type': 'esp32',
                'capabilities': 'voice,display'
            }
        }
        
        print(f"📤 设备 {self.device_id} 发送注册消息...")
        await self.websocket.send(json.dumps(register_message))
        
        # 等待注册响应
        try:
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            if response_data.get('data', {}).get('success'):
                print(f"✅ 设备 {self.device_id} 注册成功")
            else:
                print(f"❌ 设备 {self.device_id} 注册失败: {response}")
        except asyncio.TimeoutError:
            print(f"⏰ 设备 {self.device_id} 注册响应超时")
    
    async def listen_messages(self):
        """监听WebSocket消息"""
        print(f"👂 设备 {self.device_id} 开始监听消息...")
        
        try:
            while self.running:
                try:
                    # 等待消息
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    await self.handle_message(message)
                except asyncio.TimeoutError:
                    # 发送心跳
                    await self.send_heartbeat()
                except websockets.exceptions.ConnectionClosed:
                    print(f"🔌 设备 {self.device_id} 连接已断开")
                    break
                    
        except Exception as e:
            print(f"❌ 设备 {self.device_id} 监听消息异常: {e}")
    
    async def handle_message(self, message):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            msg_data = data.get('data', {})
            
            if msg_type == 'speak_message':
                # 处理语音消息
                text = msg_data.get('message', '')
                print(f"🎤 设备 {self.device_id} 收到语音消息: {text}")
                
                # 模拟语音播放
                await self.simulate_speech(text)
                
            elif msg_type == 'device_status':
                # 处理状态查询
                await self.send_status()
                
            else:
                print(f"📥 设备 {self.device_id} 收到消息: {msg_type}")
                
        except Exception as e:
            print(f"❌ 设备 {self.device_id} 处理消息异常: {e}")
    
    async def simulate_speech(self, text):
        """模拟语音播放"""
        print(f"🔊 设备 {self.device_id} 开始播放: {text}")
        
        # 发送状态更新 - 开始说话
        await self.send_status_update('speaking')
        
        # 模拟播放时间（根据文字长度）
        play_time = max(2, len(text) * 0.1)
        await asyncio.sleep(play_time)
        
        # 发送状态更新 - 播放完成
        await self.send_status_update('idle')
        print(f"✅ 设备 {self.device_id} 播放完成")
    
    async def send_status_update(self, status):
        """发送状态更新"""
        status_message = {
            'type': 'device_status',
            'data': {
                'device_id': self.device_id,
                'device_state': status,
                'timestamp': time.time()
            }
        }
        
        if self.websocket:
            await self.websocket.send(json.dumps(status_message))
    
    async def send_heartbeat(self):
        """发送心跳"""
        heartbeat_message = {
            'type': 'heartbeat',
            'data': {
                'device_id': self.device_id,
                'timestamp': time.time()
            }
        }
        
        if self.websocket:
            await self.websocket.send(json.dumps(heartbeat_message))
    
    async def send_status(self):
        """发送设备状态"""
        await self.send_status_update('idle')

async def main():
    """主函数"""
    print("🚀 启动ESP32设备模拟器")
    print("="*50)
    
    # 创建设备模拟器
    device = DeviceSimulator("30:ed:a0:29:f0:a4", "硅灵造物直播设备")
    
    try:
        await device.connect()
    except KeyboardInterrupt:
        print(f"\n⏹️ 设备 {device.device_id} 模拟器已停止")
    except Exception as e:
        print(f"❌ 设备模拟器异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
