{"esp32_001": {"device_id": "esp32_001", "device_name": "演示设备001", "device_type": "esp32", "ip_address": "*************", "status": "offline", "last_seen": "2025-08-14T01:00:00.000000", "auto_registered": false, "created_at": "2025-08-14T01:00:00.000000", "description": "ESP32开发板 - 演示用设备"}, "esp32_002": {"device_id": "esp32_002", "device_name": "演示设备002", "device_type": "esp32", "ip_address": "*************", "status": "offline", "last_seen": "2025-08-14T01:00:00.000000", "auto_registered": false, "created_at": "2025-08-14T01:00:00.000000", "description": "ESP32开发板 - 演示用设备"}, "esp32_003": {"device_id": "esp32_003", "device_name": "测试设备003", "device_type": "esp32", "ip_address": "*************", "status": "offline", "last_seen": "2025-08-14T01:00:00.000000", "auto_registered": false, "created_at": "2025-08-14T01:00:00.000000", "description": "ESP32开发板 - 测试环境"}, "30:ed:a0:29:f0:a4": {"device_id": "30:ed:a0:29:f0:a4", "device_name": "硅灵造物直播设备", "device_type": "esp32", "capabilities": "voice,display", "created_at": "2025-08-14T21:25:24.702508", "last_seen": "2025-08-14T21:25:24.702509", "enabled": true, "auto_registered": true}}