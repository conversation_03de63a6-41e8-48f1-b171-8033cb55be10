<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业控制中心 - 专业风格演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 专业暗蓝色系统 */
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #1e40af;
            --accent-blue: #2563eb;
            --light-blue: #3b82f6;
            --dark-blue: #1e293b;
            --steel-blue: #334155;
            --slate-blue: #475569;
            --space-black: #0f172a;
            --space-dark: #1e293b;
            --space-medium: #334155;
            --space-light: #475569;
            --panel-dark: #1e293b;
            --panel-medium: #334155;
            --text-primary: #e2e8f0;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;
            --text-accent: #60a5fa;
            --status-success: #059669;
            --status-warning: #d97706;
            --status-error: #dc2626;
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
            --border-secondary: 1px solid #334155;
            --font-tech: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-tech);
            background: linear-gradient(135deg, var(--space-black) 0%, var(--space-dark) 50%, var(--space-medium) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 星空背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: stars 20s linear infinite;
            z-index: -1;
            opacity: 0.3;
        }

        @keyframes stars {
            from { transform: translateY(0px); }
            to { transform: translateY(-100px); }
        }

        /* 导航栏 */
        .spaceship-navbar {
            background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
            border-bottom: 2px solid var(--neon-cyan);
            box-shadow: var(--border-glow-cyan);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand-spaceship {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--neon-cyan);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: var(--glow-cyan);
        }

        .navbar-brand-spaceship i {
            font-size: 2rem;
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from { 
                text-shadow: var(--glow-cyan);
                transform: scale(1);
            }
            to { 
                text-shadow: 0 0 30px #00ffff, 0 0 60px #00ffff, 0 0 90px #00ffff;
                transform: scale(1.05);
            }
        }

        /* 统计面板 */
        .stats-console {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .stats-grid-spaceship {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-panel {
            background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
            border: 2px solid var(--neon-blue);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px var(--neon-blue);
        }

        .stat-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
            animation: scan-line 3s linear infinite;
        }

        @keyframes scan-line {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .stat-panel:hover {
            transform: translateY(-5px) scale(1.02);
            border-color: var(--neon-cyan);
            box-shadow: var(--border-glow-cyan);
        }

        .stat-number-spaceship {
            font-family: 'Courier New', monospace;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--neon-green);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px var(--neon-green);
            animation: number-flicker 4s ease-in-out infinite;
        }

        @keyframes number-flicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .stat-label-spaceship {
            color: var(--neon-cyan);
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-label-spaceship i {
            color: var(--neon-blue);
            font-size: 1.2rem;
            text-shadow: 0 0 10px var(--neon-blue);
        }

        /* 按钮 */
        .btn-spaceship {
            background: linear-gradient(135deg, var(--space-dark) 0%, var(--space-medium) 100%);
            color: var(--neon-cyan);
            border: 2px solid var(--neon-blue);
            border-radius: 6px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            margin: 0.5rem;
        }

        .btn-spaceship:hover {
            transform: translateY(-2px);
            border-color: var(--neon-cyan);
            box-shadow: var(--border-glow-cyan);
            color: var(--neon-green);
            text-shadow: 0 0 10px var(--neon-green);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .demo-section {
            background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
            border: 2px solid var(--neon-blue);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 0 20px rgba(0, 128, 255, 0.3);
        }

        .demo-title {
            color: var(--neon-cyan);
            font-size: 1.5rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
            text-shadow: var(--glow-cyan);
        }

        .glitch-text {
            animation: glitch 2s infinite;
        }

        @keyframes glitch {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-1px, 1px); }
            40% { transform: translate(-1px, -1px); }
            60% { transform: translate(1px, 1px); }
            80% { transform: translate(1px, -1px); }
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .stats-grid-spaceship {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .container {
                padding: 0 1rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid-spaceship {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="spaceship-navbar">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <a class="navbar-brand-spaceship" href="#">
                    <i class="bi bi-grid-3x3-gap-fill"></i>
                    <span>企业控制中心</span>
                </a>
                <div style="color: var(--text-accent); font-family: 'Courier New', monospace;">
                    <i class="bi bi-clock"></i>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 统计面板 -->
    <div class="stats-console">
        <div class="container">
            <div class="stats-grid-spaceship">
                <div class="stat-panel">
                    <div class="stat-number-spaceship">12</div>
                    <div class="stat-label-spaceship">
                        <i class="bi bi-house-door-fill"></i>
                        直播间总数
                    </div>
                </div>
                <div class="stat-panel">
                    <div class="stat-number-spaceship">8</div>
                    <div class="stat-label-spaceship">
                        <i class="bi bi-play-circle-fill"></i>
                        活跃直播间
                    </div>
                </div>
                <div class="stat-panel">
                    <div class="stat-number-spaceship">24</div>
                    <div class="stat-label-spaceship">
                        <i class="bi bi-cpu-fill"></i>
                        设备总数
                    </div>
                </div>
                <div class="stat-panel">
                    <div class="stat-number-spaceship">20</div>
                    <div class="stat-label-spaceship">
                        <i class="bi bi-wifi"></i>
                        在线设备
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 演示区域 -->
    <div class="container">
        <div class="demo-section">
            <h2 class="demo-title">企业级控制台演示</h2>
            <p style="color: var(--text-secondary); margin-bottom: 2rem;">
                这是一个专业的企业级控制台界面，采用暗蓝色调设计，提供清晰的数据展示和直观的操作体验。
            </p>

            <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
                <button class="btn-spaceship" onclick="playBeep()">
                    <i class="bi bi-volume-up"></i>
                    播放音效
                </button>
                <button class="btn-spaceship" onclick="toggleHighlight()">
                    <i class="bi bi-lightbulb"></i>
                    切换高亮
                </button>
                <button class="btn-spaceship" onclick="scanSystem()">
                    <i class="bi bi-search"></i>
                    系统扫描
                </button>
                <button class="btn-spaceship" onclick="alert('数据传输启动！')">
                    <i class="bi bi-arrow-up-circle"></i>
                    数据传输
                </button>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }
        
        // 音效
        function playBeep() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
        
        // 切换高亮效果
        function toggleHighlight() {
            document.querySelectorAll('.stat-panel').forEach(panel => {
                panel.style.borderColor = panel.style.borderColor === 'rgb(59, 130, 246)' ?
                    '#334155' : '#3b82f6';
            });
            playBeep();
        }

        // 系统扫描
        function scanSystem() {
            alert('正在执行系统扫描...\n\n✅ 核心处理器: 正常\n✅ 安全系统: 激活\n✅ 网络连接: 在线\n✅ 数据库: 就绪');
            playBeep();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            // 添加按钮音效
            document.querySelectorAll('.btn-spaceship').forEach(btn => {
                btn.addEventListener('click', playBeep);
            });
        });
    </script>
</body>
</html>
