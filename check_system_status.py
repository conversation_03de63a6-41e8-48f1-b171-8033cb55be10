#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查工具
用于查看AI机器人自动化直播控制台的当前状态
"""

import json
import os
from datetime import datetime
from pathlib import Path

def check_device_configs():
    """检查设备配置状态"""
    devices_file = 'config/devices/devices.json'
    
    if os.path.exists(devices_file):
        with open(devices_file, 'r', encoding='utf-8') as f:
            devices = json.load(f)
        
        print(f"📱 设备配置状态:")
        print(f"   - 已注册设备: {len(devices)} 个")
        
        if devices:
            print("   - 设备列表:")
            for device_id, info in devices.items():
                device_name = info.get('device_name', '未知')
                last_seen = info.get('last_seen', '未知')
                print(f"     * {device_id}: {device_name} (最后连接: {last_seen})")
        else:
            print("   - 当前无已注册设备")
    else:
        print("❌ 设备配置文件不存在")

def check_runtime_data():
    """检查运行时数据状态"""
    runtime_file = 'runtime_data.json'
    
    if os.path.exists(runtime_file):
        try:
            with open(runtime_file, 'r', encoding='utf-8') as f:
                runtime_data = json.load(f)
            
            connected_devices = runtime_data.get('connected_devices', {})
            device_configs = runtime_data.get('device_configs', {})
            
            print(f"🔄 运行时状态:")
            print(f"   - 在线设备: {len(connected_devices)} 个")
            print(f"   - 配置缓存: {len(device_configs)} 个")
            
            if connected_devices:
                print("   - 在线设备列表:")
                for device_id, info in connected_devices.items():
                    device_name = info.get('device_name', '未知')
                    client_id = info.get('client_id', '未知')
                    print(f"     * {device_id}: {device_name} (客户端: {client_id})")
            else:
                print("   - 当前无在线设备")
                
        except Exception as e:
            print(f"❌ 读取运行时数据失败: {e}")
    else:
        print("⚠️ 运行时数据文件不存在")

def check_room_configs():
    """检查房间配置状态"""
    rooms_dir = 'config/rooms'
    
    if os.path.exists(rooms_dir):
        room_files = [f for f in os.listdir(rooms_dir) if f.endswith('.json')]
        print(f"🏠 房间配置状态:")
        print(f"   - 配置房间: {len(room_files)} 个")
        
        if room_files:
            print("   - 房间列表:")
            for filename in room_files:
                room_file = os.path.join(rooms_dir, filename)
                try:
                    with open(room_file, 'r', encoding='utf-8') as f:
                        room_config = json.load(f)
                    
                    live_id = room_config.get('live_id', '未知')
                    room_name = room_config.get('room_name', '未知')
                    target_device_id = room_config.get('target_device_id', '')
                    enabled = room_config.get('enabled', False)
                    
                    status = "✅ 启用" if enabled else "❌ 禁用"
                    device_status = f"设备: {target_device_id}" if target_device_id else "设备: 未配置"
                    
                    print(f"     * {live_id}: {room_name} ({status}, {device_status})")
                    
                except Exception as e:
                    print(f"     * {filename}: 读取失败 ({e})")
        else:
            print("   - 当前无房间配置")
    else:
        print("❌ 房间配置目录不存在")

def check_backup_files():
    """检查备份文件"""
    backup_dirs = [d for d in os.listdir('.') if d.startswith('backup_')]
    
    print(f"💾 备份文件状态:")
    print(f"   - 备份目录: {len(backup_dirs)} 个")
    
    if backup_dirs:
        print("   - 备份列表:")
        for backup_dir in sorted(backup_dirs, reverse=True)[:5]:  # 显示最新的5个备份
            if os.path.isdir(backup_dir):
                backup_time = backup_dir.split('_')[-2:] if '_' in backup_dir else ['未知', '时间']
                print(f"     * {backup_dir} (时间: {backup_time[0]}_{backup_time[1]})")

def main():
    """主函数"""
    print("🤖 AI机器人自动化直播控制台 - 系统状态检查")
    print("=" * 60)
    print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查各个组件状态
    check_device_configs()
    print()
    
    check_runtime_data()
    print()
    
    check_room_configs()
    print()
    
    check_backup_files()
    print()
    
    print("=" * 60)
    print("💡 提示:")
    print("   - 如果设备配置为空，ESP32设备连接后会自动注册")
    print("   - 房间配置中的设备ID为空时，需要手动配置")
    print("   - 运行时数据反映当前实际连接状态")

if __name__ == "__main__":
    main()