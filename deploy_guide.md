# Linux服务器部署指南

## 🔧 环境准备

### 1. 系统要求
- Linux系统（Ubuntu 18.04+, CentOS 7+, 或其他主流发行版）
- Python 3.7+ 环境
- 至少2GB内存
- 至少1GB磁盘空间

### 2. 安装Python依赖
```bash
# 安装Python3和pip（如果没有）
sudo apt update
sudo apt install python3 python3-pip

# 或者在CentOS上
sudo yum install python3 python3-pip
```

## 📦 打包步骤

### 1. 在开发机器上准备
```bash
# 安装打包依赖
pip install -r build_requirements.txt

# 运行打包脚本
python build_linux.py
```

### 2. 打包完成后
- 会生成 `dist_linux/` 目录
- 包含所有可执行文件和配置文件
- 包含启动脚本 `start_system.sh`

## 🚀 部署到Linux服务器

### 1. 上传文件
```bash
# 将整个dist_linux目录上传到服务器
scp -r dist_linux/ user@server:/opt/douyin-bot/

# 或者打包后上传
tar -czf douyin-bot-linux.tar.gz dist_linux/
scp douyin-bot-linux.tar.gz user@server:/opt/
```

### 2. 服务器上解压和设置
```bash
# 解压（如果是压缩包）
cd /opt
tar -xzf douyin-bot-linux.tar.gz
mv dist_linux douyin-bot

# 设置权限
cd douyin-bot
chmod +x live_chat_bot flask_web_server websocket_server start_system.sh

# 检查端口是否可用
netstat -tlnp | grep -E ':(15000|15008)'
```

### 3. 启动系统
```bash
# 直接启动
./start_system.sh

# 或者后台启动
nohup ./start_system.sh > system.log 2>&1 &
```

## 🔧 配置说明

### 1. 端口配置
- WebSocket服务器：15000端口
- Web管理界面：15008端口
- 确保防火墙开放这两个端口

### 2. 配置文件
- `config/` 目录包含所有配置文件
- 可以根据需要修改配置
- 重启服务后生效

## 🛠️ 服务管理

### 1. 创建systemd服务（推荐）
```bash
# 创建服务文件
sudo nano /etc/systemd/system/douyin-bot.service
```

服务文件内容：
```ini
[Unit]
Description=Douyin Live Chat Bot System
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/opt/douyin-bot
ExecStart=/opt/douyin-bot/start_system.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. 启用和管理服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable douyin-bot

# 启动服务
sudo systemctl start douyin-bot

# 查看状态
sudo systemctl status douyin-bot

# 查看日志
sudo journalctl -u douyin-bot -f
```

## 🔍 故障排除

### 1. 常见问题
- **端口被占用**：使用 `netstat -tlnp | grep 端口号` 检查
- **权限问题**：确保可执行文件有执行权限
- **依赖缺失**：检查系统是否缺少必要的库文件

### 2. 日志查看
```bash
# 查看系统日志
tail -f system.log

# 查看特定组件日志
./live_chat_bot --help  # 查看帮助信息
```

### 3. 手动启动组件
```bash
# 单独启动WebSocket服务器
./websocket_server

# 单独启动Web服务器
./flask_web_server

# 单独启动聊天机器人
./live_chat_bot --live-id 房间ID --device-id 设备ID
```

## 🔒 安全建议

### 1. 网络安全
- 使用防火墙限制访问端口
- 考虑使用反向代理（nginx）
- 启用HTTPS（如果需要）

### 2. 系统安全
- 定期更新系统
- 使用非root用户运行服务
- 定期备份配置文件

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 系统日志文件
2. 网络连接状态
3. 端口占用情况
4. 文件权限设置
