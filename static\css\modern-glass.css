/* 工业风格蓝白配色设计系统 */

:root {
  /* 主色调 - 纯蓝色系 */
  --primary-blue: #2563eb;
  --secondary-blue: #1d4ed8;
  --light-blue: #dbeafe;
  --accent-blue: #1e40af;
  --dark-blue: #1e3a8a;
  --sky-blue: #0ea5e9;
  --pure-white: #ffffff;
  --bg-white: #f8fafc;
  --off-white: #f1f5f9;
  --border-blue: #3b82f6;
  
  /* 文字颜色 */
  --text-dark: #0f172a;
  --text-gray: #475569;
  --text-light: #64748b;
  --text-primary: var(--text-dark);
  --text-secondary: var(--text-gray);
  --text-muted: var(--text-light);
  
  /* 工业风强调色 */
  --accent-green: #059669;
  --accent-red: #dc2626;
  --accent-orange: #ea580c;
  --accent-cyan: #0891b2;
  
  /* 渐变色 - 纯蓝色系 */
  --gradient-primary: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-light: linear-gradient(90deg, #dbeafe 0%, #f1f5f9 100%);
  --gradient-card: rgba(255, 255, 255, 0.98);
  --gradient-header: linear-gradient(90deg, #eff6ff 0%, #f8fafc 100%);
  
  /* 工业风阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.15);
  --shadow-blue: 0 4px 12px rgba(37, 99, 235, 0.25);
  
  /* 工业风边框半径 - 更小更锐利 */
  --radius-sm: 2px;
  --radius-md: 4px;
  --radius-lg: 6px;
  --radius-xl: 8px;
  
  /* 紧凑间距 */
  --spacing-xs: 0.125rem;
  --spacing-sm: 0.25rem;
  --spacing-md: 0.5rem;
  --spacing-lg: 0.75rem;
  --spacing-xl: 1rem;
  --spacing-2xl: 1.5rem;
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: var(--text-primary);
  line-height: 1.4;
  min-height: 100vh;
  font-size: 14px;
}

/* 玻璃效果基础类 */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.glass-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* 工业风卡片 */
.modern-card {
  background: var(--gradient-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-blue);
  transition: all 0.2s ease;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-blue);
}

.modern-card-header {
  background: var(--gradient-header);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-blue);
}

.modern-card-body {
  padding: var(--spacing-lg);
}

/* 工业风按钮 */
.btn-modern {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: 600;
  font-size: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-blue);
  filter: brightness(1.1);
}

.btn-glass {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  transition: all 0.2s ease;
  cursor: pointer;
  font-weight: 600;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-glass:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-blue);
  background: var(--secondary-blue);
}

/* 工业风导航栏 */
.modern-navbar {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-bottom: 2px solid var(--border-blue);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.navbar-brand-modern {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-blue);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 工业风侧边栏 */
.modern-sidebar {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-right: 2px solid var(--border-blue);
  min-height: 100vh;
  padding: var(--spacing-xl);
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  font-size: 13px;
  border: 1px solid transparent;
}

.sidebar-nav-item:hover,
.sidebar-nav-item.active {
  background: var(--gradient-primary);
  color: white;
  border-color: var(--dark-blue);
  transform: translateX(2px);
}

.sidebar-nav-item i {
  font-size: 1rem;
}

/* 现代化表单 */
.form-modern {
  background: var(--primary-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.form-group-modern {
  margin-bottom: var(--spacing-lg);
}

.form-label-modern {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.form-input-modern {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 现代化表格 */
.table-modern {
  background: var(--primary-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table-modern thead {
  background: var(--gradient-glass);
}

.table-modern th,
.table-modern td {
  padding: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-modern tbody tr:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 状态徽章 */
.badge-modern {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: var(--accent-green);
  color: white;
}

.badge-warning {
  background: var(--accent-orange);
  color: white;
}

.badge-danger {
  background: var(--accent-red);
  color: white;
}

.badge-info {
  background: var(--accent-blue);
  color: white;
}

/* 响应式网格 */
.grid-modern {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

/* 实用工具类 */
.d-flex {
  display: flex;
}

.d-grid {
  display: grid;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-1 {
  gap: var(--spacing-xs);
}

.gap-2 {
  gap: var(--spacing-sm);
}

.gap-3 {
  gap: var(--spacing-md);
}

.gap-4 {
  gap: var(--spacing-lg);
}

.gap-5 {
  gap: var(--spacing-xl);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: var(--text-muted);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: var(--spacing-xs);
}

.mb-2 {
  margin-bottom: var(--spacing-sm);
}

.mb-3 {
  margin-bottom: var(--spacing-md);
}

.mb-4 {
  margin-bottom: var(--spacing-lg);
}

.mb-5 {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: var(--spacing-xs);
}

.mt-2 {
  margin-top: var(--spacing-sm);
}

.mt-3 {
  margin-top: var(--spacing-md);
}

.mt-4 {
  margin-top: var(--spacing-lg);
}

.mt-5 {
  margin-top: var(--spacing-xl);
}

.me-1 {
  margin-right: var(--spacing-xs);
}

.me-2 {
  margin-right: var(--spacing-sm);
}

.me-3 {
  margin-right: var(--spacing-md);
}

.ms-1 {
  margin-left: var(--spacing-xs);
}

.ms-2 {
  margin-left: var(--spacing-sm);
}

.ms-3 {
  margin-left: var(--spacing-md);
}

/* 加载状态 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-text {
  color: var(--text-secondary);
  text-align: center;
  margin-top: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-lg);
  }

  .sidebar-modern {
    padding: var(--spacing-md);
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    padding: var(--spacing-md);
  }

  .sidebar-modern {
    position: static;
    height: auto;
    margin-bottom: var(--spacing-lg);
  }

  .admin-layout {
    grid-template-columns: 1fr;
  }

  .admin-sidebar {
    display: none;
  }

  .hero-section {
    padding: var(--spacing-xl);
  }

  .page-header {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .modern-card {
    margin: var(--spacing-sm);
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .grid-modern {
    gap: var(--spacing-md);
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .device-grid {
    grid-template-columns: 1fr;
  }

  .leaderboards-grid {
    grid-template-columns: 1fr;
  }

  .hero-section {
    padding: var(--spacing-lg);
    text-align: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .header-title {
    font-size: 2rem;
  }

  .modern-navbar {
    padding: var(--spacing-sm) 0;
  }

  .navbar-brand-modern {
    font-size: 1.25rem;
  }

  .btn-modern,
  .btn-glass {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .table-modern {
    font-size: 0.875rem;
  }

  .table-modern th,
  .table-modern td {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  body {
    padding: var(--spacing-sm);
  }

  .hero-section {
    padding: var(--spacing-md);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    padding: var(--spacing-md);
  }

  .modern-card-header,
  .modern-card-body {
    padding: var(--spacing-md);
  }

  .table-header {
    padding: var(--spacing-md);
  }

  .btn-modern,
  .btn-glass {
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: var(--spacing-xs) !important;
  }

  .login-container {
    margin: var(--spacing-sm);
    padding: var(--spacing-lg);
  }

  .login-title {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .leaderboard-item {
    padding: var(--spacing-sm);
  }

  .rank-badge {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
