#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空所有设备ID的脚本
用于清理AI机器人自动化直播控制台中的所有设备注册信息
"""

import json
import os
import shutil
from datetime import datetime
from pathlib import Path

def backup_files():
    """备份原始文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_clear_devices_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    print(f"📁 创建备份目录: {backup_dir}")
    
    # 备份设备配置文件
    if os.path.exists('config/devices/devices.json'):
        shutil.copy2('config/devices/devices.json', f'{backup_dir}/devices.json')
        print("✅ 已备份设备配置文件")
    
    # 备份运行时数据
    if os.path.exists('runtime_data.json'):
        shutil.copy2('runtime_data.json', f'{backup_dir}/runtime_data.json')
        print("✅ 已备份运行时数据文件")
    
    # 备份房间配置文件
    rooms_dir = 'config/rooms'
    if os.path.exists(rooms_dir):
        backup_rooms_dir = f'{backup_dir}/rooms'
        shutil.copytree(rooms_dir, backup_rooms_dir)
        print("✅ 已备份房间配置文件")
    
    return backup_dir

def clear_device_configs():
    """清空设备配置文件"""
    devices_file = 'config/devices/devices.json'
    
    if os.path.exists(devices_file):
        # 读取当前配置
        with open(devices_file, 'r', encoding='utf-8') as f:
            current_config = json.load(f)
        
        device_count = len(current_config)
        print(f"🔍 发现 {device_count} 个设备配置")
        
        # 清空配置
        empty_config = {}
        
        # 写入空配置
        with open(devices_file, 'w', encoding='utf-8') as f:
            json.dump(empty_config, f, ensure_ascii=False, indent=2)
        
        print(f"🗑️ 已清空设备配置文件，删除了 {device_count} 个设备")
        return device_count
    else:
        print("⚠️ 设备配置文件不存在")
        return 0

def clear_runtime_data():
    """清空运行时数据中的设备信息"""
    runtime_file = 'runtime_data.json'
    
    if os.path.exists(runtime_file):
        try:
            with open(runtime_file, 'r', encoding='utf-8') as f:
                runtime_data = json.load(f)
            
            # 清空连接设备和设备配置
            connected_count = len(runtime_data.get('connected_devices', {}))
            config_count = len(runtime_data.get('device_configs', {}))
            
            runtime_data['connected_devices'] = {}
            runtime_data['device_configs'] = {}
            
            # 写回文件
            with open(runtime_file, 'w', encoding='utf-8') as f:
                json.dump(runtime_data, f, ensure_ascii=False, indent=2)
            
            print(f"🗑️ 已清空运行时数据，删除了 {connected_count} 个连接设备和 {config_count} 个设备配置")
            return connected_count + config_count
        except Exception as e:
            print(f"❌ 清空运行时数据失败: {e}")
            return 0
    else:
        print("⚠️ 运行时数据文件不存在")
        return 0

def clear_room_device_mappings():
    """清空房间配置中的设备映射"""
    rooms_dir = 'config/rooms'
    cleared_rooms = 0
    
    if not os.path.exists(rooms_dir):
        print("⚠️ 房间配置目录不存在")
        return 0
    
    for filename in os.listdir(rooms_dir):
        if filename.endswith('.json'):
            room_file = os.path.join(rooms_dir, filename)
            try:
                with open(room_file, 'r', encoding='utf-8') as f:
                    room_config = json.load(f)
                
                # 检查是否有target_device_id字段
                if 'target_device_id' in room_config:
                    old_device_id = room_config['target_device_id']
                    room_config['target_device_id'] = ""  # 清空设备ID
                    
                    # 写回文件
                    with open(room_file, 'w', encoding='utf-8') as f:
                        json.dump(room_config, f, ensure_ascii=False, indent=2)
                    
                    print(f"🗑️ 房间 {filename}: 清空设备ID ({old_device_id})")
                    cleared_rooms += 1
                    
            except Exception as e:
                print(f"❌ 处理房间配置 {filename} 失败: {e}")
    
    return cleared_rooms

def main():
    """主函数"""
    print("🤖 AI机器人自动化直播控制台 - 设备ID清理工具")
    print("=" * 60)
    
    # 确认操作
    print("⚠️ 此操作将清空所有设备ID和设备配置信息！")
    print("📋 将清理以下内容：")
    print("   1. config/devices/devices.json - 设备配置")
    print("   2. runtime_data.json - 运行时设备数据")
    print("   3. config/rooms/*.json - 房间设备映射")
    print()
    
    confirm = input("🔍 确认执行清理操作？(输入 'yes' 确认): ").strip().lower()
    if confirm != 'yes':
        print("❌ 操作已取消")
        return
    
    print("\n🚀 开始清理设备ID...")
    
    # 1. 备份文件
    backup_dir = backup_files()
    
    # 2. 清空设备配置
    device_count = clear_device_configs()
    
    # 3. 清空运行时数据
    runtime_count = clear_runtime_data()
    
    # 4. 清空房间设备映射
    room_count = clear_room_device_mappings()
    
    # 总结
    print("\n" + "=" * 60)
    print("✅ 设备ID清理完成！")
    print(f"📊 清理统计:")
    print(f"   - 设备配置: {device_count} 个")
    print(f"   - 运行时数据: {runtime_count} 个")
    print(f"   - 房间映射: {room_count} 个")
    print(f"📁 备份位置: {backup_dir}")
    print("\n💡 提示:")
    print("   - 所有ESP32设备需要重新连接以重新注册")
    print("   - 房间配置中的设备映射已清空，需要重新配置")
    print("   - 如需恢复，请使用备份文件")

if __name__ == "__main__":
    main()