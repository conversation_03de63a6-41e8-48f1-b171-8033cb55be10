#!/usr/bin/python
# coding:utf-8

# @FileName:    live_chat_bot.py
# @Time:        2024/7/12
# @Author:      Assistant
# @Project:     douyinLiveWebFetcher
# @Version:     2.0 - 增强版
# @Update:      2025/1/14 - 添加智能设备状态检查功能

"""
抖音直播聊天机器人 - 增强版
支持配置文件、消息过滤、多种回复模板等功能

新增功能 v2.0:
- 智能设备状态检查：参考 web/server.py 的设备状态检查逻辑
- 只有在 'listening' 或 'idle' 状态时才发送消息
- 在 'speaking' 状态时自动阻止发送，避免设备冲突
- 支持状态缓存，避免频繁检查
- 详细的状态信息和说明
- 可配置的状态检查间隔

状态说明:
- idle: 空闲 - 可以发送消息
- listening: 监听中 - 可以发送消息
- speaking: 讲话中 - 暂时不能发送消息
- connecting: 连接中 - 暂时不能发送消息
- activating: 激活中 - 暂时不能发送消息
- error: 错误状态 - 不能发送消息
- offline: 离线 - 不能发送消息
- timeout: 超时 - 不能发送消息
"""

import json
import os
import time
import threading
import re
import random
import websockets
from typing import Dict, Any
from difflib import SequenceMatcher
from collections import defaultdict
from liveMan import DouyinLiveWebFetcher

from leaderboard_stats import leaderboard_stats

# 消息权重常量定义 - 统一管理所有消息类型的优先级
class MessageWeight:
    """消息权重常量类 - 数值越大优先级越高"""

    # 最高优先级 (999-100)
    SYSTEM_BROADCAST = 999      # 系统插播 - 紧急通知
    GIFT_EXPENSIVE = 100       # 嘉年华礼物 - 高价值礼物
    GIFT_MEDIUM = 50           # 火箭礼物 - 中等礼物
    GIFT_CHEAP = 20            # 跑车礼物 - 便宜礼物
    GIFT_BASIC = 15            # 基础礼物 - 玫瑰、小心心

    # 用户消息优先级 (15-10)
    NEW_USER = 15              # 新用户首次发言 - 鼓励新用户参与
    PRIORITY_USER = 12         # 优先用户消息 - 关注用户、游戏获胜者
    NORMAL_USER = 10           # 普通用户消息 - 所有真实用户聊天

    # 系统回复优先级 (8-5)
    CUSTOM_REPLY = 8           # 自定义回复 - 关键词触发回复
    FOLLOW_MESSAGE = 6         # 关注消息回复 - 感谢关注
    LIKE_MESSAGE = 5           # 点赞消息回复 - 感谢点赞

    # 低优先级消息 (1-0)
    WELCOME_MESSAGE = 1        # 欢迎消息 - 进入直播间欢迎
    ANTI_SILENCE = 0           # 防冷场消息 - 自动防冷场

    # 游戏相关消息（保持原有高优先级）
    GAME_START = 200           # 游戏开始指令
    GAME_TIMEOUT = 150         # 游戏超时指令

    # 普通消息（无优先级）
    NORMAL = 0                 # 普通消息（不进优先队列）

class LogManager:
    """日志管理器 - 过滤和优化日志输出"""

    def __init__(self, config: 'ConfigManager', room_id: str = None):
        self.config = config
        self.room_id = room_id

        # 日志配置
        self.level = config.get('logging.level', 'INFO')
        self.filter_http = config.get('logging.filter_http_requests', True)
        self.filter_heartbeat = config.get('logging.filter_heartbeat', True)
        self.filter_device_status = config.get('logging.filter_device_status', False)
        self.merge_duplicates = config.get('logging.merge_duplicate_logs', True)
        self.show_timestamps = config.get('logging.show_timestamps', True)
        self.highlight_important = config.get('logging.highlight_important', True)
        self.show_room_id = config.get('logging.show_room_id', True)

        # 重复日志计数器和优化
        self.duplicate_counts = defaultdict(int)
        self.last_log_time = defaultdict(float)
        self.log_cache = {}  # 日志缓存，避免重复输出
        self.last_cache_clear = time.time()  # 上次清理缓存时间

        # 过滤的日志模式
        self.filtered_patterns = [
            "GET /api/devices/list",
            "GET /api/status",
            "GET /api/processes",
            "GET /api/rooms",
            "GET /api/internal/devices",
            "【√】发送心跳包",
            "直播间adaptation:",
            "⚠️ 未知消息格式: 2",
            "⚠️ 未知消息格式:",
            "📨 收到消息",
            "🤖 检测到ESP32 JSON消息",
            "🔧 解析消息",
            "📱 Web客户端已添加",
            "🔍 收到设备状态查询请求",
            "📤 已发送设备列表响应",
            "============================================================",
            "🔍 设备ID:",
            "🎯 状态变化:",
            "🎤 可以说话:",
            "🔊 检测到语音:",
            "⏰ 时间戳:",
            "❌ 广播状态更新失败:",
            "✅ 设备状态已更新并保存"
        ]

    def should_log(self, message: str, level: str = 'INFO') -> bool:
        """判断是否应该输出日志"""

        # 检查日志级别
        if level == 'DEBUG' and self.level != 'DEBUG':
            return False

        # 过滤心跳包
        if self.filter_heartbeat and "【√】发送心跳包" in message:
            return False

        # 过滤HTTP请求
        if self.filter_http:
            for pattern in self.filtered_patterns:
                if pattern in message:
                    return False

        # 过滤设备状态（如果启用）
        if self.filter_device_status and ("设备状态实时更新" in message or "📊" in message):
            return False

        return True

    def log(self, message: str, level: str = 'INFO', important: bool = False):
        """输出日志（带过滤和优化）"""

        if not self.should_log(message, level):
            return

        # 重要日志突出显示
        if important and self.highlight_important:
            message = f"🔥 {message}"

        # 定期清理日志缓存，避免内存泄漏
        current_time = time.time()
        if current_time - self.last_cache_clear > 300:  # 每5分钟清理一次
            self._clear_log_cache()
            self.last_cache_clear = current_time

        # 优化的重复日志处理
        if self.merge_duplicates:
            # 简化消息用于重复检测
            simple_msg = self._simplify_message(message)

            # 使用缓存避免重复处理
            cache_key = f"{simple_msg}_{level}"
            if cache_key in self.log_cache:
                cache_entry = self.log_cache[cache_key]
                cache_entry['count'] += 1

                # 如果距离上次输出超过3秒，输出合并信息
                if current_time - cache_entry['last_output'] > 3:
                    if cache_entry['count'] > 1:
                        print(f"   ↳ 上述消息重复了 {cache_entry['count']} 次")
                    cache_entry['count'] = 1
                    cache_entry['last_output'] = current_time
                else:
                    return  # 跳过重复日志
            else:
                self.log_cache[cache_key] = {
                    'count': 1,
                    'last_output': current_time,
                    'original_message': message
                }

        # 添加时间戳（如果启用）
        if self.show_timestamps:
            timestamp = time.strftime("%H:%M:%S", time.localtime())
            message = f"[{timestamp}] {message}"

        # 添加直播间标识（如果启用且有room_id）
        if self.show_room_id and self.room_id:
            # 截取直播间ID的后6位作为标识
            room_short = self.room_id[-6:] if len(self.room_id) > 6 else self.room_id
            message = f"[{room_short}] {message}"

        print(message)

    def _clear_log_cache(self):
        """清理过期的日志缓存"""
        current_time = time.time()
        expired_keys = []

        for key, entry in self.log_cache.items():
            # 清理超过10分钟未使用的缓存
            if current_time - entry['last_output'] > 600:
                expired_keys.append(key)

        for key in expired_keys:
            del self.log_cache[key]

        # 同时清理旧的重复计数器
        expired_simple_msgs = []
        for simple_msg, last_time in self.last_log_time.items():
            if current_time - last_time > 600:
                expired_simple_msgs.append(simple_msg)

        for simple_msg in expired_simple_msgs:
            del self.last_log_time[simple_msg]
            if simple_msg in self.duplicate_counts:
                del self.duplicate_counts[simple_msg]

    def _simplify_message(self, message: str) -> str:
        """简化消息用于重复检测 - 改进版，减少误判"""
        # 移除时间戳、数字等变化部分
        import re

        # 移除时间戳
        message = re.sub(r'\[\d{2}:\d{2}:\d{2}\]', '', message)

        # 移除房间ID标识（如[907443]）
        message = re.sub(r'\[\d{6,}\]', '[ROOM]', message)

        # 移除IP地址
        message = re.sub(r'\d+\.\d+\.\d+\.\d+', 'IP', message)

        # 移除端口号
        message = re.sub(r':\d+', ':PORT', message)

        # 更精确的数字替换：保留用户ID和重要数字模式
        # 只替换独立的长数字（可能是用户ID），保留短数字
        message = re.sub(r'\b\d{8,}\b', 'USERID', message)  # 8位以上数字视为用户ID
        message = re.sub(r'\b\d{4,7}\b', 'LONGNUM', message)  # 4-7位数字
        # 保留1-3位数字不替换，这些通常是有意义的数字

        # 移除多余空格
        message = re.sub(r'\s+', ' ', message).strip()

        return message

    def important(self, message: str):
        """输出重要日志"""
        self.log(message, 'INFO', important=True)

    def debug(self, message: str):
        """输出调试日志"""
        self.log(message, 'DEBUG')

    def error(self, message: str):
        """输出错误日志"""
        self.log(f"❌ {message}", 'ERROR', important=True)

    def warning(self, message: str):
        """输出警告日志"""
        self.log(f"⚠️ {message}", 'WARNING', important=True)

    def success(self, message: str):
        """输出成功日志"""
        self.log(f"✅ {message}", 'INFO')

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ 配置文件加载成功: {self.config_file}")
                return config
            else:
                print(f"⚠️ 配置文件不存在，使用默认配置: {self.config_file}")
                return self.get_default_config()
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return self.get_default_config()
            
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "live_settings": {
                "live_id": "200382305200"
            },
            "esp32_settings": {
                "device_id": "esp32_device_001"
            },
            "websocket_settings": {
                "url": "ws://localhost:15000"
            },
            "reply_settings": {
                "enabled": True,
                "template": "{user_name}说{content}",
                "cooldown_seconds": 2
            },
            "filter_settings": {
                "min_message_length": 1,
                "max_message_length": 100,
                "blocked_keywords": [],
                "blocked_users": []
            },
            "templates": {
                "simple": "{user_name}说{content}",
                "echo": "{content}",
                "custom": "苦瓜说{content}"
            },
            "status_check": {
                "enabled": True,
                "interval_seconds": 10
            },
            "message_queue": {
                "enabled": True,
                "max_size": 10,
                "max_age_seconds": 60
            },
            "status_monitor": {
                "enabled": True,
                "interval_seconds": 1
            },
            "anti_silence": {
                "enabled": True,
                "delay_seconds": 3,
                "cooldown_seconds": 10
            },
            "like_interaction": {
                "enabled": True,
                "cooldown_seconds": 60,
                "first_like_threshold": 1,
                "subsequent_like_threshold": 100
            },
            "force_send": {
                "enabled": True,
                "timeout_seconds": 20,
                "user_only": True
            },
            "topic_templates": [
                "大家好，我是苦瓜，一个AI助手！今天天气不错呢，大家都在做什么呀？",
                "刚才聊到的话题很有趣，我想继续和大家聊聊，有什么想法吗？",
                "我发现直播间里有很多有趣的朋友，大家可以多互动一下哦！",
                "作为AI助手，我很喜欢和大家聊天，有什么问题都可以问我！",
                "今天学到了很多新东西，和大家分享真的很开心！"
            ],
            "like_templates": [
                "谢谢{user_name}的点赞！你的支持让我很开心！",
                "哇，{user_name}给我点赞了！感谢你的认可！",
                "{user_name}，你的点赞就像阳光一样温暖！",
                "感谢{user_name}的点赞支持，我们一起聊天吧！",
                "{user_name}点赞了！你有什么想和我聊的吗？"
            ]
        }
        
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"💾 配置文件保存成功: {self.config_file}")
        except Exception as e:
            print(f"❌ 配置文件保存失败: {e}")
            
    def get(self, key_path: str, default=None):
        """获取配置值，支持点号分隔的路径 - 增强版本"""
        try:
            keys = key_path.split('.')
            value = self.config

            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    if default is not None:
                        print(f"⚠️ 配置项 '{key_path}' 不存在，使用默认值: {default}")
                    return default

            # 验证配置值的合理性
            validated_value = self._validate_config_value(key_path, value, default)
            return validated_value

        except Exception as e:
            print(f"❌ 获取配置 '{key_path}' 时出错: {e}")
            return default

    def _validate_config_value(self, key_path: str, value, default):
        """验证配置值的合理性"""
        try:
            # 数值范围验证
            if 'interval_seconds' in key_path and isinstance(value, (int, float)):
                if value < 0.1:
                    print(f"⚠️ 配置 '{key_path}' 值过小 ({value})，调整为最小值 0.1")
                    return 0.1
                elif value > 3600:
                    print(f"⚠️ 配置 '{key_path}' 值过大 ({value})，调整为最大值 3600")
                    return 3600

            # 队列大小验证
            if 'max_size' in key_path and isinstance(value, int):
                if value < 1:
                    print(f"⚠️ 配置 '{key_path}' 值过小 ({value})，调整为最小值 1")
                    return 1
                elif value > 1000:
                    print(f"⚠️ 配置 '{key_path}' 值过大 ({value})，调整为最大值 1000")
                    return 1000

            # 列表类型验证
            if 'templates' in key_path and not isinstance(value, list):
                print(f"⚠️ 配置 '{key_path}' 应为列表类型，使用默认值")
                return default if isinstance(default, list) else []

            return value

        except Exception as e:
            print(f"⚠️ 验证配置 '{key_path}' 时出错: {e}，使用原值")
            return value
        
    def set(self, key_path: str, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        config[keys[-1]] = value
        self.save_config()

class MessageFilter:
    """消息过滤器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        
    def should_reply(self, user_name: str, user_id: str, content: str) -> bool:
        """判断是否应该回复此消息"""
        
        # 检查消息长度
        min_length = self.config.get('filter_settings.min_message_length', 1)
        max_length = self.config.get('filter_settings.max_message_length', 100)
        
        if len(content) < min_length or len(content) > max_length:
            print(f"🚫 消息长度不符合要求: {len(content)} (要求: {min_length}-{max_length})")
            return False
            
        # 检查屏蔽关键词
        blocked_keywords = self.config.get('filter_settings.blocked_keywords', [])
        for keyword in blocked_keywords:
            if keyword in content:
                print(f"🚫 消息包含屏蔽关键词: {keyword}")
                return False
                
        # 检查屏蔽用户
        blocked_users = self.config.get('filter_settings.blocked_users', [])
        if user_name in blocked_users or user_id in blocked_users:
            print(f"🚫 用户已被屏蔽: {user_name}")
            return False
            
        return True

class CustomReplyManager:
    """自定义回复管理器"""

    def __init__(self, config_manager):
        self.config = config_manager
        self.enabled = False
        self.priority_weight = MessageWeight.CUSTOM_REPLY
        self.match_mode = "contains"  # "exact" 或 "contains"
        self.case_sensitive = False
        self.cooldown_seconds = 5
        self.rules = []
        self.cooldowns = {}  # 规则冷却时间记录 {rule_id: last_trigger_time}

        # 线程安全锁
        import threading
        self.lock = threading.Lock()

        self.load_config()

    def load_config(self):
        """加载自定义回复配置"""
        try:
            custom_reply_config = self.config.get('custom_reply', {})
            self.enabled = custom_reply_config.get('enabled', False)
            self.priority_weight = custom_reply_config.get('priority_weight', MessageWeight.CUSTOM_REPLY)
            self.match_mode = custom_reply_config.get('match_mode', 'contains')
            self.case_sensitive = custom_reply_config.get('case_sensitive', False)
            self.cooldown_seconds = custom_reply_config.get('cooldown_seconds', 5)
            self.rules = custom_reply_config.get('rules', [])

            # 验证规则格式
            valid_rules = []
            for rule in self.rules:
                if self._validate_rule(rule):
                    valid_rules.append(rule)
                else:
                    print(f"⚠️ 自定义回复规则格式错误，已跳过: {rule.get('id', 'unknown')}")

            self.rules = valid_rules
            print(f"📝 自定义回复配置加载完成: {'启用' if self.enabled else '禁用'}, {len(self.rules)}条规则")

        except Exception as e:
            print(f"❌ 加载自定义回复配置失败: {e}")
            self.enabled = False
            self.rules = []

    def _validate_rule(self, rule):
        """验证规则格式"""
        required_fields = ['id', 'keywords', 'responses']
        for field in required_fields:
            if field not in rule:
                return False

        if not isinstance(rule['keywords'], list) or not rule['keywords']:
            return False

        if not isinstance(rule['responses'], list) or not rule['responses']:
            return False

        return True

    def check_and_reply(self, user_message, user_name, user_id):
        """检查消息并返回自定义回复"""
        if not self.enabled or not self.rules:
            return None

        with self.lock:
            current_time = time.time()

            # 查找匹配的规则
            matching_rule = self._find_matching_rule(user_message)
            if not matching_rule:
                return None

            rule_id = matching_rule['id']

            # 检查冷却时间
            if self._is_in_cooldown(rule_id, current_time):
                print(f"🕐 自定义回复规则 {rule_id} 在冷却期，跳过")
                return None

            # 选择回复内容
            responses = matching_rule['responses']
            reply_template = random.choice(responses)

            # 替换变量
            reply_content = reply_template.format(
                user_name=user_name,
                user_id=user_id
            )

            # 更新冷却时间
            self.cooldowns[rule_id] = current_time

            # 获取规则权重
            rule_weight = matching_rule.get('weight', self.priority_weight)

            print(f"✅ 自定义回复触发: 规则={rule_id}, 用户={user_name}, 回复={reply_content[:30]}...")

            return {
                'content': reply_content,
                'weight': rule_weight,
                'rule_id': rule_id
            }

    def _find_matching_rule(self, message):
        """查找匹配的规则"""
        for rule in self.rules:
            if not rule.get('enabled', True):
                continue

            keywords = rule['keywords']
            rule_match_mode = rule.get('match_mode', self.match_mode)
            rule_case_sensitive = rule.get('case_sensitive', self.case_sensitive)

            # 准备消息文本
            check_message = message if rule_case_sensitive else message.lower()

            # 检查关键词匹配
            for keyword in keywords:
                check_keyword = keyword if rule_case_sensitive else keyword.lower()

                if rule_match_mode == 'exact':
                    if check_message == check_keyword:
                        return rule
                else:  # contains
                    if check_keyword in check_message:
                        return rule

        return None

    def _is_in_cooldown(self, rule_id, current_time):
        """检查规则是否在冷却期"""
        if rule_id not in self.cooldowns:
            return False

        last_trigger_time = self.cooldowns[rule_id]
        return (current_time - last_trigger_time) < self.cooldown_seconds

class AntiSpamFilter:
    """防刷屏过滤器"""

    def __init__(self, config: ConfigManager):
        self.config = config

        # 用户消息历史记录 {user_id: [{'content': str, 'timestamp': float}, ...]}
        self.user_messages = {}

        # 被屏蔽的用户 {user_id: block_until_timestamp}
        self.blocked_users = {}

        # 警告冷却 {user_id: last_warning_timestamp}
        self.warning_cooldowns = {}

        # 线程安全锁
        import threading
        self.lock = threading.Lock()

        # 配置参数
        self.enabled = config.get('anti_spam.enabled', True)

        # 重复消息检测配置
        self.duplicate_enabled = config.get('anti_spam.duplicate_detection.enabled', True)
        self.duplicate_time_window = config.get('anti_spam.duplicate_detection.time_window_seconds', 60)
        self.max_duplicates = config.get('anti_spam.duplicate_detection.max_duplicates', 3)
        self.similarity_threshold = config.get('anti_spam.duplicate_detection.similarity_threshold', 0.8)

        # 频率限制配置
        self.frequency_enabled = config.get('anti_spam.frequency_limit.enabled', True)
        self.frequency_time_window = config.get('anti_spam.frequency_limit.time_window_seconds', 30)
        self.max_messages = config.get('anti_spam.frequency_limit.max_messages', 5)

        # 警告和屏蔽配置
        self.warning_messages = config.get('anti_spam.warning_messages', [
            "{user_name}你个刷屏怪，再刷屏把你关小黑屋了！"
        ])
        self.block_duration = config.get('anti_spam.block_duration_seconds', 300)
        self.warning_cooldown = config.get('anti_spam.warning_cooldown_seconds', 60)

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

    def clean_old_messages(self, user_id: str, current_time: float):
        """清理过期的消息记录"""
        if user_id not in self.user_messages:
            return

        # 清理重复检测时间窗口外的消息
        self.user_messages[user_id] = [
            msg for msg in self.user_messages[user_id]
            if current_time - msg['timestamp'] <= max(self.duplicate_time_window, self.frequency_time_window)
        ]

        # 如果没有消息了，删除用户记录
        if not self.user_messages[user_id]:
            del self.user_messages[user_id]

    def is_user_blocked(self, user_id: str) -> bool:
        """检查用户是否被屏蔽"""
        current_time = time.time()

        if user_id in self.blocked_users:
            if current_time < self.blocked_users[user_id]:
                return True
            else:
                # 屏蔽时间已过，移除屏蔽
                del self.blocked_users[user_id]
                print(f"🔓 用户 {user_id} 屏蔽时间已过，恢复正常")

        return False

    def block_user(self, user_id: str, user_name: str):
        """屏蔽用户"""
        current_time = time.time()
        self.blocked_users[user_id] = current_time + self.block_duration
        print(f"🚫 用户 {user_name}({user_id}) 因刷屏被屏蔽 {self.block_duration} 秒")

    def should_warn_user(self, user_id: str) -> bool:
        """检查是否应该警告用户（避免警告过于频繁）"""
        current_time = time.time()

        if user_id in self.warning_cooldowns:
            if current_time - self.warning_cooldowns[user_id] < self.warning_cooldown:
                return False

        self.warning_cooldowns[user_id] = current_time
        return True

    def get_warning_message(self, user_name: str) -> str:
        """获取随机警告消息"""
        template = random.choice(self.warning_messages)
        return template.format(user_name=user_name)

    def check_spam(self, user_id: str, user_name: str, content: str) -> Dict[str, Any]:
        """
        检查消息是否为刷屏 - 线程安全版本
        返回: {
            'is_spam': bool,
            'reason': str,
            'action': str,  # 'block', 'warn', 'ignore'
            'warning_message': str or None
        }
        """
        if not self.enabled:
            return {'is_spam': False, 'reason': 'disabled', 'action': 'ignore', 'warning_message': None}

        with self.lock:
            current_time = time.time()

            # 检查用户是否已被屏蔽
            if self.is_user_blocked(user_id):
                return {'is_spam': True, 'reason': 'blocked', 'action': 'ignore', 'warning_message': None}

        # 清理过期消息
        self.clean_old_messages(user_id, current_time)

        # 初始化用户消息记录
        if user_id not in self.user_messages:
            self.user_messages[user_id] = []

        user_msgs = self.user_messages[user_id]

        # 检查频率限制
        if self.frequency_enabled:
            recent_msgs = [
                msg for msg in user_msgs
                if current_time - msg['timestamp'] <= self.frequency_time_window
            ]

            if len(recent_msgs) >= self.max_messages:
                print(f"🚨 检测到频率刷屏: {user_name} 在 {self.frequency_time_window}秒内发送了 {len(recent_msgs)} 条消息")

                # 屏蔽用户
                self.block_user(user_id, user_name)

                # 检查是否需要警告
                if self.should_warn_user(user_id):
                    warning_msg = self.get_warning_message(user_name)
                    return {'is_spam': True, 'reason': 'frequency', 'action': 'warn', 'warning_message': warning_msg}
                else:
                    return {'is_spam': True, 'reason': 'frequency', 'action': 'block', 'warning_message': None}

        # 检查重复消息
        if self.duplicate_enabled:
            duplicate_count = 0
            for msg in user_msgs:
                if current_time - msg['timestamp'] <= self.duplicate_time_window:
                    similarity = self.calculate_similarity(content, msg['content'])
                    if similarity >= self.similarity_threshold:
                        duplicate_count += 1

            if duplicate_count >= self.max_duplicates:
                print(f"🚨 检测到重复刷屏: {user_name} 发送了 {duplicate_count} 条相似消息 (相似度阈值: {self.similarity_threshold})")

                # 屏蔽用户
                self.block_user(user_id, user_name)

                # 检查是否需要警告
                if self.should_warn_user(user_id):
                    warning_msg = self.get_warning_message(user_name)
                    return {'is_spam': True, 'reason': 'duplicate', 'action': 'warn', 'warning_message': warning_msg}
                else:
                    return {'is_spam': True, 'reason': 'duplicate', 'action': 'block', 'warning_message': None}

            # 只有通过所有检查的正常消息才记录到用户消息历史中
            user_msgs.append({
                'content': content,
                'timestamp': current_time
            })

            return {'is_spam': False, 'reason': 'normal', 'action': 'allow', 'warning_message': None}



class EnhancedLiveFetcher(DouyinLiveWebFetcher):
    """增强版直播监控器"""

    def __init__(self, config: ConfigManager):
        self.config = config
        # 获取直播间ID用于日志标识
        live_id = config.get('live_settings.live_id', 'unknown')
        self.logger = LogManager(config, live_id)
        self.message_filter = MessageFilter(config)

        # 获取直播间ID用于房间配置
        self.live_id = config.get('live_settings.live_id', 'unknown')

        # 使用房间配置初始化防刷屏过滤器
        anti_spam_config = self.get_room_config('anti_spam', {})
        self.anti_spam_filter = AntiSpamFilter(anti_spam_config)

        # 初始化自定义回复管理器
        self.custom_reply_manager = CustomReplyManager(self.config)



        print(f"🏠 房间ID: {self.live_id}")

        # 从配置获取参数
        live_id = config.get('live_settings.live_id')

        # 初始化父类
        super().__init__(live_id)

        # WebSocket配置
        self.websocket_url = config.get('websocket_settings.url', 'ws://localhost:15000')

        # 设备ID配置 - 优先使用房间配置中的esp32_settings.device_id
        room_esp32_settings = self.get_room_config('esp32_settings', {})
        if room_esp32_settings and 'device_id' in room_esp32_settings:
            self.target_device_id = room_esp32_settings['device_id']
        else:
            # 回退到旧的配置方式
            self.target_device_id = self.get_room_config('target_device_id', config.get('esp32_settings.device_id', 'esp32_device_001'))

        # 应用配置 - 使用房间配置
        reply_config = self.get_room_config('reply_settings', {})
        self.auto_reply_enabled = reply_config.get('enabled', config.get('reply_settings.enabled', True))
        self.reply_template = reply_config.get('template', config.get('reply_settings.template', '{user_name}说{content}'))
        self.reply_cooldown = reply_config.get('cooldown_seconds', config.get('reply_settings.cooldown_seconds', 2))
        self.last_reply_time = 0

        # 状态检查配置
        self.status_check_enabled = config.get('status_check.enabled', True)
        self.status_check_interval = config.get('status_check.interval_seconds', 10)
        self.last_status_check = 0
        self.last_device_state = "unknown"

        # 消息队列配置 - 优化时效性和防积压
        self.message_queue_enabled = config.get('message_queue.enabled', True)
        self.message_queue_max_size = config.get('message_queue.max_size', 20)  # 增加到20条，防止重要消息丢失
        self.message_queue_max_age = config.get('message_queue.max_age_seconds', 30)  # 减少到30秒，加快清理
        self.message_queue = []  # 消息队列
        self.queue_lock = threading.Lock()  # 队列锁，防止并发访问冲突

        # 队列优化配置
        self.queue_high_priority_max = 5   # 高优先级消息最大数量
        self.queue_normal_priority_max = 15  # 普通优先级消息最大数量
        self.queue_cleanup_threshold = 18   # 队列清理阈值

        # 全局发送锁 - 防止多条消息同时发送导致设备卡死
        self.send_lock = threading.Lock()  # 全局发送锁，确保同时只能发送一条消息
        self.current_sending = False  # 当前是否正在发送消息
        self.last_send_complete_time = 0  # 上次发送完成时间

        # 强制发送配置
        self.force_send_enabled = config.get('force_send.enabled', True)
        self.force_send_timeout = config.get('force_send.timeout_seconds', 60)  # 60秒后强制发送
        self.force_send_user_only = config.get('force_send.user_only', True)  # 只强制发送用户消息

        # 消息时间过滤（只处理程序启动后的消息）
        self.start_time = time.time()
        self.message_time_filter_enabled = True

        # 运行控制
        self.running = False

        # 状态监控线程
        self.status_monitor_enabled = config.get('status_monitor.enabled', True)
        self.status_monitor_interval = config.get('status_monitor.interval_seconds', 1)  # 改为1秒，减少延迟
        self.status_monitor_thread = None

        # 防冷场功能配置 - 从房间配置加载
        anti_silence_room_config = self.get_room_config('anti_silence', {})
        self.anti_silence_enabled = anti_silence_room_config.get('enabled', True)
        self.anti_silence_delay = anti_silence_room_config.get('delay_seconds', 3)  # listening状态3秒无消息触发
        self.anti_silence_cooldown = anti_silence_room_config.get('cooldown_seconds', 10)  # 防冷场消息间隔
        self.last_anti_silence_time = 0
        self.last_message_time = 0  # 最后收到消息的时间（初始为0，等待第一条消息）
        self.has_received_message = False  # 是否已收到过消息
        self.listening_start_time = 0  # listening状态开始时间

        # 最后发言用户跟踪（用于个性化防冷场）
        self.last_speaker = None  # 最后发言用户信息 {'user_name': str, 'user_id': str, 'time': float}
        self.max_proactive_attempts = anti_silence_room_config.get('max_proactive_attempts', 2)  # 对同一用户最多主动沟通次数
        self.user_proactive_count = {}  # 用户主动沟通次数记录 {user_id: count}

        # 点赞互动功能配置 - 从房间配置加载
        like_interaction_config = self.get_room_config('like_interaction', {})
        self.like_interaction_enabled = like_interaction_config.get('enabled', True)
        self.like_interaction_cooldown = like_interaction_config.get('cooldown_seconds', 60)  # 点赞互动间隔
        self.first_like_threshold = like_interaction_config.get('first_like_threshold', 1)  # 第一次点赞阈值
        self.subsequent_like_threshold = like_interaction_config.get('subsequent_like_threshold', 1000)  # 后续点赞阈值改为1000
        self.last_like_interaction_time = 0
        self.user_like_counts = {}  # 记录用户点赞累计数
        self.user_first_like = {}  # 记录用户是否已经第一次点赞回复过

        # 获取默认模板配置
        default_templates = self._get_default_templates()

        # 话题库 - 使用房间配置
        anti_silence_config = self.get_room_config('anti_silence', {})
        self.topic_templates = anti_silence_config.get('templates', default_templates['topics'])

        # 点赞模板 - 使用房间配置
        like_config = self.get_room_config('like_interaction', {})
        # 修复空模板问题：如果模板为空，使用默认模板
        templates = like_config.get('templates', [])
        if not templates:  # 如果模板为空或不存在，使用默认模板
            templates = default_templates['like_responses']
        self.like_templates = templates

        # 消息预处理配置 - 从房间配置加载
        message_preprocess_config = self.get_room_config('message_preprocess', {})
        self.message_preprocessing_enabled = message_preprocess_config.get('enabled', True)
        self.max_repeat_chars = message_preprocess_config.get('max_repeat_chars', 3)  # 最大重复字符数

        # 定时插播配置 - 使用房间配置
        broadcast_config = self.get_room_config('scheduled_broadcast', {})
        self.scheduled_broadcast_enabled = broadcast_config.get('enabled', config.get('scheduled_broadcast.enabled', True))
        self.broadcast_interval = broadcast_config.get('interval_minutes', config.get('scheduled_broadcast.interval_minutes', 10))
        self.broadcast_templates = broadcast_config.get('templates', config.get('scheduled_broadcast.templates', default_templates['broadcast_templates']))
        # 初始化时设置为当前时间，避免启动后立即触发插播
        self.last_broadcast_time = time.time()

        # 进入欢迎配置 - 使用房间配置
        welcome_config = self.get_room_config('welcome_message', {})
        self.welcome_enabled = welcome_config.get('enabled', config.get('welcome_message.enabled', True))
        self.welcome_cooldown = welcome_config.get('cooldown_seconds', config.get('welcome_message.cooldown_seconds', 0))
        # 修复空模板问题：如果模板为空，使用默认模板
        templates = welcome_config.get('templates', config.get('welcome_message.templates', []))
        if not templates:  # 如果模板为空或不存在，使用默认模板
            templates = [
                "欢迎{user_name}进入直播间！",
                "欢迎{user_name}来到直播间，很高兴见到你！",
                "{user_name}欢迎你！有什么想聊的吗？"
            ]
        self.welcome_templates = templates
        self.last_welcome_time = 0

        print(f"📋 配置已加载:")
        print(f"   📺 直播间ID: {live_id}")
        print(f"   📡 WebSocket地址: {self.websocket_url}")
        print(f"   🎯 目标设备ID: {self.target_device_id}")
        print(f"   🎯 回复模板: {self.reply_template}")
        print(f"   ⏱️ 冷却时间: {self.reply_cooldown}秒")
        print(f"   🔍 状态检查: {'启用' if self.status_check_enabled else '禁用'}")
        print(f"   📦 消息队列: {'启用' if self.message_queue_enabled else '禁用'}")
        print(f"   🔄 状态监控: {'启用' if self.status_monitor_enabled else '禁用'}")

        # 启动状态监控线程
        if self.status_monitor_enabled:
            self._start_status_monitor()

        # 启动配置重载线程（每30秒检查一次配置更新）
        self._start_config_reload_monitor()

        # 消息发送锁，防止消息重叠
        self.message_send_lock = threading.Lock()
        self.current_sending_message = None  # 当前正在发送的消息
        self.last_send_time = 0  # 上次发送时间

        # 新用户检测 - 记录已发言的用户
        self.spoken_users = set()  # 记录已经发过言的用户ID
        self.spoken_users_last_cleanup = time.time()  # 上次清理时间
        self.spoken_users_cleanup_interval = 3600  # 每小时清理一次（3600秒）

        # 排行榜统计相关（已改为直接统计聊天消息）

        # 礼物优先级功能 - 从房间配置加载
        gift_priority_config = self.get_room_config('gift_priority', {})
        self.gift_priority_enabled = gift_priority_config.get('enabled', True)
        self.gift_priority_count = gift_priority_config.get('priority_message_count', 2)
        self.gift_response_templates = gift_priority_config.get('gift_response_templates', [
            "哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧"
        ])

        # 礼物权重配置 - 不同礼物有不同的优先级权重，从房间配置加载
        self.gift_weights = gift_priority_config.get('gift_weights', {
            "玫瑰": MessageWeight.GIFT_BASIC,
            "小心心": MessageWeight.GIFT_BASIC,
            "跑车": MessageWeight.GIFT_CHEAP,
            "火箭": MessageWeight.GIFT_MEDIUM,
            "嘉年华": MessageWeight.GIFT_EXPENSIVE,
            "默认": MessageWeight.GIFT_BASIC  # 未配置礼物的默认权重
        })

        self.priority_users = {}  # 记录有优先权的用户 {user_id: remaining_count}

        # 关注互动配置 - 使用房间配置
        follow_config = self.get_room_config('follow_interaction', {})
        self.follow_interaction_enabled = follow_config.get('enabled', config.get('follow_interaction.enabled', True))
        # 修复空模板问题：如果模板为空，使用默认模板
        templates = follow_config.get('response_templates', config.get('follow_interaction.response_templates', []))
        if not templates:  # 如果模板为空或不存在，使用默认模板
            templates = default_templates['follow_templates']
        self.follow_response_templates = templates

        # 互动游戏系统配置 - 从房间配置加载
        interactive_games_config = self.get_room_config('interactive_games', {})
        self.interactive_games_enabled = interactive_games_config.get('enabled', True)
        self.game_cooldown = interactive_games_config.get('cooldown_seconds', 30)  # 游戏冷却时间
        self.last_game_time = 0

        # 当前游戏状态
        self.current_game = None
        self.game_answer = None
        self.game_participants = set()  # 参与游戏的用户
        self.game_start_time = 0
        self.game_timeout = 60  # 游戏超时时间（秒）
        self.game_starting = False  # 游戏启动锁定状态
        self.pending_game_type = None  # 等待启动的游戏类型

        # 游戏类型配置
        self.game_types = {
            "guess_number": {
                "name": "猜数字",
                "description": "我想了一个1-100的数字，大家来猜猜看！",
                "reward_priority": 3,  # 答对获得的优先权
                "timeout": 60
            },
            "math_quiz": {
                "name": "数学题",
                "description": "来做个简单的数学题吧！",
                "reward_priority": 2,
                "timeout": 30
            },
            "word_chain": {
                "name": "成语接龙",
                "description": "我说一个成语，大家来接龙！",
                "reward_priority": 2,
                "timeout": 45
            },
            "riddle": {
                "name": "猜谜语",
                "description": "来猜个谜语吧！",
                "reward_priority": 3,
                "timeout": 60
            },
            "quick_answer": {
                "name": "抢答题",
                "description": "快速抢答题来了！",
                "reward_priority": 1,
                "timeout": 20
            },
            "count_game": {
                "name": "数数游戏",
                "description": "我们来数数吧！从1开始，每人说一个数字！",
                "reward_priority": 1,
                "timeout": 120
            },
            "word_solitaire": {
                "name": "词语接龙",
                "description": "词语接龙游戏开始！",
                "reward_priority": 2,
                "timeout": 90
            },
            "emoji_guess": {
                "name": "猜表情",
                "description": "根据表情猜词语！",
                "reward_priority": 2,
                "timeout": 45
            }
        }

        # 自动游戏触发配置 - 从房间配置加载
        self.auto_game_enabled = interactive_games_config.get('auto_trigger', True)
        self.auto_game_interval = interactive_games_config.get('auto_interval_minutes', 5)  # 5分钟自动触发
        self.last_auto_game_time = 0

    def _get_default_templates(self):
        """获取所有默认模板配置"""
        return {
            'topics': [
                '你们好，今天天气不错呢，你们都在做什么呀？',
                '刚才聊到的话题很有趣，你们还有什么想法吗？',
                '你们可以多互动一下哦，我很喜欢和你们聊天！',
                '你们有什么问题都可以问我，我很乐意回答！',
                '今天学到了很多新东西，和你们分享真的很开心！',
                '你们最近在看什么好看的电影或电视剧吗？',
                '你们有什么有趣的话题想分享一下吗？',
                '直播间有点安静呢，你们都在忙什么呀？',
                '我想了解一下你们的兴趣爱好，可以聊聊吗？',
                '你们今天过得怎么样？有什么开心的事情想分享吗？'
            ],
            'like_responses': [
                "{user_name}刚刚给你点了赞，请感谢他并主动和他聊聊天",
                "{user_name}点赞了，请表达感谢并问问他有什么想聊的",
                "{user_name}给你点赞了，请热情地回应他，让他感受到温暖",
                "{user_name}点了赞，请感谢他的支持并主动找话题和他互动",
                "{user_name}刚刚点赞，请表达开心的心情并邀请他一起聊天",
                "{user_name}给你点赞了，请感谢他并问问他今天过得怎么样",
                "{user_name}点赞了，请表达感激之情并主动关心他",
                "{user_name}给你点了赞，请开心地回应并找个有趣的话题和他聊",
                "{user_name}点赞了，请感谢他的鼓励并主动和他交流",
                "{user_name}刚刚点赞，请表达感谢并邀请他参与到聊天中来"
            ],
            'follow_templates': [
                "哟 {user_name}关注了我，是不是看上我了？",
                "{user_name}终于关注了，眼光还不错嘛",
                "欢迎{user_name}，不过关注了可别后悔哦",
                "{user_name}关注了我，算你有品味",
                "哟{user_name}，关注我是你做过最明智的决定"
            ],
            'broadcast_templates': [
                "欢迎大家来到硅灵造物直播间！记得点赞关注哦！",
                "有什么问题可以随时在评论区提问，我会尽力回答！",
                "感谢大家的支持，让我们一起聊天互动吧！",
                "别忘了给主播点个小心心，你的支持是我最大的动力！",
                "新来的朋友记得关注一下，不迷路哦！"
            ]
        }

    def _log_config_update(self, config_name: str, old_count: int, new_count: int):
        """统一的配置更新日志"""
        if old_count != new_count:
            print(f"✅ {config_name}已更新: {old_count} -> {new_count} 条")

    def _start_status_monitor(self):
        """启动状态监控线程"""
        if self.status_monitor_thread is None or not self.status_monitor_thread.is_alive():
            self.status_monitor_thread = threading.Thread(
                target=self._status_monitor_loop,
                daemon=True
            )
            self.status_monitor_thread.start()
            print(f"🔄 状态监控线程已启动，检查间隔: {self.status_monitor_interval}秒")

    def get_room_config(self, section: str, default_value: Any = None) -> Any:
        """
        从房间配置获取配置，如果房间配置中没有则回退到全局配置

        Args:
            section: 配置部分名称
            default_value: 默认值

        Returns:
            配置值
        """
        try:
            # 尝试从新的配置结构加载房间配置
            import os
            import json

            # 优先使用新的配置结构
            room_config_path = f'config/rooms/{self.live_id}.json'
            if os.path.exists(room_config_path):
                with open(room_config_path, 'r', encoding='utf-8') as f:
                    room_config = json.load(f)
                if section in room_config:
                    return room_config[section]

            # 兼容旧的配置结构
            elif os.path.exists('rooms_config.json'):
                with open('rooms_config.json', 'r', encoding='utf-8') as f:
                    rooms_config = json.load(f)
                room_config = rooms_config.get(self.live_id, {})
                if section in room_config:
                    return room_config[section]
        except Exception as e:
            print(f"❌ 读取房间配置失败: {e}")

        # 如果房间配置中没有，回退到全局配置
        global_config = self.config.get(section, default_value)
        return global_config if global_config is not None else default_value

    def _status_monitor_loop(self):
        """状态监控循环"""
        while True:
            try:
                # 动态调整监控间隔：根据队列状态智能调整
                with self.queue_lock:
                    queue_length = len(self.message_queue)

                if queue_length > 30:
                    monitor_interval = 0.3  # 队列严重积压时高频监控，快速检测设备空闲
                elif queue_length > 15:
                    monitor_interval = 0.5  # 队列较多时加快监控
                elif queue_length > 5:
                    monitor_interval = 0.8  # 队列中等时适度加快
                else:
                    monitor_interval = self.status_monitor_interval  # 队列较少时正常间隔

                time.sleep(monitor_interval)

                # 检查设备状态变化
                current_status = self._get_device_status()
                current_device_state = current_status.get('device_state', 'unknown')

                # 如果状态从 speaking 变为 listening/idle，处理队列
                if (self.last_device_state == 'speaking' and
                    current_device_state in ['idle', 'listening']):
                    # 使用日志系统的重复过滤功能，减少重复输出
                    self.logger.log(f"🔄 设备状态变化: {self.last_device_state} -> {current_device_state}")
                    self.logger.log("📤 开始处理消息队列...")
                    self._process_message_queue()

                # 如果状态变为 listening，重置防冷场计时器
                if (self.last_device_state != 'listening' and current_device_state == 'listening'):
                    current_time = time.time()
                    self.listening_start_time = current_time
                    print(f"🎧 设备进入listening状态，重置防冷场计时器")

                # 检查防冷场功能
                self._check_anti_silence(current_device_state)

                # 检查定时插播功能（独立于消息触发）
                self._check_scheduled_broadcast()

                # 定期清理spoken_users集合，防止内存泄漏
                self._cleanup_spoken_users()

                # 更新状态缓存
                self.last_device_state = current_device_state
                self.last_status_check = time.time()

            except Exception as e:
                print(f"❌ 状态监控异常: {e}")
                import traceback
                print(f"📋 异常详情: {traceback.format_exc()}")
                time.sleep(self.status_monitor_interval)
                # 重置状态缓存，避免异常状态持续
                if hasattr(self, '_status_cache'):
                    delattr(self, '_status_cache')
                if hasattr(self, '_status_cache_time'):
                    delattr(self, '_status_cache_time')

    def _start_config_reload_monitor(self):
        """启动配置重载监控线程"""
        def config_reload_monitor():
            last_reload_time = 0

            while True:
                try:
                    time.sleep(30)  # 每30秒检查一次

                    # 检查房间配置文件的修改时间（支持新旧格式）
                    config_file = f'config/rooms/{self.live_id}.json'
                    if os.path.exists(config_file):
                        file_mtime = os.path.getmtime(config_file)
                    elif os.path.exists('rooms_config.json'):
                        config_file = 'rooms_config.json'
                        file_mtime = os.path.getmtime(config_file)
                    else:
                        continue  # 没有配置文件，跳过检查

                    if file_mtime > last_reload_time:
                        print(f"🔄 检测到配置文件更新，重新加载配置...")
                        self._reload_config()
                        last_reload_time = file_mtime

                except Exception as e:
                    print(f"❌ 配置重载监控异常: {e}")

        config_thread = threading.Thread(target=config_reload_monitor, daemon=True)
        config_thread.start()
        print("🔄 配置重载监控已启动")

    def _reload_config(self):
        """重新加载配置（支持新旧配置格式）"""
        try:
            # 优先使用新的配置结构
            room_config_path = f'config/rooms/{self.live_id}.json'
            if os.path.exists(room_config_path):
                with open(room_config_path, 'r', encoding='utf-8') as f:
                    room_config = json.load(f)

                # 更新配置
                self._update_config_from_new_format(room_config)
                print(f"🔄 已重新加载房间 {self.live_id} 的配置（新格式）")

            # 兼容旧的配置结构
            elif os.path.exists('rooms_config.json'):
                with open('rooms_config.json', 'r', encoding='utf-8') as f:
                    rooms_config = json.load(f)

                # 获取当前房间的配置
                room_config = rooms_config.get(self.live_id, {})

                # 更新冷场回复配置
                anti_silence = room_config.get('anti_silence', {})

                # 无论是否有配置都要更新（使用默认值）
                old_templates = len(self.topic_templates)
                self.topic_templates = anti_silence.get('templates', self._get_default_templates()['topics'])
                new_templates = len(self.topic_templates)

                self._log_config_update("冷场回复模板", old_templates, new_templates)

                self.anti_silence_enabled = anti_silence.get('enabled', True)
                self.anti_silence_delay = anti_silence.get('delay_seconds', 3)
                self.anti_silence_cooldown = anti_silence.get('cooldown_seconds', 10)

                print(f"🎭 冷场回复状态: {'启用' if self.anti_silence_enabled else '禁用'}")
                print(f"🎭 冷场回复延迟: {self.anti_silence_delay}秒")
                print(f"🎭 冷场回复冷却: {self.anti_silence_cooldown}秒")
                print(f"🎭 房间配置路径: rooms_config['{self.live_id}']['anti_silence']")
                print(f"🎭 找到配置: {bool(anti_silence)}")

                # 更新欢迎消息配置
                welcome = room_config.get('welcome_message', {})
                self.welcome_enabled = welcome.get('enabled', True)
                self.welcome_cooldown = welcome.get('cooldown_seconds', 0)
                # 修复空模板问题：如果模板为空，使用默认模板
                templates = welcome.get('templates', [])
                if not templates:  # 如果模板为空或不存在，使用默认模板
                    templates = [
                        "欢迎{user_name}进入直播间！",
                        "欢迎{user_name}来到直播间，很高兴见到你！",
                        "{user_name}欢迎你！有什么想聊的吗？"
                    ]
                self.welcome_templates = templates

                # 更新防刷屏配置
                anti_spam = room_config.get('anti_spam', {})
                # 重新初始化防刷屏过滤器
                self.anti_spam_filter = AntiSpamFilter(anti_spam)

                # 更新自定义回复配置
                self.custom_reply_manager.load_config()

                # 更新防冷场配置
                anti_silence = room_config.get('anti_silence', {})
                self.anti_silence_enabled = anti_silence.get('enabled', True)
                self.anti_silence_delay = anti_silence.get('delay_seconds', 3)
                self.anti_silence_cooldown = anti_silence.get('cooldown_seconds', 10)
                self.max_proactive_attempts = anti_silence.get('max_proactive_attempts', 2)

                # 更新点赞互动配置
                like_interaction = room_config.get('like_interaction', {})
                self.like_interaction_enabled = like_interaction.get('enabled', True)
                self.like_interaction_cooldown = like_interaction.get('cooldown_seconds', 60)
                self.first_like_threshold = like_interaction.get('first_like_threshold', 1)
                self.subsequent_like_threshold = like_interaction.get('subsequent_like_threshold', 100)

                # 更新点赞互动模板
                old_like_templates = len(self.like_templates)
                # 修复空模板问题：如果模板为空，使用默认模板
                templates = like_interaction.get('templates', [])
                if not templates:  # 如果模板为空或不存在，使用默认模板
                    templates = self._get_default_templates()['like_responses']
                self.like_templates = templates
                new_like_templates = len(self.like_templates)

                self._log_config_update("点赞互动模板", old_like_templates, new_like_templates)

                # 更新关注互动配置
                follow_interaction = room_config.get('follow_interaction', {})
                self.follow_interaction_enabled = follow_interaction.get('enabled', True)

                # 更新关注互动模板
                old_follow_templates = len(self.follow_response_templates)
                # 修复空模板问题：如果模板为空，使用默认模板
                templates = follow_interaction.get('response_templates', [])
                if not templates:  # 如果模板为空或不存在，使用默认模板
                    templates = self._get_default_templates()['follow_templates']
                self.follow_response_templates = templates
                new_follow_templates = len(self.follow_response_templates)

                self._log_config_update("关注互动模板", old_follow_templates, new_follow_templates)

                print(f"💖 关注互动状态: {'启用' if self.follow_interaction_enabled else '禁用'}")
                print(f"💖 关注互动模板数量: {len(self.follow_response_templates)}")

                # 显示当前使用的关注模板（调试用）
                if self.follow_response_templates:
                    print(f"💖 当前关注模板示例: {self.follow_response_templates[0][:50]}...")
                else:
                    print(f"⚠️ 警告：关注模板为空，将使用默认模板")

                # 更新礼物互动配置
                gift_priority = room_config.get('gift_priority', {})
                self.gift_priority_enabled = gift_priority.get('enabled', True)

                # 更新礼物回复模板
                old_gift_templates = len(self.gift_response_templates)
                self.gift_response_templates = gift_priority.get('gift_response_templates', [
                    "哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧"
                ])
                new_gift_templates = len(self.gift_response_templates)

                self._log_config_update("礼物回复模板", old_gift_templates, new_gift_templates)

                # 更新礼物权重配置
                self.gift_weights = gift_priority.get('gift_weights', {
                    "玫瑰": MessageWeight.GIFT_BASIC,
                    "小心心": MessageWeight.GIFT_BASIC,
                    "跑车": MessageWeight.GIFT_CHEAP,
                    "火箭": MessageWeight.GIFT_MEDIUM,
                    "嘉年华": MessageWeight.GIFT_EXPENSIVE,
                    "默认": MessageWeight.GIFT_BASIC
                })

                # 更新定时插播配置
                scheduled_broadcast = room_config.get('scheduled_broadcast', {})
                self.scheduled_broadcast_enabled = scheduled_broadcast.get('enabled', True)
                self.broadcast_interval = scheduled_broadcast.get('interval_minutes', 10)

                # 更新定时插播模板
                old_broadcast_templates = len(self.broadcast_templates)
                self.broadcast_templates = scheduled_broadcast.get('templates', self._get_default_templates()['broadcast_templates'])
                new_broadcast_templates = len(self.broadcast_templates)

                self._log_config_update("定时插播模板", old_broadcast_templates, new_broadcast_templates)

                print(f"📺 定时插播状态: {'启用' if self.scheduled_broadcast_enabled else '禁用'}")
                print(f"📺 定时插播间隔: {self.broadcast_interval}分钟")
                print(f"📺 定时插播模板数量: {len(self.broadcast_templates)}")

                # 显示当前使用的定时插播模板（调试用）
                if self.broadcast_templates:
                    print(f"📺 当前插播模板示例: {self.broadcast_templates[0][:50]}...")
                else:
                    print(f"⚠️ 警告：定时插播模板为空，将使用默认模板")

                # 更新定时插播配置（旧格式兼容）
                scheduled_broadcast = room_config.get('scheduled_broadcast', {})
                self.scheduled_broadcast_enabled = scheduled_broadcast.get('enabled', True)
                self.broadcast_interval = scheduled_broadcast.get('interval_minutes', 10)

                # 更新定时插播模板
                old_broadcast_templates = len(self.broadcast_templates)
                self.broadcast_templates = scheduled_broadcast.get('templates', self._get_default_templates()['broadcast_templates'])
                new_broadcast_templates = len(self.broadcast_templates)

                self._log_config_update("定时插播模板", old_broadcast_templates, new_broadcast_templates)

                print(f"📺 定时插播状态: {'启用' if self.scheduled_broadcast_enabled else '禁用'}")
                print(f"📺 定时插播间隔: {self.broadcast_interval}分钟")
                print(f"📺 定时插播模板数量: {len(self.broadcast_templates)}")

                # 显示当前使用的定时插播模板（调试用）
                if self.broadcast_templates:
                    print(f"📺 当前插播模板示例: {self.broadcast_templates[0][:50]}...")
                else:
                    print(f"⚠️ 警告：定时插播模板为空，将使用默认模板")

                # 更新互动游戏配置
                interactive_games = room_config.get('interactive_games', {})
                self.interactive_games_enabled = interactive_games.get('enabled', True)
                self.game_cooldown = interactive_games.get('cooldown_seconds', 30)

                print("✅ 配置重载完成")
                print(f"📋 房间 {self.live_id} 配置状态:")
                print(f"   🎭 冷场回复: {'启用' if self.anti_silence_enabled else '禁用'}")
                print(f"   👋 欢迎消息: {'启用' if self.welcome_enabled else '禁用'}")
                # print(f"   🚫 防刷屏: {'启用' if self.anti_spam_enabled else '禁用'}")  # 防刷屏功能已集成到过滤器中
                print(f"   👍 点赞互动: {'启用' if self.like_interaction_enabled else '禁用'}")
                print(f"   ❤️ 关注互动: {'启用' if self.follow_interaction_enabled else '禁用'}")
                print(f"   🎁 礼物互动: {'启用' if self.gift_priority_enabled else '禁用'}")
                print(f"   📺 定时插播: {'启用' if self.scheduled_broadcast_enabled else '禁用'}")
                print(f"   🎮 互动游戏: {'启用' if self.interactive_games_enabled else '禁用'}")

        except Exception as e:
            print(f"❌ 配置重载失败: {e}")
            import traceback
            print(f"📋 异常详情: {traceback.format_exc()}")
            print(f"⚠️ 将继续使用当前配置，不影响系统运行")

    def _update_config_from_new_format(self, room_config: dict):
        """从新格式配置更新设置"""
        # 更新冷场回复配置
        anti_silence = room_config.get('anti_silence', {})

        # 无论是否有配置都要更新（使用默认值）
        old_templates = len(self.topic_templates)
        self.topic_templates = anti_silence.get('templates', self._get_default_templates()['topics'])
        new_templates = len(self.topic_templates)

        self._log_config_update("冷场回复模板", old_templates, new_templates)

        self.anti_silence_enabled = anti_silence.get('enabled', True)
        self.anti_silence_delay = anti_silence.get('delay_seconds', 3)
        self.anti_silence_cooldown = anti_silence.get('cooldown_seconds', 10)

        print(f"🎭 冷场回复状态: {'启用' if self.anti_silence_enabled else '禁用'}")
        print(f"🎭 冷场回复延迟: {self.anti_silence_delay}秒")
        print(f"🎭 冷场回复冷却: {self.anti_silence_cooldown}秒")
        print(f"🎭 房间配置路径: config/rooms/{self.live_id}.json['anti_silence']")

    def _debug_anti_silence_config(self):
        """调试冷场回复配置"""
        print(f"🎭 === 冷场回复配置状态 ===")
        print(f"🎭 启用状态: {self.anti_silence_enabled}")
        print(f"🎭 延迟时间: {self.anti_silence_delay}秒")
        print(f"🎭 冷却时间: {self.anti_silence_cooldown}秒")
        print(f"🎭 模板数量: {len(self.topic_templates)}条")
        print(f"🎭 listening开始时间: {self.listening_start_time}")
        print(f"🎭 上次冷场时间: {self.last_anti_silence_time}")
        print(f"🎭 是否收到过消息: {self.has_received_message}")
        print(f"🎭 上次消息时间: {self.last_message_time}")
        print(f"🎭 ========================")

    def _get_device_status(self):
        """获取设备状态 - 多方案优化版本"""
        # 状态缓存机制：避免频繁查询
        current_time = time.time()
        if hasattr(self, '_status_cache') and hasattr(self, '_status_cache_time'):
            if current_time - self._status_cache_time < 1.0:  # 1秒内使用缓存
                return self._status_cache

        try:
            status = self._fetch_device_status()
            # 缓存状态
            self._status_cache = status
            self._status_cache_time = current_time
            return status
        except Exception as e:
            print(f"❌ 获取设备状态失败: {e}")
            return {"device_state": "error", "connected": False, "can_speak": False}

    def _fetch_device_status(self):
        """实际获取设备状态的方法"""
        # 方式1：优先读取runtime_data.json文件（最快，适合高频调用）
        if os.path.exists('runtime_data.json'):
            try:
                with open('runtime_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    connected_devices = data.get('connected_devices', {})

                    if self.target_device_id in connected_devices:
                        device_info = connected_devices[self.target_device_id]
                        device_state = device_info.get("device_state", "unknown")
                        return {
                            "device_state": device_state,
                            "connected": True,
                            "can_speak": device_state in ['idle', 'listening'],
                            "source": "runtime_data"
                        }
            except Exception as e:
                print(f"⚠️ 读取runtime_data.json失败: {e}")

        # 方式2：通过HTTP API获取（备用方案）
        try:
            import requests
            url = "http://localhost:15008/api/internal/devices"
            response = requests.get(url, timeout=2)
            response_data = response.json()

            if response_data.get("success"):
                devices = response_data.get("devices", [])
                for device in devices:
                    if device.get("device_id") == self.target_device_id:
                        return {
                            "device_state": device.get("device_state", "unknown"),
                            "connected": device.get("online", False),
                            "can_speak": device.get("can_speak", False),
                            "source": "http_api"
                        }
        except Exception as e:
            print(f"⚠️ HTTP API获取失败: {e}")

        return {"device_state": "offline", "connected": False, "can_speak": False, "source": "fallback"}

    def _can_send_message(self):
        """检查是否可以发送消息"""
        status = self._get_device_status()
        device_state = status.get("device_state", "unknown")
        connected = status.get("connected", False)

        if not connected:
            return {
                "can_send": False,
                "reason": f"设备 {self.target_device_id} 未连接",
                "device_state": device_state
            }

        if device_state in ['idle', 'listening']:
            return {
                "can_send": True,
                "device_state": device_state
            }
        else:
            return {
                "can_send": False,
                "reason": f"设备状态为 {device_state}，不允许发送消息",
                "device_state": device_state
            }

    def _check_anti_silence(self, device_state):
        """检查防冷场功能 - 检测listening状态下3秒无消息"""
        if not self.anti_silence_enabled:
            return

        current_time = time.time()

        # 只在设备listening状态时触发防冷场
        if device_state != 'listening':
            return

        # 如果listening_start_time为0，说明还没进入过listening状态
        if self.listening_start_time == 0:
            return

        # 计算两个时间：
        # 1. 距离进入listening状态的时间
        # 2. 距离上次收到消息的时间（如果有消息的话）
        time_since_listening_start = current_time - self.listening_start_time

        # 如果有收到过消息，则使用最近的时间作为基准
        if self.has_received_message:
            time_since_last_message = current_time - self.last_message_time
            # 使用较小的时间（即更近的事件）
            time_since_activity = min(time_since_listening_start, time_since_last_message)
        else:
            # 如果没收到过消息，使用listening开始时间
            time_since_activity = time_since_listening_start

        time_since_last_anti_silence = current_time - self.last_anti_silence_time

        # 添加调试信息
        if time_since_activity >= self.anti_silence_delay - 1:  # 提前1秒开始显示调试信息
            print(f"🎭 冷场检查: 无活动时间={time_since_activity:.1f}s, 需要={self.anti_silence_delay}s, 上次冷场={time_since_last_anti_silence:.1f}s, 冷却={self.anti_silence_cooldown}s")

        # 如果listening状态下3秒无活动，且距离上次防冷场消息超过冷却时间
        if (time_since_activity >= self.anti_silence_delay and
            time_since_last_anti_silence >= self.anti_silence_cooldown):

            print(f"🎭 检测到冷场：listening状态 {time_since_activity:.1f}秒无活动，触发防冷场消息")
            self._send_anti_silence_message()

    def _send_anti_silence_message(self):
        """发送防冷场消息 - 支持个性化回复最后发言用户"""
        try:
            # 获取防冷场配置
            anti_silence_config = self.get_room_config('anti_silence', {})

            # 尝试个性化回复最后发言用户
            message_content = self._get_personalized_anti_silence_message(anti_silence_config)

            if not message_content:
                # 如果没有个性化消息，使用通用模板
                message_content = self._get_general_anti_silence_message(anti_silence_config)

            # 直接使用消息内容，不添加复杂格式
            # 限制长度避免设备解析问题
            max_length = 50  # 适中的长度限制

            if len(message_content) <= max_length:
                full_message = message_content
            else:
                # 如果消息太长，截断
                full_message = message_content[:max_length-3] + "..."

            print(f"🎭 发送防冷场消息: {message_content}")
            print(f"🎭 最终格式: {full_message} (长度:{len(full_message)})")

            current_time = time.time()

            # 如果启用了状态检查，先检查设备状态
            if self.status_check_enabled:
                can_send_result = self._can_send_message()
                device_state = can_send_result.get('device_state', 'unknown')

                # 更新设备状态缓存
                self.last_device_state = device_state
                self.last_status_check = current_time

                if not can_send_result.get('can_send'):
                    reason = can_send_result.get('reason', '未知原因')

                    print(f"🚫 防冷场消息被阻止发送: {reason}")
                    print(f"   设备状态: {device_state}")
                    print(f"   防冷场内容: {message_content}")

                    # 如果是 speaking 状态且启用了消息队列，则加入优先队列
                    if device_state == 'speaking' and self.message_queue_enabled:
                        if self._add_to_priority_queue(full_message, "系统", "防冷场", "anti_silence", MessageWeight.ANTI_SILENCE):
                            print(f"📦 防冷场消息已加入队列等待处理")
                        else:
                            print(f"❌ 防冷场消息加入队列失败")

                    # 更新时间戳（即使没发送成功，也要更新，避免频繁触发）
                    self.last_anti_silence_time = current_time
                    return

            # 统一使用优先队列机制，防冷场消息权重为最低优先级
            print(f"📦 防冷场消息加入优先队列（权重:{MessageWeight.ANTI_SILENCE}-最低优先级）")
            if self._add_to_priority_queue(full_message, "系统", "防冷场", "anti_silence", MessageWeight.ANTI_SILENCE):
                print(f"📦 防冷场消息已加入队列")
                # 如果设备空闲，立即触发队列处理
                if can_send_result.get('can_send'):
                    self._process_message_queue()
            else:
                print(f"❌ 防冷场消息加入队列失败")

            # 更新时间戳
            self.last_anti_silence_time = current_time

        except Exception as e:
            print(f"❌ 发送防冷场消息异常: {e}")

    def _get_personalized_anti_silence_message(self, anti_silence_config):
        """获取个性化防冷场消息（针对最后发言用户）"""
        # 检查是否有最后发言用户
        if not self.last_speaker:
            return None

        user_id = self.last_speaker['user_id']
        user_name = self.last_speaker['user_name']

        # 检查对该用户的主动沟通次数
        current_count = self.user_proactive_count.get(user_id, 0)
        if current_count >= self.max_proactive_attempts:
            print(f"💬 用户 {user_name} 已达到最大主动沟通次数 ({current_count}/{self.max_proactive_attempts})")
            return None

        # 获取个性化模板
        personalized_templates = anti_silence_config.get('personalized_templates', [])
        if not personalized_templates:
            # 如果没有配置个性化模板，使用默认个性化模板
            personalized_templates = [
                "{user_name}，刚才聊得挺开心的，还有什么想聊的吗？",
                "嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！",
                "{user_name}，直播间有点安静了，来活跃一下气氛吧！",
                "{user_name}，有什么问题或想法都可以说出来哦！"
            ]

        # 随机选择模板并替换用户名
        template = random.choice(personalized_templates)
        message = template.format(user_name=user_name)

        # 更新用户主动沟通次数
        self.user_proactive_count[user_id] = current_count + 1

        print(f"💬 生成个性化防冷场消息: 目标用户={user_name}, 次数={self.user_proactive_count[user_id]}/{self.max_proactive_attempts}")
        return message

    def _get_general_anti_silence_message(self, anti_silence_config):
        """获取通用防冷场消息"""
        # 获取通用模板
        general_templates = anti_silence_config.get('templates', [])
        if not general_templates:
            print(f"⚠️ 防冷场模板为空，使用默认模板")
            general_templates = self._get_default_templates()['topics']

        # 随机选择一个话题
        message = random.choice(general_templates)
        print(f"💬 生成通用防冷场消息")
        return message

    def _add_to_queue(self, reply_content: str, user_name: str, original_content: str, message_type: str = "user", user_id: str = None):
        """添加消息到队列"""
        if not self.message_queue_enabled:
            return False

        current_time = time.time()
        message_item = {
            'reply_content': reply_content,
            'user_name': user_name,
            'original_content': original_content,
            'timestamp': current_time,
            'time_str': time.strftime('%H:%M:%S', time.localtime(current_time)),
            'message_type': message_type,  # 新增：消息类型 (user/anti_silence/like)
            'user_id': user_id  # 添加user_id字段
        }

        with self.queue_lock:
            # 1. 如果是用户消息，清理队列中的冷场消息
            if message_type == "user":
                self._cleanup_anti_silence_messages()

            # 2. 只有在队列满了的情况下才清理过期消息
            if len(self.message_queue) >= self.message_queue_max_size:
                removed_count = self._cleanup_expired_messages()
                if removed_count > 0:
                    print(f"🧹 队列满时清理了 {removed_count} 条过期消息，当前队列大小: {len(self.message_queue)}")

            # 3. 只有在队列仍然满的情况下，才进行激进的时效性控制
            if len(self.message_queue) >= self.message_queue_max_size and self.message_queue:
                oldest_msg = self.message_queue[0]
                oldest_age = current_time - oldest_msg['timestamp']
                if oldest_age > 45:  # 45秒阈值，只有在队列满且消息过期时才清理
                    clear_count = len(self.message_queue) // 2 + 1  # 清理一半+1
                    removed_count = 0

                    # 优先清理普通消息，保护优先消息
                    i = 0
                    while i < len(self.message_queue) and removed_count < clear_count:
                        if not self.message_queue[i].get('priority', False):
                            removed_msg = self.message_queue.pop(i)
                            self.logger.debug(f"🧹 队列满时清理过期普通消息: {removed_msg['user_name']}: {removed_msg['original_content'][:15]}... (已过期 {current_time - removed_msg['timestamp']:.1f}秒)")
                            removed_count += 1
                        else:
                            i += 1

                    # 如果还需要清理更多消息，才清理优先消息
                    while removed_count < clear_count and self.message_queue:
                        removed_msg = self.message_queue.pop(0)
                        self.logger.debug(f"🧹 队列满时清理过期优先消息: {removed_msg['user_name']}: {removed_msg['original_content'][:15]}... (已过期 {current_time - removed_msg['timestamp']:.1f}秒)")
                        removed_count += 1

                    if removed_count > 0:
                        print(f"🧹 队列满时激进清理了 {removed_count} 条过期消息")

            # 4. 如果队列仍然过大，智能移除消息（保护高权重优先消息）
            while len(self.message_queue) >= self.message_queue_max_size:
                # 检查新消息的权重
                new_msg_weight = message_item.get('gift_weight', 0) if message_item.get('priority', False) else 0

                # 找到可以被替换的消息（权重低于或等于新消息的）
                replaceable_index = self._find_replaceable_message(new_msg_weight)

                if replaceable_index is not None:
                    removed_msg = self.message_queue.pop(replaceable_index)
                    priority_info = f"权重:{removed_msg.get('gift_weight', 0)}" if removed_msg.get('priority', False) else "普通"
                    self.logger.log(f"⚠️ 队列已满，移除可替换消息({priority_info}): {removed_msg['user_name']}: {removed_msg['original_content'][:20]}...")
                else:
                    # 如果没有可替换的消息，拒绝新消息
                    self.logger.log(f"🚫 队列已满且无法替换，拒绝新消息: {user_name}: {original_content[:20]}...")
                    return False

            # 5. 添加新消息到队列末尾（最新消息）
            self.message_queue.append(message_item)

            # 6. 对整个队列按优先级排序
            self._sort_queue_by_priority()
            queue_size = len(self.message_queue)

            # 6. 显示队列状态和消息年龄
            if queue_size > 0:
                oldest_msg = self.message_queue[0]
                oldest_age = current_time - oldest_msg['timestamp']
                self.logger.log(f"📦 消息已加入队列 [{queue_size}/{self.message_queue_max_size}]: {user_name}: {original_content[:20]}...")
                self.logger.log(f"   📊 队列状态: 最旧消息 {oldest_age:.1f}秒前, 最大存活 {self.message_queue_max_age}秒")
            else:
                self.logger.log(f"📦 消息已加入队列 [{queue_size}/{self.message_queue_max_size}]: {user_name}: {original_content[:20]}...")

        return True

    def _cleanup_expired_messages(self):
        """清理过期消息（需要在锁内调用）- 只在队列满时调用"""
        if not self.message_queue:
            return

        current_time = time.time()
        original_count = len(self.message_queue)

        # 过滤掉过期消息
        self.message_queue = [
            msg for msg in self.message_queue
            if current_time - msg['timestamp'] <= self.message_queue_max_age
        ]

        removed_count = original_count - len(self.message_queue)
        if removed_count > 0:
            print(f"🧹 队列满时清理了 {removed_count} 条过期消息")

        return removed_count

    def _cleanup_anti_silence_messages(self):
        """清理队列中的冷场消息（需要在锁内调用）"""
        if not self.message_queue:
            return

        # 过滤掉冷场消息
        anti_silence_messages = [
            msg for msg in self.message_queue
            if msg.get('message_type') == 'anti_silence'
        ]

        self.message_queue = [
            msg for msg in self.message_queue
            if msg.get('message_type') != 'anti_silence'
        ]

        removed_count = len(anti_silence_messages)
        if removed_count > 0:
            print(f"🧹 检测到用户消息，清理了 {removed_count} 条冷场消息:")
            for msg in anti_silence_messages:
                print(f"   - 已清理: {msg['original_content'][:50]}...")
            print(f"💡 原因: 有用户活跃，不再需要冷场消息")

    def _process_message_queue(self):
        """处理消息队列"""
        if not self.message_queue_enabled:
            return

        with self.queue_lock:
            if not self.message_queue:
                print("📦 消息队列为空，无需处理")
                return

            # 复制队列并清空原队列（不在这里清理过期消息，让消息有机会被处理）
            messages_to_process = self.message_queue.copy()
            self.message_queue.clear()

        if not messages_to_process:
            print("📦 清理后队列为空，无需处理")
            return

        self.logger.log(f"📦 处理队列: {len(messages_to_process)}条消息")

        # 分离优先消息和普通消息
        priority_messages = [msg for msg in messages_to_process if msg.get('priority', False)]
        normal_messages = [msg for msg in messages_to_process if not msg.get('priority', False)]

        if priority_messages:
            self.logger.log(f"🔥 发现 {len(priority_messages)} 条优先消息（礼物回复等）")
        if normal_messages:
            self.logger.log(f"📝 发现 {len(normal_messages)} 条普通消息")

        # 将所有消息合并并按权重排序
        all_messages = priority_messages + normal_messages
        # 按权重排序：权重高的在前，相同权重按时间正序（早的先处理）
        def get_sort_key(msg):
            if msg.get('priority', False):
                # 优先消息：按权重降序，权重相同按时间升序
                return (0, -msg.get('gift_weight', 1), msg.get('timestamp', 0))
            else:
                # 普通消息：权重为负数，按时间升序
                return (1, 0, msg.get('timestamp', 0))

        all_messages.sort(key=get_sort_key)

        messages_to_process = all_messages

        processed_count = 0
        # 动态调整处理数量 - 根据队列积压情况
        queue_length = len(messages_to_process)
        if queue_length > 15:
            max_process_count = 3  # 积压严重时，批量处理
        elif queue_length > 8:
            max_process_count = 2  # 中等积压时，处理2条
        else:
            max_process_count = 1  # 正常情况，处理1条

        self.logger.log(f"📊 队列状态: {queue_length}条消息，本次处理上限: {max_process_count}条")

        for i, msg in enumerate(messages_to_process, 1):
            # 检查消息是否已过期
            current_time = time.time()
            msg_age = current_time - msg['timestamp']

            if msg_age > self.message_queue_max_age:
                print(f"⏰ 跳过过期消息 [{i}/{len(messages_to_process)}]: {msg['user_name']}: {msg['original_content'][:20]}... (已过期 {msg_age:.1f}秒)")
                continue

            if processed_count >= max_process_count:
                remaining_count = len(messages_to_process) - i
                print(f"⏸️ 已发送 {processed_count} 条消息，剩余 {remaining_count} 条消息将在下次处理")
                # 将未处理的消息重新加入队列
                with self.queue_lock:
                    for remaining_msg in messages_to_process[i:]:
                        # 检查消息是否还未过期
                        if time.time() - remaining_msg['timestamp'] <= self.message_queue_max_age:
                            self.message_queue.append(remaining_msg)
                break
            try:
                user_short = msg['user_name'][:8]
                content_short = msg['original_content'][:15]
                priority_flag = "🔥" if msg.get('priority', False) else "📤"
                priority_text = "[优先]" if msg.get('priority', False) else ""
                self.logger.log(f"{priority_flag} 队列[{i}/{len(messages_to_process)}] {user_short}: {content_short}...{priority_text} ({msg_age:.1f}s)")

                # 特别处理不同类型的消息
                msg_type = msg.get('message_type', 'unknown')
                if msg_type == 'gift':
                    self.logger.log(f"🎁 正在处理礼物消息: {msg['reply_content']}")
                elif msg_type == 'user':
                    self.logger.log(f"💬 正在处理用户消息: {user_short}")
                elif msg_type == 'welcome':
                    self.logger.log(f"👋 正在处理欢迎消息: {user_short}")
                elif msg_type == 'broadcast':
                    self.logger.log(f"📺 正在处理插播消息")
                elif msg_type == 'anti_silence':
                    self.logger.log(f"🤖 正在处理防冷场消息")
                elif msg_type == 'like':
                    self.logger.log(f"👍 正在处理点赞消息: {user_short}")
                elif msg_type == 'follow':
                    self.logger.log(f"❤️ 正在处理关注消息: {user_short}")
                else:
                    self.logger.log(f"❓ 正在处理未知类型消息({msg_type}): {user_short}")

                # 检查回复内容是否为空
                if not msg.get('reply_content') or not msg['reply_content'].strip():
                    self.logger.log(f"❌ 跳过空消息: {user_short} (类型:{msg_type})")
                    continue

                # 等待设备状态允许发送，或检查是否需要强制发送
                should_force_send = self._should_force_send_message(msg)

                # 动态调整等待超时：根据队列长度和消息优先级
                if msg.get('priority', False) and msg.get('gift_weight', 0) >= MessageWeight.GIFT_MEDIUM:
                    wait_timeout = 10  # 高价值礼物消息等待更久
                elif queue_length > 30:
                    wait_timeout = 3   # 队列积压时快速处理
                else:
                    wait_timeout = 5   # 正常情况下的等待时间

                if not should_force_send and not self._wait_for_device_ready(timeout=wait_timeout):
                    print(f"⚠️ 等待超时({wait_timeout}s)，跳过: {user_short}")
                    continue

                # 双重检查设备状态，防止消息重叠
                if not should_force_send:
                    device_status = self._get_device_status()
                    current_state = device_status.get('device_state', 'unknown')
                    # 修复：允许 idle 和 listening 状态发送消息
                    if current_state not in ['idle', 'listening']:
                        print(f"⚠️ 状态异常({current_state})，跳过: {user_short}")
                        continue

                if should_force_send:
                    print(f"🚨 强制发送: {user_short}")

                # 发送消息（强制发送时跳过状态检查）
                if should_force_send:
                    result = self._send_websocket_message(msg['reply_content'], force=True)
                else:
                    result = self._send_websocket_message(msg['reply_content'], force=False)
                    
                if result.get('success'):
                    self.logger.log(f"✅ 队列消息发送成功: {user_short}")
                    # 如果是聊天或礼物消息，记录互动
                    if msg.get('message_type') in ['chat', 'gift']:
                        # 使用消息中已有的user_id，如果没有则跳过记录
                        user_id = msg.get('user_id')
                        if user_id:
                            # 根据消息类型调用相应的记录方法
                            if msg.get('message_type') == 'chat':
                                leaderboard_stats.record_chat_interaction(
                                    user_id, msg['user_name'], self.live_id,
                                    msg.get('content', ''), bot_replied=True
                                )
                            elif msg.get('message_type') == 'gift':
                                leaderboard_stats.record_gift(
                                    user_id, msg['user_name'], self.live_id,
                                    msg.get('gift_name', '未知礼物'), msg.get('gift_count', 1)
                                )
                        else:
                            self.logger.log(f"⚠️ 消息缺少user_id，跳过互动记录: {msg['user_name']}")
                    processed_count += 1
                else:
                    self.logger.log(f"❌ 队列消息发送失败: {user_short} - {result.get('message')}")
                    # 单次失败，重新加入队列末尾等待下次处理
                    print(f"🔄 消息重新入队等待下次处理: {user_short}")
                    with self.queue_lock:
                        # 降低优先级重新入队（避免一直失败的消息阻塞队列）
                        msg['retry_count'] = msg.get('retry_count', 0) + 1
                        if msg['retry_count'] <= 2:  # 最多重新入队2次
                            self.message_queue.append(msg)
                            print(f"📦 消息已重新入队 (重试次数: {msg['retry_count']}/2)")
                        else:
                            print(f"💀 消息重试次数超限，彻底放弃: {user_short}")

                    # 发送失败时也要等待一下，避免快速重试
                    time.sleep(0.5)
            except Exception as e:
                print(f"❌ 处理队列消息异常: {e}")
                print(f"   消息: {msg['user_name']}: {msg['original_content'][:20]}...")
                import traceback
                print(f"📋 异常详情: {traceback.format_exc()}")

                # 异常恢复：将消息标记为失败并重新入队（有限次数）
                with self.queue_lock:
                    msg['error_count'] = msg.get('error_count', 0) + 1
                    if msg['error_count'] <= 1:  # 最多重试1次
                        self.message_queue.append(msg)
                        print(f"🔄 异常消息已重新入队 (错误次数: {msg['error_count']}/1)")
                    else:
                        print(f"💀 消息错误次数超限，彻底放弃: {msg['user_name']}")

                # 继续处理下一条消息，不让单个异常阻塞整个队列
                continue


        print(f"✅ 队列处理完成，共处理 {len(messages_to_process)} 条消息")

    def _wait_for_device_ready(self, timeout=30):
        """等待设备状态允许发送消息"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # 检查设备状态
                can_send_result = self._can_send_message()
                if can_send_result.get('can_send'):
                    device_state = can_send_result.get('device_state', 'unknown')
                    print(f"✅ 设备就绪，状态: {device_state}")
                    return True

                device_state = can_send_result.get('device_state', 'unknown')
                print(f"⏳ 等待设备就绪，当前状态: {device_state}")
                time.sleep(1)  # 每秒检查一次

            except Exception as e:
                print(f"❌ 检查设备状态异常: {e}")
                time.sleep(1)

        print(f"⏰ 等待设备就绪超时 ({timeout}秒)")
        return False

    def _wait_for_device_finish_speaking(self, timeout=15):
        """等待设备说完当前消息"""
        start_time = time.time()
        last_state = None
        stable_count = 0  # 状态稳定计数
        speaking_detected = False  # 是否检测到设备开始说话

        # 先等待一小段时间，让设备开始说话
        time.sleep(0.8)

        while time.time() - start_time < timeout:
            try:
                # 检查设备状态
                status = self._get_device_status()
                device_state = status.get('device_state', 'unknown')

                # 检测到设备开始说话
                if device_state == 'speaking':
                    speaking_detected = True
                    stable_count = 0
                    last_state = device_state
                    self.logger.debug("🗣️ 设备正在说话...")
                    time.sleep(0.5)
                    continue

                # 检查状态是否稳定
                if device_state == last_state:
                    stable_count += 1
                else:
                    stable_count = 0
                    last_state = device_state

                # 如果设备状态为idle或listening且稳定超过3次检查，认为完成
                if device_state in ['idle', 'listening']:
                    if speaking_detected and stable_count >= 3:
                        # print(f"✅ 设备完成说话，状态稳定: {device_state}")  # 简化日志
                        return True
                    elif not speaking_detected and stable_count >= 2:
                        # 如果没有检测到speaking状态，可能消息很短，直接完成了
                        # print(f"✅ 设备状态稳定: {device_state} (未检测到speaking)")  # 简化日志
                        return True

                time.sleep(0.3)  # 缩短检查间隔

            except Exception as e:
                print(f"❌ 检查状态异常: {e}")
                time.sleep(0.5)

        print(f"⏰ 等待超时({timeout}s)，状态: {last_state}")
        return False

    def _should_force_send_message(self, message_item):
        """判断是否应该强制发送消息"""
        if not self.force_send_enabled:
            return False

        # 只对用户消息进行强制发送
        if self.force_send_user_only and message_item.get('message_type') != 'user':
            return False

        # 检查消息在队列中的时间
        current_time = time.time()
        message_age = current_time - message_item.get('timestamp', current_time)

        if message_age >= self.force_send_timeout:
            return True

        return False

    def _parseChatMsg(self, payload):
        """重写聊天消息处理，添加过滤功能、状态检查和消息队列"""
        # 调用原始方法处理基本逻辑（不包含ESP32回复）
        from protobuf.douyin import ChatMessage
        message = ChatMessage().parse(payload)
        user_name = message.user.nick_name
        user_id = message.user.id
        content = message.content

        self.logger.log(f"【聊天msg】[{user_id}]{user_name}: {content}")
        self._addMessage('chat', content, user_name, str(user_id))

        # 检查消息时间过滤（跳过程序启动前的历史消息）
        current_time = time.time()
        time_since_start = current_time - self.start_time
        self.logger.log(f"🕐 消息时间检查: 启动后 {time_since_start:.1f}s, 过滤启用: {self.message_time_filter_enabled}")

        # 减少历史消息过滤时间，从5秒改为2秒，因为抖音连接成功后消息应该是实时的
        if self.message_time_filter_enabled and time_since_start < 2:
            self.logger.log(f"⏭️ 跳过历史消息: {user_name}: {content} (启动后 {time_since_start:.1f}s < 2s)")
            return

        # 消息预处理
        original_content = content
        content = self.preprocess_message(content)
        if content != original_content:
            self.logger.log(f"🔧 消息预处理: '{original_content}' → '{content}'")

        # 防刷屏检查 - 提前到最前面，避免记录被屏蔽用户的互动
        spam_result = self.anti_spam_filter.check_spam(str(user_id), user_name, content)
        if spam_result['is_spam']:
            self.logger.warning(f"检测到刷屏行为: {user_name} - {spam_result['reason']}")

            # 如果需要发送警告消息
            if spam_result['action'] == 'warn' and spam_result['warning_message']:
                self.logger.important(f"发送防刷屏警告: {spam_result['warning_message']}")
                # 防刷屏警告加入高优先级队列
                if self._add_to_priority_queue(spam_result['warning_message'], "系统警告", f"防刷屏警告:{user_name}", "anti_spam", MessageWeight.SYSTEM_BROADCAST):
                    self.logger.log(f"🔥 防刷屏警告已加入高优先级队列")
                    # 立即触发队列处理
                    self._process_message_queue()
                else:
                    self.logger.log(f"❌ 防刷屏警告加入队列失败")

            return  # 不处理刷屏消息，也不记录互动统计

        # 更新最后消息时间（用于防冷场检测）
        self.last_message_time = current_time
        self.has_received_message = True
        # 如果当前是listening状态，重置listening计时器
        if self.last_device_state == 'listening':
            self.listening_start_time = current_time

        # 更新最后发言用户信息（用于个性化防冷场）
        self.last_speaker = {
            'user_name': user_name,
            'user_id': str(user_id),
            'time': current_time
        }

        # 记录互动统计（只有通过刷屏检测的消息才记录）
        try:
            from leaderboard_stats import leaderboard_stats
            leaderboard_stats.record_chat_interaction(
                str(user_id),
                user_name,
                self.live_id,
                content,
                bot_replied=True  # 每条有效消息都算成功互动
            )
            print(f"📊 记录聊天互动: {user_name} -> {content[:20]}...")
        except Exception as e:
            print(f"❌ 记录聊天互动失败: {e}")

        # 检查游戏状态是否需要重置
        self._check_game_state_reset(current_time)

        # 检查用户是否有优先权
        user_id_str = str(user_id)
        is_priority_user = user_id_str in self.priority_users
        if is_priority_user:
            remaining_count = self.priority_users[user_id_str]
            self.logger.log(f"🔥 优先用户消息: {user_name} (剩余优先权: {remaining_count})")

            # 减少优先权计数
            self.priority_users[user_id_str] -= 1
            if self.priority_users[user_id_str] <= 0:
                del self.priority_users[user_id_str]
                self.logger.log(f"🔥 {user_name} 优先权已用完")

        # 检查自定义回复
        custom_reply = self.custom_reply_manager.check_and_reply(content, user_name, str(user_id))
        if custom_reply:
            # 自定义回复触发，跳过普通回复处理
            self.logger.log(f"📝 自定义回复触发: {custom_reply['rule_id']}")

            # 加入优先队列
            if self._add_to_priority_queue(
                custom_reply['content'],
                user_name,
                content,
                "custom_reply",
                custom_reply['weight'],
                str(user_id)
            ):
                self.logger.log(f"📝 自定义回复已加入队列")
                # 如果设备空闲，立即触发队列处理
                can_send_result = self._can_send_message()
                if can_send_result.get('can_send'):
                    self._process_message_queue()
            else:
                self.logger.log(f"❌ 自定义回复加入队列失败")

            # 更新最后回复时间
            self.last_reply_time = current_time
            return  # 跳过普通回复处理

        # 如果启用了自动回复且通过过滤器，则处理ESP32回复
        if self.auto_reply_enabled and self.message_filter.should_reply(user_name, str(user_id), content):
            # 检查冷却时间
            current_time = time.time()
            if current_time - self.last_reply_time < self.reply_cooldown:
                print(f"⏳ 回复冷却中，将消息加入队列等待处理: {content}")

                # 清理用户名中的特殊字符，防止ESP32解析错误
                clean_user_name = self.clean_username_for_device(user_name)

                # 生成回复内容
                reply_content = self.reply_template.format(
                    user_name=clean_user_name,
                    user_id=user_id,
                    content=content
                )

                # 判断用户优先级
                user_id_str = str(user_id)
                is_priority_user = user_id_str in self.priority_users and self.priority_users[user_id_str] > 0
                is_new_user = user_id_str not in self.spoken_users

                if is_new_user:
                    # 新用户首次发言，给予最高用户权重
                    user_weight = MessageWeight.NEW_USER
                    self.spoken_users.add(user_id_str)  # 记录该用户已发言
                    print(f"🆕 新用户首次发言（冷却期）: {user_name} (权重:{user_weight})")
                elif is_priority_user:
                    user_weight = MessageWeight.PRIORITY_USER
                    print(f"🔥 优先用户消息（冷却期）: {user_name} (权重:{user_weight})")
                else:
                    user_weight = MessageWeight.NORMAL_USER
                    print(f"📦 普通用户消息（冷却期）: {user_name} (权重:{user_weight})")

                # 将消息加入优先队列等待处理
                if self._add_to_priority_queue(reply_content, user_name, content, "user", user_weight, str(user_id)):
                    print(f"📦 冷却期消息已加入队列等待处理")
                else:
                    print(f"❌ 冷却期消息加入队列失败")

                return

            # 清理用户名中的特殊字符，防止ESP32解析错误
            clean_user_name = self.clean_username_for_device(user_name)

            # 生成回复内容
            reply_content = self.reply_template.format(
                user_name=clean_user_name,
                user_id=user_id,
                content=content
            )

            # 如果启用了状态检查，先检查设备状态
            if self.status_check_enabled:
                can_send_result = self._can_send_message()
                device_state = can_send_result.get('device_state', 'unknown')

                # 更新设备状态缓存
                self.last_device_state = device_state
                self.last_status_check = current_time

                if not can_send_result.get('can_send'):
                    reason = can_send_result.get('reason', '未知原因')

                    self.logger.log(f"🚫 消息被阻止发送: {reason}")
                    self.logger.log(f"   用户消息: {user_name}: {content}")
                    self.logger.log(f"   设备状态: {device_state}")

                    # 设备忙碌时也使用统一的权重分配逻辑
                    # 不直接return，而是继续到统一处理逻辑

            # 统一使用优先队列机制，用户消息权重根据优先权和新用户状态决定
            # 安全处理user_id，避免None或异常值
            if user_id is None or user_id == "":
                user_id_str = f"anonymous_{hash(user_name)}"  # 匿名用户使用用户名哈希
            else:
                user_id_str = str(user_id)

            is_new_user = user_id_str not in self.spoken_users

            if is_new_user:
                # 新用户首次发言，给予最高用户权重
                user_weight = MessageWeight.NEW_USER
                self.spoken_users.add(user_id_str)  # 记录该用户已发言
                print(f"🆕 新用户首次发言: {user_name} (权重:{user_weight})")
            elif is_priority_user:
                user_weight = MessageWeight.PRIORITY_USER
                print(f"🔥 优先用户消息: {user_name} (权重:{user_weight})")
            else:
                user_weight = MessageWeight.NORMAL_USER
                print(f"📦 普通用户消息: {user_name} (权重:{user_weight})")
            if self._add_to_priority_queue(reply_content, user_name, content, "user", user_weight, str(user_id)):
                if is_new_user:
                    print(f"🆕 新用户消息已加入高优先级队列")
                else:
                    print(f"📦 用户消息已加入队列")

                # 如果设备空闲，立即触发队列处理
                if can_send_result.get('can_send'):
                    self._process_message_queue()
            else:
                print(f"❌ 用户消息加入队列失败")

            self.last_reply_time = current_time

    def _parseMemberMsg(self, payload):
        """重写进入消息处理，添加进入欢迎功能"""
        # 调用原始方法处理基本逻辑
        super()._parseMemberMsg(payload)

        # 检查消息时间过滤（跳过程序启动前的历史消息）
        current_time = time.time()
        if self.message_time_filter_enabled and current_time - self.start_time < 2:
            print(f"⏭️ 跳过历史进入消息")
            return

        # 如果启用了进入欢迎功能，则处理欢迎回复
        if self.welcome_enabled:
            from protobuf.douyin import MemberMessage
            message = MemberMessage().parse(payload)
            user_name = message.user.nick_name
            gender = ["女", "男"][message.user.gender]

            print(f"👋 检测到用户进入: {user_name} ({gender})")

            # 检查冷却时间
            current_time = time.time()
            if current_time - self.last_welcome_time < self.welcome_cooldown:
                print(f"⏳ 欢迎消息冷却中，跳过欢迎: {user_name}")
                return

            # 检查是否有欢迎模板
            if not self.welcome_templates:
                print(f"⚠️ 欢迎消息模板为空，跳过欢迎: {user_name}")
                return

            # 随机选择欢迎模板
            welcome_template = random.choice(self.welcome_templates)
            # 清理用户名防止ESP32解析错误
            clean_user_name = self.clean_username_for_device(user_name)
            welcome_message = welcome_template.format(user_name=clean_user_name)

            print(f"👋 准备发送欢迎消息: {welcome_message}")

            # 如果启用了状态检查，先检查设备状态
            if self.status_check_enabled:
                can_send_result = self._can_send_message()
                device_state = can_send_result.get('device_state', 'unknown')

                # 更新设备状态缓存
                self.last_device_state = device_state
                self.last_status_check = current_time

                if not can_send_result.get('can_send'):
                    reason = can_send_result.get('reason', '未知原因')

                    print(f"🚫 欢迎消息被阻止发送: {reason}")
                    print(f"   用户: {user_name}")
                    print(f"   设备状态: {device_state}")
                    print(f"   欢迎内容: {welcome_message}")

                    # 如果是 speaking 状态且启用了消息队列，则加入优先队列
                    if device_state == 'speaking' and self.message_queue_enabled:
                        if self._add_to_priority_queue(welcome_message, user_name, f"进入直播间", "welcome", MessageWeight.WELCOME_MESSAGE):
                            self.logger.log(f"📦 欢迎消息已加入队列等待处理")
                        else:
                            self.logger.log(f"❌ 欢迎消息加入队列失败")

                    # 更新时间戳（即使没发送成功，也要更新，避免频繁触发）
                    self.last_welcome_time = current_time
                    return

            # 统一使用优先队列机制，欢迎消息权重为低优先级
            print(f"📦 欢迎消息加入优先队列（权重:{MessageWeight.WELCOME_MESSAGE}-低优先级）")
            if self._add_to_priority_queue(welcome_message, user_name, "进入直播间", "welcome", MessageWeight.WELCOME_MESSAGE):
                print(f"📦 欢迎消息已加入队列")
                # 如果设备空闲，立即触发队列处理
                if can_send_result.get('can_send'):
                    self._process_message_queue()
            else:
                print(f"❌ 欢迎消息加入队列失败")

            # 更新时间戳
            self.last_welcome_time = current_time

    def _parseSocialMsg(self, payload):
        """重写关注消息处理，添加关注互动功能"""
        # 调用父类方法处理基本逻辑
        super()._parseSocialMsg(payload)

        # 检查消息时间过滤（跳过程序启动前的历史消息）
        current_time = time.time()
        if self.message_time_filter_enabled and current_time - self.start_time < 2:
            self.logger.log(f"⏭️ 跳过历史关注消息")
            return

        # 如果启用了关注互动功能，则处理关注回复
        if self.follow_interaction_enabled:
            from protobuf.douyin import SocialMessage
            message = SocialMessage().parse(payload)
            user_name = message.user.nick_name
            user_id = str(message.user.id)

            self.logger.log(f"💖 检测到用户关注: {user_name}")

            # 给关注用户设置优先权（关注比点赞更重要）
            priority_count = 1  # 关注获得1条优先权
            if user_id in self.priority_users:
                self.priority_users[user_id] += priority_count
            else:
                self.priority_users[user_id] = priority_count
            self.logger.log(f"🔥 {user_name} 关注主播，获得{priority_count}条优先权")

            # 检查是否有关注回复模板
            if not self.follow_response_templates:
                self.logger.log(f"⚠️ 关注回复模板为空，跳过关注回复: {user_name}")
                return

            # 随机选择关注回复模板
            # 清理用户名防止ESP32解析错误
            clean_user_name = self.clean_username_for_device(user_name)
            follow_template = random.choice(self.follow_response_templates)
            follow_response = follow_template.format(user_name=clean_user_name)

            self.logger.log(f"💖 准备发送关注感谢: {follow_response}")

            # 检查设备状态是否允许发送
            can_send_result = self._can_send_message()
            # device_state = can_send_result.get('device_state', 'unknown')  # 暂时不需要使用

            # 统一使用优先队列机制，关注消息权重为高优先级
            self.logger.log(f"📦 关注回复加入优先队列（权重:{MessageWeight.FOLLOW_MESSAGE}-高优先级）")
            if self._add_to_priority_queue(follow_response, user_name, f"关注了主播", "follow", MessageWeight.FOLLOW_MESSAGE, user_id):
                self.logger.log(f"📦 关注回复已加入队列")
                # 如果设备空闲，立即触发队列处理
                if can_send_result.get('can_send'):
                    self._process_message_queue()
            else:
                self.logger.log(f"❌ 关注回复加入队列失败")

    def _parseLikeMsg(self, payload):
        """重写点赞消息处理，添加智能互动功能"""
        # 调用父类方法处理基本逻辑
        super()._parseLikeMsg(payload)

        # 检查消息时间过滤（跳过程序启动前的历史消息）
        current_time = time.time()
        if self.message_time_filter_enabled and current_time - self.start_time < 5:
            return

        # 如果启用了点赞互动功能，则处理互动回复
        if self.like_interaction_enabled:
            from protobuf.douyin import LikeMessage
            message = LikeMessage().parse(payload)
            user_name = message.user.nick_name
            user_id = str(message.user.id)
            count = message.count

            current_time = time.time()

            # 累计用户点赞数
            if user_id not in self.user_like_counts:
                self.user_like_counts[user_id] = {'name': user_name, 'total': 0, 'last_time': 0}

            self.user_like_counts[user_id]['total'] += count
            self.user_like_counts[user_id]['last_time'] = current_time
            total_likes = self.user_like_counts[user_id]['total']

            # 检查是否达到阈值且满足冷却时间
            is_first_like = user_id not in self.user_first_like

            # 第一次点赞：达到1个赞就回复
            # 后续点赞：达到100个赞才回复
            threshold = self.first_like_threshold if is_first_like else self.subsequent_like_threshold

            if (total_likes >= threshold and
                current_time - self.last_like_interaction_time >= self.like_interaction_cooldown):

                print(f"👍 {user_name} 点了{count}个赞，累计{total_likes}个赞")

                # 检查是否有点赞回复模板
                if not self.like_templates:
                    print(f"⚠️ 点赞回复模板为空，跳过点赞回复: {user_name}")
                    return

                # 随机选择点赞回复模板
                like_response = random.choice(self.like_templates).format(user_name=user_name)

                if is_first_like:
                    print(f"🎉 首次点赞互动触发: {user_name} 累计{total_likes}个赞，首次点赞回复 -> {like_response}")
                    # 标记该用户已经首次回复过
                    self.user_first_like[user_id] = True
                else:
                    self.logger.log(f"🎉 点赞互动触发: {user_name} 累计{total_likes}个赞，达到{threshold}个阈值 -> {like_response}")

                    # 达到100个赞，给该用户设置优先权
                    if total_likes >= 100:
                        priority_count = 1  # 100个赞获得1条优先权
                        if user_id in self.priority_users:
                            self.priority_users[user_id] += priority_count
                        else:
                            self.priority_users[user_id] = priority_count
                        self.logger.log(f"🔥 {user_name} 点赞达到{total_likes}个，获得{priority_count}条优先权")

                # 重置该用户的点赞计数
                self.user_like_counts[user_id]['total'] = 0

                # 如果启用了状态检查，先检查设备状态
                if self.status_check_enabled:
                    can_send_result = self._can_send_message()
                    device_state = can_send_result.get('device_state', 'unknown')

                    # 更新设备状态缓存
                    self.last_device_state = device_state
                    self.last_status_check = current_time

                    if not can_send_result.get('can_send'):
                        reason = can_send_result.get('reason', '未知原因')

                        print(f"🚫 点赞回复被阻止发送: {reason}")
                        print(f"   点赞用户: {user_name} (累计{total_likes}个赞)")
                        print(f"   设备状态: {device_state}")

                        # 如果是 speaking 状态且启用了消息队列，则加入优先队列
                        if device_state == 'speaking' and self.message_queue_enabled:
                            if self._add_to_priority_queue(like_response, user_name, f"累计点赞{total_likes}个", "like", MessageWeight.LIKE_MESSAGE):
                                self.logger.log(f"� 点赞回复已加入优先队列")
                            else:
                                self.logger.log(f"❌ 点赞回复加入队列失败")

                        return

                # 统一使用优先队列机制，点赞消息权重为中等优先级
                print(f"📦 点赞回复加入优先队列（权重:{MessageWeight.LIKE_MESSAGE}-中等优先级）")
                if self._add_to_priority_queue(like_response, user_name, f"累计点赞{total_likes}个", "like", MessageWeight.LIKE_MESSAGE):
                    print(f"📦 点赞回复已加入队列")
                    # 如果设备空闲，立即触发队列处理
                    if can_send_result.get('can_send'):
                        self._process_message_queue()
                else:
                    print(f"❌ 点赞回复加入队列失败")

                self.last_like_interaction_time = current_time
            else:
                # 未达到阈值或在冷却期，静默处理（不输出日志）
                if total_likes >= threshold:
                    # 只在冷却期时显示冷却信息
                    remaining_cooldown = self.like_interaction_cooldown - (current_time - self.last_like_interaction_time)
                    print(f"⏳ 点赞互动冷却中，还需等待{remaining_cooldown:.1f}秒")
            
    def preprocess_message(self, content: str) -> str:
        """消息预处理：去除重复字符、清理格式等"""
        if not self.message_preprocessing_enabled or not content:
            return content

        import re

        # 1. 去除重复字符（保留最多3个相同字符）
        def remove_repeat_chars(text):
            result = []
            prev_char = None
            repeat_count = 0

            for char in text:
                if char == prev_char:
                    repeat_count += 1
                    if repeat_count < self.max_repeat_chars:
                        result.append(char)
                else:
                    result.append(char)
                    prev_char = char
                    repeat_count = 1

            return ''.join(result)

        # 2. 清理多余空格
        processed = re.sub(r'\s+', ' ', content.strip())

        # 3. 清理危险字符（防止ESP32解析错误）
        processed = self.clean_dangerous_chars_from_message(processed)

        # 4. 去除重复字符
        processed = remove_repeat_chars(processed)

        # 5. 限制长度（可选）
        max_length = 200
        if len(processed) > max_length:
            processed = processed[:max_length] + "..."

        return processed

    def clean_dangerous_chars_from_message(self, message):
        """清理消息中可能导致ESP32解析错误的危险字符和模式"""
        if not message:
            return message

        original_message = message

        # 1. 移除或替换危险的字符组合
        dangerous_patterns = [
            (r'print\s*\([^)]*\)', '代码'),  # print("xxx") -> 代码
            (r'echo\s+[^\s]+', '命令'),      # echo xxx -> 命令
            (r'exec\s*\([^)]*\)', '执行'),   # exec("xxx") -> 执行
            (r'eval\s*\([^)]*\)', '计算'),   # eval("xxx") -> 计算
            (r'system\s*\([^)]*\)', '系统'), # system("xxx") -> 系统
            (r'bash\s*[|&;]', '脚本'),       # bash| -> 脚本
            (r'cmd\s*[|&;]', '命令'),        # cmd| -> 命令
            (r'sh\s*[|&;]', '脚本'),         # sh| -> 脚本
        ]

        for pattern, replacement in dangerous_patterns:
            message = re.sub(pattern, replacement, message, flags=re.IGNORECASE)

        # 2. 移除危险的单个字符
        dangerous_chars = ['"', "'", '`', ';', '&', '|', '<', '>', '$', '\\']
        for char in dangerous_chars:
            message = message.replace(char, '')

        # 3. 清理括号内的内容如果包含危险模式
        # 例如: @print("GKll44") -> @用户名
        message = re.sub(r'@[^@\s]*\([^)]*\)', '@用户名', message)

        # 4. 清理多余空格
        message = re.sub(r'\s+', ' ', message).strip()

        # 如果消息被大幅修改，记录日志
        if message != original_message:
            print(f"🔧 消息内容清理: '{original_message[:30]}...' → '{message[:30]}...'")

        return message

    def clean_username_for_device(self, user_name):
        """清理用户名中可能导致ESP32解析错误的特殊字符，保持用户名可读性"""
        if not user_name:
            return "用户"

        # 记录原始用户名
        original_name = user_name

        # 移除可能导致解析错误的字符
        dangerous_chars = ['(', ')', '"', "'", '`', ';', '&', '|', '<', '>', '$', '\\']
        for char in dangerous_chars:
            user_name = user_name.replace(char, '')

        # 移除可能的代码关键词
        code_keywords = ['print', 'echo', 'exec', 'eval', 'system', 'cmd', 'bash', 'sh']
        for keyword in code_keywords:
            if keyword in user_name.lower():
                user_name = user_name.replace(keyword, '').replace(keyword.upper(), '').replace(keyword.capitalize(), '')

        # 处理非ASCII字符 - 优化策略，保持可读性
        import re
        import unicodedata

        # 扩展的中文用户名映射表
        name_mappings = {
            'ドラえもん': 'doraemon',
            '福建第一帅': 'fujian1shuai',
            '广西创东方传媒': '广西创东方',
            '岛田家族': '岛田家族',
            # 常见单字符中文名
            '锤': 'chui', '龙': 'long', '虎': 'hu', '凤': 'feng', '鹰': 'ying',
            '狼': 'lang', '熊': 'xiong', '猫': 'mao', '狗': 'gou', '鸟': 'niao',
            '鱼': 'yu', '花': 'hua', '草': 'cao', '树': 'shu', '山': 'shan',
            '水': 'shui', '火': 'huo', '土': 'tu', '金': 'jin', '木': 'mu',
            '风': 'feng', '雨': 'yu', '雪': 'xue', '云': 'yun', '月': 'yue',
            '日': 'ri', '星': 'xing'
        }

        # 检查是否有直接映射
        if user_name in name_mappings:
            user_name = name_mappings[user_name]
        else:
            # 新的处理策略：保持中文字符，只处理真正危险的字符
            cleaned_chars = []
            for char in user_name:
                char_code = ord(char)
                if char_code < 128:  # ASCII字符
                    cleaned_chars.append(char)
                elif 0x4e00 <= char_code <= 0x9fff:  # 中文字符范围
                    # 保持中文字符不变
                    cleaned_chars.append(char)
                else:
                    # 其他非ASCII字符尝试转换
                    try:
                        normalized = unicodedata.normalize('NFKD', char)
                        ascii_char = ''.join(c for c in normalized if ord(c) < 128)
                        if ascii_char:
                            cleaned_chars.append(ascii_char)
                        else:
                            # 如果无法转换，使用简单占位符
                            cleaned_chars.append('x')
                    except:
                        cleaned_chars.append('x')

            user_name = ''.join(cleaned_chars)

        # 清理多余空格
        user_name = re.sub(r'\s+', ' ', user_name).strip()

        # 如果清理后为空或太短，使用默认名称
        if not user_name or len(user_name) < 1:
            user_name = "用户"

        # 限制长度（增加到15字符以保持更好的可读性）
        if len(user_name) > 15:
            user_name = user_name[:15]

        # 只有在用户名被显著修改时才记录日志
        if len(user_name) < len(original_name) * 0.5:  # 如果长度减少超过50%
            print(f"🔧 用户名清理: '{original_name}' → '{user_name}'")

        return user_name



    def _check_scheduled_broadcast(self):
        """检查是否需要定时插播"""
        if not self.scheduled_broadcast_enabled:
            return

        current_time = time.time()
        time_since_last_broadcast = current_time - self.last_broadcast_time
        interval_seconds = self.broadcast_interval * 60

        # 检查是否到了插播时间
        if time_since_last_broadcast >= interval_seconds:
            # 检查是否有可用的插播模板
            if not self.broadcast_templates:
                print(f"⚠️ 定时插播模板为空，跳过插播")
                self.last_broadcast_time = current_time  # 更新时间避免频繁检查
                return

            # 检查设备状态，只在设备空闲时发送插播
            device_status = self._get_device_status()
            device_state = device_status.get("device_state", "unknown")

            # 如果设备正在说话，延迟插播
            if device_state == 'speaking':
                print(f"📺 定时插播延迟：设备正在说话，等待设备空闲")
                return

            # 随机选择一个插播模板
            broadcast_content = random.choice(self.broadcast_templates)

            print(f"📺 定时插播触发: 距离上次插播 {time_since_last_broadcast/60:.1f} 分钟")
            self.logger.log(f"📺 定时插播内容: {broadcast_content[:50]}...")

            # 定时插播加入高优先级队列
            if self._add_to_priority_queue(broadcast_content, "系统插播", "定时插播", "broadcast", MessageWeight.SYSTEM_BROADCAST):
                self.logger.log(f"📺 定时插播已加入高优先级队列")
                # 立即触发队列处理
                self._process_message_queue()
            else:
                print(f"❌ 定时插播加入队列失败")

            # 更新最后插播时间
            self.last_broadcast_time = current_time



    def _parseGiftMsg(self, payload):
        """重写礼物消息处理，添加优先级功能"""
        # 调用父类方法处理基本逻辑
        super()._parseGiftMsg(payload)

        if not self.gift_priority_enabled:
            return

        # 解析礼物消息
        from protobuf.douyin import GiftMessage
        message = GiftMessage().parse(payload)
        user_name = message.user.nick_name
        user_id = str(message.user.id)
        gift_name = message.gift.name
        gift_count = message.combo_count

        self.logger.log(f"🎁 礼物消息: {user_name} 送出 {gift_name}x{gift_count}")

        # 记录礼物到排行榜统计
        leaderboard_stats.record_gift(user_id, user_name, self.live_id, gift_name, gift_count)

        # 根据礼物权重计算优先权数量
        gift_weight = self.gift_weights.get(gift_name, self.gift_weights.get("默认", 1))
        total_weight = gift_weight * gift_count  # 礼物权重 × 数量

        # 计算优先权数量：固定1条优先权
        priority_count = 1  # 所有礼物都只获得1条优先权

        # 给该用户设置优先权
        if user_id in self.priority_users:
            self.priority_users[user_id] += priority_count  # 累加优先权
        else:
            self.priority_users[user_id] = priority_count

        self.logger.log(f"🔥 {user_name} 获得优先权: 礼物权重{gift_weight}×{gift_count}={total_weight}，优先权{priority_count}条")
        self.logger.log(f"🔥 {user_name} 当前总优先权: {self.priority_users[user_id]}条消息")

        # 调试信息：检查礼物回复功能状态
        self.logger.log(f"🔍 礼物回复功能状态: enabled={self.gift_priority_enabled}, 模板数量={len(self.gift_response_templates)}")

        # 礼物回复处理 - 与优先权无关，礼物回复本身具有高优先级
        self._handle_gift_reply(user_name, gift_name, gift_count, gift_weight, user_id)

    def _handle_gift_reply(self, user_name: str, gift_name: str, gift_count: int, gift_weight: int, user_id: str):
        """处理礼物回复，与用户优先权分开处理"""

        # 检查礼物回复功能是否启用
        if not self.gift_priority_enabled:
            self.logger.log("⏭️ 礼物回复功能已禁用，跳过回复")
            return

        # 检查消息时间过滤（跳过程序启动前的历史消息）
        current_time = time.time()
        if self.message_time_filter_enabled and current_time - self.start_time < 2:
            self.logger.log(f"⏭️ 跳过历史礼物消息")
            return

        # 随机选择礼物回复模板
        if self.gift_response_templates:
            # 增强日志记录，帮助诊断问题
            self.logger.log(f"🔍 可用的礼物回复模板数量: {len(self.gift_response_templates)}")
            for i, template in enumerate(self.gift_response_templates):
                self.logger.log(f"   模板 {i}: {template}")
            
            template = random.choice(self.gift_response_templates)
            self.logger.log(f"🎯 选中的礼物回复模板: {template}")
            
            # 清理用户名防止ESP32解析错误
            clean_user_name = self.clean_username_for_device(user_name)
            gift_response = template.format(
                user_name=clean_user_name,
                gift_name=gift_name,
                gift_count=gift_count
            )
            self.logger.log(f"💬 生成的礼物回复内容: {gift_response}")

            # 检查生成的回复是否为空
            if not gift_response or not gift_response.strip():
                self.logger.log("❌ 警告: 生成的礼物回复为空，跳过发送")
                return

            # 礼物回复本身具有高优先级，直接加入优先队列
            self.logger.log(f"🎁 礼物回复加入优先队列，权重: {gift_weight}")
            self._add_to_priority_queue(gift_response, user_name, f"送出{gift_name}x{gift_count}", "gift", gift_weight, user_id)
        else:
            self.logger.log("❌ 警告: 没有配置礼物回复模板，无法发送礼物回复")

    def _add_to_priority_queue(self, reply_content: str, user_name: str, original_content: str, message_type: str = "gift", gift_weight: int = 1, user_id: str = None):
        """添加消息到优先队列（按权重排序）"""
        if not self.message_queue_enabled:
            return False

        with self.queue_lock:
            # 创建优先消息
            priority_message = {
                'reply_content': reply_content,
                'user_name': user_name,
                'original_content': original_content,
                'timestamp': time.time(),
                'message_type': message_type,
                'priority': True,
                'gift_weight': gift_weight,  # 礼物权重，用于排序
                'user_id': user_id  # 添加user_id字段
            }

            # 找到合适的插入位置（按权重降序排列，相同权重按时间顺序）
            insert_index = len(self.message_queue)  # 默认插入到末尾

            # 防冷场消息应该排在最后，甚至在普通消息后面
            if gift_weight == MessageWeight.ANTI_SILENCE and message_type == "anti_silence":
                # 防冷场消息直接插入到队列末尾
                insert_index = len(self.message_queue)
            else:
                for i, msg in enumerate(self.message_queue):
                    if msg.get('priority', False):
                        # 如果当前消息权重更高，插入到这里
                        if gift_weight > msg.get('gift_weight', 1):
                            insert_index = i
                            break
                        # 如果权重相同，继续查找（保持时间顺序）
                    else:
                        # 遇到普通消息，只有权重大于普通用户的优先消息才能插入到普通消息前面
                        if gift_weight > MessageWeight.NORMAL_USER:
                            insert_index = i
                            break

            self.message_queue.insert(insert_index, priority_message)
            self.logger.log(f"🔥 优先消息已加入队列[位置{insert_index}]: {user_name}: {original_content} (权重:{gift_weight})")

            # 智能队列管理 - 防止积压
            self._smart_queue_cleanup(gift_weight, user_name, original_content, insert_index)

            return True

    def _smart_queue_cleanup(self, new_weight: int, user_name: str, original_content: str, insert_index: int):
        """智能队列清理机制"""
        current_size = len(self.message_queue)

        # 如果队列未满，直接返回
        if current_size <= self.message_queue_max_size:
            return

        # 统计不同优先级的消息数量
        high_priority_count = sum(1 for msg in self.message_queue
                                if msg.get('priority', False) and msg.get('gift_weight', 0) > MessageWeight.NORMAL_USER)
        normal_priority_count = current_size - high_priority_count

        # 队列积压严重时的紧急清理
        if current_size >= self.queue_cleanup_threshold:
            self.logger.log(f"🚨 队列积压严重({current_size}条)，启动紧急清理")
            self._emergency_queue_cleanup()
            return

        # 按优先级分类管理
        if new_weight > MessageWeight.NORMAL_USER:  # 高优先级消息
            if high_priority_count > self.queue_high_priority_max:
                # 移除最老的高优先级消息
                self._remove_oldest_high_priority_message()
            elif normal_priority_count > 0:
                # 移除一个普通优先级消息
                self._remove_oldest_normal_priority_message()
            else:
                # 移除刚插入的消息
                self.message_queue.pop(insert_index)
                self.logger.log(f"🚫 高优先级队列已满，拒绝新消息: {user_name}: {original_content[:20]}...")
        else:  # 普通优先级消息
            if normal_priority_count > self.queue_normal_priority_max:
                # 移除最老的普通优先级消息
                self._remove_oldest_normal_priority_message()
            else:
                # 移除刚插入的消息
                self.message_queue.pop(insert_index)
                self.logger.log(f"🚫 普通优先级队列已满，拒绝新消息: {user_name}: {original_content[:20]}...")

    def _emergency_queue_cleanup(self):
        """紧急队列清理 - 只保留最重要的消息"""
        if not self.message_queue:
            return

        # 按权重排序，保留前10条最重要的消息
        sorted_messages = sorted(self.message_queue,
                               key=lambda x: (x.get('gift_weight', 0), -x.get('timestamp', 0)),
                               reverse=True)

        removed_count = len(self.message_queue) - 10
        self.message_queue = sorted_messages[:10]

        self.logger.log(f"🧹 紧急清理完成，移除了 {removed_count} 条消息，保留 {len(self.message_queue)} 条重要消息")

    def _remove_oldest_high_priority_message(self):
        """移除最老的高优先级消息"""
        for i, msg in enumerate(self.message_queue):
            if msg.get('priority', False) and msg.get('gift_weight', 0) > MessageWeight.NORMAL_USER:
                removed = self.message_queue.pop(i)
                self.logger.log(f"📦 移除最老的高优先级消息(权重:{removed.get('gift_weight', 0)}): {removed['user_name']}")
                break

    def _remove_oldest_normal_priority_message(self):
        """移除最老的普通优先级消息"""
        for i, msg in enumerate(self.message_queue):
            if not msg.get('priority', False) or msg.get('gift_weight', 0) <= MessageWeight.NORMAL_USER:
                removed = self.message_queue.pop(i)
                priority_info = f"权重:{removed.get('gift_weight', 0)}" if removed.get('priority', False) else "普通"
                self.logger.log(f"📦 移除最老的普通优先级消息({priority_info}): {removed['user_name']}")
                break

    def _find_lowest_priority_message(self):
        """找到优先级最低的消息索引"""
        if not self.message_queue:
            return None

        lowest_priority = float('inf')
        lowest_index = None

        for i, msg in enumerate(self.message_queue):
            if msg.get('priority', False):
                # 优先消息按权重排序，权重越低优先级越低
                priority = msg.get('gift_weight', 1)
            else:
                # 普通消息优先级为0（最低）
                priority = 0

            if priority < lowest_priority:
                lowest_priority = priority
                lowest_index = i

        return lowest_index

    def _find_replaceable_message(self, new_msg_weight: int):
        """找到可以被新消息替换的消息索引（权重保护机制）"""
        if not self.message_queue:
            return None

        # 优先查找普通消息（权重为0）
        for i, msg in enumerate(self.message_queue):
            if not msg.get('priority', False):
                return i

        # 如果没有普通消息，查找权重低于或等于新消息的优先消息
        for i, msg in enumerate(self.message_queue):
            if msg.get('priority', False):
                existing_weight = msg.get('gift_weight', 1)
                if existing_weight <= new_msg_weight:
                    return i

        # 如果所有消息权重都高于新消息，返回None（拒绝新消息）
        return None

    # 游戏交互检查已删除

    def _check_game_state_reset(self, current_time: float):
        """检查并重置卡住的游戏状态"""
        # 如果游戏启动状态超过30秒，自动重置
        if self.game_starting and (current_time - self.game_start_time > 30):
            self.logger.log(f"🎮 游戏启动超时，重置游戏状态")
            self._reset_game_state()

        # 如果游戏进行状态超过5分钟，自动重置
        elif self.current_game and (current_time - self.game_start_time > 300):
            self.logger.log(f"🎮 游戏运行超时，重置游戏状态")
            self._reset_game_state()

    def _reset_game_state(self):
        """重置所有游戏状态"""
        self.current_game = None
        self.game_starting = False
        self.pending_game_type = None
        self.game_participants = set()
        self.game_start_time = 0

    def _start_game(self, game_type: str, current_time: float):
        """开始AI驱动的游戏"""
        # 设置游戏启动锁定状态
        self.game_starting = True
        self.pending_game_type = game_type
        self.last_game_time = current_time  # 更新最后游戏时间（用于冷却）

        self.logger.log(f"🎮 开始启动游戏: {game_type}")

        # 构建AI游戏指令，包含完整的游戏规则和互动逻辑
        game_instructions = self._build_ai_game_instruction(game_type)

        # 发送给AI设备，使用最高优先级
        self._send_ai_game_instruction(game_instructions, current_time)

        return None  # 不返回消息，因为AI会自己开始游戏

    def _build_ai_game_instruction(self, game_type: str):
        """构建AI游戏指令"""

        if game_type == "猜数字":
            return "现在开始猜数字游戏。你心里想一个1到100的数字，告诉观众来猜。观众猜数字时你判断大小并给提示，有人答对就恭喜并结束游戏。"

        elif game_type == "数学题":
            return "现在开始数学题游戏。你出一道简单的加减乘法题，观众抢答，答对的就恭喜并结束游戏。"

        elif game_type == "成语接龙":
            return "现在开始成语接龙游戏。你说一个四字成语，观众接龙，接对的就继续，进行几轮后结束游戏。"

        elif game_type == "猜谜语":
            return "现在开始猜谜语游戏。你出一个简单的谜语，观众来猜，答对的就恭喜并结束游戏。"

        elif game_type == "抢答题":
            return "现在开始抢答题游戏。你出一道常识问答题，观众抢答，答对的就恭喜并结束游戏。"

        elif game_type == "数数游戏":
            return "现在开始数数游戏。从1开始数数，每人说一个数字，你来判断对错，数到100就结束游戏。"

        elif game_type == "词语接龙":
            return "现在开始词语接龙游戏。你说一个词语，观众接龙，接对的就继续，进行几轮后结束游戏。"

        elif game_type == "猜表情":
            return "现在开始猜表情游戏。你发几个表情符号组合，观众猜含义，答对的就恭喜并结束游戏。"

        return "开始主持游戏吧！"

    def _send_ai_game_instruction(self, instruction: str, current_time: float):
        """发送AI游戏指令，使用最高优先级"""
        self.logger.log(f"� 发送AI游戏指令: {instruction[:50]}...")

        # 统一使用队列系统，避免并发发送风险
        self.logger.log(f"🎮 游戏指令统一进入高优先级队列，避免设备卡死")

        # 使用游戏开始指令权重确保高优先级处理
        if self._add_to_priority_queue(instruction, "系统", f"游戏指令:{self.pending_game_type}", "game_start", MessageWeight.GAME_START):
            self.logger.log(f"✅ 游戏指令已加入高优先级队列")
            # 立即触发队列处理
            self._process_message_queue()
            # 预设游戏开始状态
            self._confirm_game_start(current_time)
        else:
            self.logger.log(f"❌ 游戏指令加入队列失败")
            self._cancel_game_start()

    def _confirm_game_start(self, current_time: float):
        """确认游戏开始，设置正式状态"""
        if self.game_starting and self.pending_game_type:
            self.current_game = self.pending_game_type
            self.game_start_time = current_time
            self.game_participants = set()

            # 清除启动状态
            self.game_starting = False
            self.pending_game_type = None

            self.logger.log(f"🎮 游戏正式开始: {self.current_game}，开始计时")

    def _cancel_game_start(self):
        """取消游戏启动"""
        if self.game_starting:
            self.logger.log(f"❌ 游戏启动失败，取消: {self.pending_game_type}")
            self.game_starting = False
            self.pending_game_type = None

    def _check_auto_game_trigger(self, current_time: float):
        """检查自动游戏触发"""
        if not self.auto_game_enabled:
            return None

        # 如果有游戏正在进行或正在启动，不触发新游戏
        if self.current_game or self.game_starting:
            return None

        # 检查是否到了自动触发时间
        if current_time - self.last_auto_game_time < self.auto_game_interval * 60:
            return None

        # 随机选择一个游戏类型
        game_types = ["猜数字", "数学题", "猜谜语", "抢答题", "猜表情"]
        selected_game = random.choice(game_types)

        self.logger.log(f"🎮 自动触发游戏: {selected_game}")
        return self._start_game(selected_game, current_time)

    def _check_game_answer(self, user_name: str, user_id: str, content: str):
        """检查游戏参与（AI模式下只记录参与者）"""
        if not self.current_game:
            return None

        # 添加用户到参与者列表
        self.game_participants.add(user_id)
        self.logger.log(f"🎮 用户 {user_name} 参与游戏: {content[:20]}...")

        # AI模式下不需要我们判断答案，让AI自己处理
        return None

    def _send_game_timeout_instruction(self):
        """发送游戏超时指令给AI"""
        timeout_instruction = "游戏时间到了，请总结一下游戏结果，宣布正确答案，然后开始下一个话题。"
        self.logger.log(f"⏰ 游戏超时，发送结束指令")

        # 统一使用队列系统，避免并发发送风险
        self.logger.log(f"⏰ 游戏超时指令统一进入高优先级队列，避免设备卡死")

        # 使用游戏超时指令权重确保高优先级处理
        if self._add_to_priority_queue(timeout_instruction, "系统", f"游戏超时:{self.current_game}", "game_timeout", MessageWeight.GAME_TIMEOUT):
            self.logger.log(f"✅ 游戏超时指令已加入高优先级队列")
            # 立即触发队列处理
            self._process_message_queue()
        else:
            self.logger.log(f"❌ 游戏超时指令加入队列失败")

        # 清理游戏状态
        self._end_game()

    def _end_game(self, timeout_message: str = None):
        """结束游戏"""
        if timeout_message:
            self.logger.log(f"🎮 {timeout_message}")

        self.current_game = None
        self.game_answer = None
        self.game_participants = set()
        self.game_start_time = 0

    # 游戏相关方法已删除

    def _sort_queue_by_priority(self):
        """按优先级对整个队列进行排序"""
        if not self.message_queue:
            return

        def get_priority(msg):
            if msg.get('priority', False):
                # 优先消息：权重越高优先级越高
                return (1, msg.get('gift_weight', 1), -msg.get('timestamp', 0))  # 权重相同时按时间顺序
            else:
                # 普通消息：按时间排序，越早优先级越高（FIFO）
                return (0, msg.get('timestamp', 0))

        # 按优先级排序（优先消息在前，权重高的在前；普通消息在后，早的在前）
        self.message_queue.sort(key=get_priority, reverse=True)

    def _send_websocket_message(self, message: str, force: bool = False, retry_count: int = 0):
        """通过WebSocket直接发送消息到设备（与Flask API使用相同的逻辑）"""
        current_time = time.time()
        max_retries = 0  # 禁用重试机制，避免重复发送

        # 初始化send_id，避免在finally块中引用未定义的变量
        send_id = "unknown"

        # 全局发送锁 - 防止多条消息同时发送
        lock_acquired = False
        try:
            lock_acquired = self.send_lock.acquire(blocking=False)
            if not lock_acquired:
                print(f"🚫 发送被阻止: 另一条消息正在发送中")
                return {"success": False, "message": "另一条消息正在发送中，请稍后重试"}

            # 锁获取成功，继续处理
            # 检查发送间隔，防止过于频繁的发送
            if current_time - self.last_send_complete_time < 0.5:  # 最小间隔0.5秒
                print(f"🚫 发送过于频繁，等待间隔: {0.5 - (current_time - self.last_send_complete_time):.1f}秒")
                time.sleep(0.5 - (current_time - self.last_send_complete_time))

            self.current_sending = True

            # 添加全局消息发送计数器
            if not hasattr(self, 'message_send_counter'):
                self.message_send_counter = 0
            self.message_send_counter += 1
            send_id = self.message_send_counter

            # 消息长度安全检查 - 定时插播不受限制
            max_safe_length = 50  # 设备安全长度限制

            # 检查是否是定时插播消息（通过调用栈判断）
            import traceback
            caller_info = traceback.extract_stack()[-2]
            is_broadcast = "_check_scheduled_broadcast" in str(caller_info) or "broadcast" in message.lower()

            if not is_broadcast and len(message) > max_safe_length:
                original_length = len(message)
                message = message[:max_safe_length] + "..."
                print(f"⚠️ 消息过长({original_length}字符)，已截断到{len(message)}字符")
            elif is_broadcast and len(message) > max_safe_length:
                print(f"📺 定时插播消息({len(message)}字符)，不受长度限制")

            # 添加调用栈信息用于调试
            caller_info = traceback.extract_stack()[-2]
            if retry_count == 0:
                print(f"🔍 发送调用[{send_id}]来源: {caller_info.filename}:{caller_info.lineno} - {caller_info.name}")
                print(f"🔍 发送消息内容[{send_id}]: {message[:50]}{'...' if len(message) > 50 else ''}")
            else:
                print(f"🔄 重试发送[{send_id}] ({retry_count}/{max_retries}): {message[:30]}...")

            # 获取消息发送锁
            with self.message_send_lock:
                try:
                    # 检查是否有消息正在发送
                    if self.current_sending_message and not force:
                        self.logger.warning(f"消息发送被阻止: 已有消息正在发送 '{self.current_sending_message[:20]}...'")
                        return {"success": False, "message": "设备正在处理其他消息"}

                    # 消息去重检查 - 防止短时间内发送相同消息
                    message_hash = hash(message)
                    if hasattr(self, 'last_message_hash') and self.last_message_hash == message_hash:
                        time_since_last = current_time - getattr(self, 'last_message_time', 0)
                        if time_since_last < 2.0:  # 2秒内不允许发送相同消息
                            print(f"🚫 检测到重复消息，跳过发送: {message[:30]}...")
                            return {"success": False, "message": "重复消息被过滤"}

                    # 检查发送间隔（防止消息过于频繁）
                    min_interval = 0.5  # 最小间隔0.5秒
                    if current_time - self.last_send_time < min_interval and not force:
                        wait_time = min_interval - (current_time - self.last_send_time)
                        self.logger.debug(f"消息发送间隔过短，等待 {wait_time:.2f} 秒")
                        time.sleep(wait_time)

                    # 确保消息内容安全，防止编码问题
                    try:
                        # 检查消息是否可以安全编码为UTF-8
                        message.encode('utf-8')
                        # 移除可能导致JSON解析问题的字符
                        message = message.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                        # 清理多余空格
                        import re
                        message = re.sub(r'\s+', ' ', message).strip()
                    except UnicodeEncodeError as e:
                        print(f"⚠️ 消息编码错误，使用安全版本: {e}")
                        # 如果编码失败，使用安全的ASCII版本
                        message = message.encode('ascii', 'ignore').decode('ascii')

                    # 标记当前正在发送的消息
                    self.current_sending_message = message
                    self.last_send_time = time.time()
                    self.last_message_hash = message_hash
                    self.last_message_time = current_time

                    import asyncio
                    import json

                    # 使用简化的WebSocket发送，避免异步处理问题
                    async def send_direct_message():
                        websocket_url = "ws://localhost:15000"
                        print(f"🔗 连接WebSocket: {websocket_url}")

                        try:
                            # 使用更短的连接超时
                            websocket = await asyncio.wait_for(
                                websockets.connect(websocket_url),
                                timeout=5.0
                            )

                            try:
                                test_message = {
                                    "type": "speak_message",
                                    "data": {
                                        "device_id": self.target_device_id,
                                        "message": message,
                                        "force": force,
                                        "live_id": self.live_id
                                    }
                                }

                                print(f"📤 发送WebSocket消息: {json.dumps(test_message, ensure_ascii=False)[:100]}...")
                                await websocket.send(json.dumps(test_message, ensure_ascii=False))

                                print(f"⏳ 等待WebSocket响应...")
                                response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                                response_data = json.loads(response)
                                print(f"📥 收到WebSocket响应: {response_data}")

                                # 检查响应
                                if response_data.get("type") == "speak_response":
                                    speak_data = response_data.get("data", {})
                                    if speak_data.get("success", True):
                                        print(f"🎤 发送: {message[:30]}{'...' if len(message) > 30 else ''}")
                                        print(f"✅ 发送成功")
                                        return {"success": True, "message": "消息已发送"}
                                    else:
                                        print(f"❌ 设备响应失败: {speak_data.get('message', '未知错误')}")
                                        return {"success": False, "message": speak_data.get('message', '设备响应失败')}
                                else:
                                    return {"success": True, "message": "消息已发送"}

                            finally:
                                # 确保连接被正确关闭
                                await websocket.close()

                        except asyncio.TimeoutError:
                            print(f"⏰ WebSocket连接或响应超时")
                            return {"success": False, "message": "连接超时"}
                        except websockets.exceptions.ConnectionClosed as e:
                            print(f"❌ WebSocket连接已关闭: {e}")
                            return {"success": False, "message": "WebSocket连接已断开"}
                        except asyncio.TimeoutError:
                            print(f"❌ WebSocket操作超时")
                            return {"success": False, "message": "WebSocket操作超时"}
                        except json.JSONDecodeError as e:
                            print(f"❌ WebSocket响应JSON解析失败: {e}")
                            return {"success": False, "message": "响应格式错误"}
                        except Exception as e:
                            print(f"❌ WebSocket发送异常: {e}")
                            import traceback
                            print(f"📋 异常详情: {traceback.format_exc()}")
                            return {"success": False, "message": str(e)}

                    # 使用简化的事件循环处理
                    try:
                        # 直接使用 asyncio.run，避免复杂的事件循环管理
                        response_data = asyncio.run(send_direct_message())
                    except Exception as e:
                        print(f"❌ 异步执行异常: {e}")
                        response_data = {"success": False, "message": f"执行异常: {str(e)}"}

                    if response_data.get("success"):
                        # 检查是否是重复的成功响应
                        current_time = time.time()
                        success_key = f"success_{hash(message)}"

                        if hasattr(self, 'last_success_times') and success_key in self.last_success_times:
                            time_since_last_success = current_time - self.last_success_times[success_key]
                            if time_since_last_success < 1.0:  # 1秒内不允许相同消息的重复成功
                                print(f"🚫 检测到重复成功响应，跳过日志: {message[:30]}...")
                                return {"success": True, "message": "重复成功响应"}

                        # 记录成功时间
                        if not hasattr(self, 'last_success_times'):
                            self.last_success_times = {}
                        self.last_success_times[success_key] = current_time

                        # 清理过期的成功记录（保留最近10条）
                        if len(self.last_success_times) > 10:
                            oldest_key = min(self.last_success_times.keys(),
                                           key=lambda k: self.last_success_times[k])
                            del self.last_success_times[oldest_key]

                        # 重试机制已禁用，不会有重试
                        # if retry_count > 0:
                        #     print(f"🔄 这是重试发送[{send_id}] (第{retry_count}次)")
                        #     print(f"🎤 重试发送[{send_id}]: {message[:30]}{'...' if len(message) > 30 else ''}")

                        # 清除当前发送消息标记
                        self.current_sending_message = None

                        # 互动统计已改为直接在聊天消息处理时记录

                        # 如果不是强制发送，等待设备完成说话
                        if not force:
                            # 等待一小段时间确保设备开始处理
                            time.sleep(0.5)

                            # 等待设备完成说话，避免消息重叠
                            print(f"⏳ 等待设备完成说话...")
                            if self._wait_for_device_finish_speaking(timeout=10):
                                print(f"✅ 设备已完成说话，可以处理下一条消息")
                            else:
                                print(f"⚠️ 等待设备完成说话超时，继续处理下一条消息")

                            # 检查最终设备状态
                            device_status = self._get_device_status()
                            current_state = device_status.get('device_state', 'unknown')
                            if current_state == 'listening':
                                print(f"🎧 设备进入listening状态，重置防冷场计时器")
                                # 重置防冷场计时器
                                self.last_message_time = time.time()

                        return {"success": True, "message": "消息发送成功"}
                    else:
                        error_msg = response_data.get("message", "发送失败")
                        print(f"❌ 发送失败: {error_msg}")

                        # 重试机制已禁用，直接返回失败
                        print(f"❌ 发送失败，不再重试: {error_msg}")
                        return {"success": False, "message": f"发送失败: {error_msg}"}

                except Exception as e:
                    self.logger.error(f"发送消息异常: {e}")

                    # 重试机制已禁用，直接返回异常
                    print(f"❌ 发送异常，不再重试: {str(e)}")
                    return {"success": False, "message": f"发送异常: {str(e)}"}

                finally:
                    # 清除当前发送消息标记
                    self.current_sending_message = None

        finally:
            # 释放全局发送锁并更新状态
            self.current_sending = False
            self.last_send_complete_time = time.time()
            if lock_acquired:
                try:
                    self.send_lock.release()
                    print(f"🔓 发送锁已释放，消息ID: {send_id}")
                except Exception as release_error:
                    print(f"⚠️ 释放发送锁时出错: {release_error}")
            else:
                print(f"🔓 发送锁未获取，无需释放，消息ID: {send_id}")

    def check_esp32_status(self):
        """检查ESP32设备状态"""
        status = self._get_device_status()
        device_state = status.get('device_state', 'unknown')
        connected = status.get('connected', False)

        if connected:
            print(f"✅ ESP32设备在线: {self.target_device_id}")
            print(f"📊 设备状态: {device_state}")

            # 状态说明
            description = self.get_status_description(device_state)
            print(f"💡 状态说明: {description}")

            return {
                'connected': True,
                'device_state': device_state,
                'device_id': self.target_device_id,
                'mode': 'websocket'
            }
        else:
            print(f"❌ ESP32设备离线: {self.target_device_id}")
            return {
                'connected': False,
                'device_state': device_state,
                'device_id': self.target_device_id,
                'mode': 'websocket',
                'message': '设备未连接到WebSocket服务器'
            }

    def get_device_state_summary(self):
        """获取设备状态摘要信息"""
        current_time = time.time()
        
        # 如果距离上次检查超过间隔时间，则重新检查
        if current_time - self.last_status_check > self.status_check_interval:
            status = self.check_esp32_status()
            self.last_status_check = current_time
            self.last_device_state = status.get('device_state', 'unknown')
            return status
        else:
            # 返回缓存的状态信息
            return {
                "device_state": self.last_device_state,
                "connected": self.last_device_state not in ['offline', 'error', 'timeout'],
                "cached": True,
                "last_check": self.last_status_check,
                "can_send": self.last_device_state in ['idle', 'listening']
            }
            
    def get_status_description(self, device_state: str) -> str:
        """获取状态描述"""
        state_descriptions = {
            'idle': '空闲 - 可以发送消息',
            'listening': '监听中 - 可以发送消息',
            'speaking': '讲话中 - 暂时不能发送消息',
            'connecting': '连接中 - 暂时不能发送消息',
            'activating': '激活中 - 暂时不能发送消息',
            'error': '错误状态 - 不能发送消息',
            'offline': '离线 - 不能发送消息',
            'timeout': '超时 - 不能发送消息'
        }
        return state_descriptions.get(device_state, f'{device_state} - 状态未知')

    def get_queue_status(self) -> Dict[str, Any]:
        """获取消息队列状态"""
        with self.queue_lock:
            # 不在查看状态时清理过期消息，避免影响正常处理
            queue_info = {
                'enabled': self.message_queue_enabled,
                'size': len(self.message_queue),
                'max_size': self.message_queue_max_size,
                'max_age_seconds': self.message_queue_max_age,
                'messages': []
            }

            # 添加队列中的消息信息（不包含完整内容，避免过长）
            current_time = time.time()
            message_types_count = {}

            for i, msg in enumerate(self.message_queue):
                msg_age = current_time - msg['timestamp']
                msg_type = msg.get('message_type', 'unknown')

                # 统计消息类型
                message_types_count[msg_type] = message_types_count.get(msg_type, 0) + 1

                queue_info['messages'].append({
                    'index': i,
                    'user_name': msg['user_name'],
                    'content_preview': msg['original_content'][:30] + ('...' if len(msg['original_content']) > 30 else ''),
                    'message_type': msg_type,
                    'age_seconds': round(msg_age, 1),
                    'priority': msg.get('priority', False),
                    'gift_weight': msg.get('gift_weight', 0),
                    'time_str': msg.get('time_str', 'unknown'),
                    'is_expired': msg_age > self.message_queue_max_age
                })

            # 添加消息类型统计
            queue_info['message_types_count'] = message_types_count
            queue_info['expired_count'] = sum(1 for msg in queue_info['messages'] if msg['is_expired'])

            return queue_info

    def clear_message_queue(self) -> int:
        """清空消息队列"""
        with self.queue_lock:
            cleared_count = len(self.message_queue)
            self.message_queue.clear()

        print(f"🧹 已清空消息队列，清除了 {cleared_count} 条消息")
        return cleared_count

    def _cleanup_spoken_users(self):
        """定期清理spoken_users集合，防止内存泄漏"""
        current_time = time.time()

        # 检查是否到了清理时间
        if current_time - self.spoken_users_last_cleanup >= self.spoken_users_cleanup_interval:
            old_count = len(self.spoken_users)

            # 清理spoken_users集合（每小时重置一次）
            self.spoken_users.clear()
            self.spoken_users_last_cleanup = current_time

            # 同时清理其他可能的内存泄漏点
            self._cleanup_memory_leaks()

            if old_count > 0:
                print(f"🧹 定期清理spoken_users集合: 清除了 {old_count} 个用户记录，防止内存泄漏")
                print(f"💡 下次清理时间: {self.spoken_users_cleanup_interval/3600:.1f}小时后")

    def _cleanup_memory_leaks(self):
        """清理其他潜在的内存泄漏点"""
        try:
            # 清理用户点赞计数（保留最近活跃的用户）
            current_time = time.time()
            if hasattr(self, 'user_like_counts'):
                # 清理超过24小时未活动的用户点赞记录
                old_like_count = len(self.user_like_counts)
                # 这里简化处理，直接清空，实际可以根据时间戳清理
                if old_like_count > 1000:  # 超过1000个用户时清理
                    self.user_like_counts.clear()
                    self.user_first_like.clear()
                    print(f"🧹 清理用户点赞记录: {old_like_count} 个")

            # 清理优先用户记录
            if hasattr(self, 'priority_users'):
                # 移除优先权已用完的用户
                expired_users = [uid for uid, count in self.priority_users.items() if count <= 0]
                for uid in expired_users:
                    del self.priority_users[uid]
                if expired_users:
                    print(f"🧹 清理过期优先用户: {len(expired_users)} 个")

        except Exception as e:
            print(f"⚠️ 内存清理异常: {e}")

class LiveChatBotController:
    """直播聊天机器人控制器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config = ConfigManager(config_file)
        self.fetcher = None
        self.running = False
        
    def start(self):
        """启动聊天机器人"""
        print("🤖 抖音直播聊天机器人 - 增强版")
        print("=" * 60)
        
        # 创建增强版监控器
        self.fetcher = EnhancedLiveFetcher(self.config)
        
        # 检查ESP32设备状态
        print("\n🔍 检查ESP32设备状态...")
        # 等待一下确保Flask服务器完全启动
        time.sleep(2)
        status = self.fetcher.check_esp32_status()
        
        if not status.get('connected'):
            print("⚠️ ESP32设备离线，但仍会继续监控直播间")
            print("💡 请确保ESP32设备已连接并运行mcp_wake服务")
        else:
            # 显示详细状态信息
            device_state = status.get('device_state', 'unknown')
            description = self.fetcher.get_status_description(device_state)
            print(f"💡 当前状态: {description}")
            
            if device_state in ['idle', 'listening']:
                print("✅ 设备状态正常，可以发送消息")
            else:
                print("⚠️ 设备状态不允许发送消息，将等待状态变化")
        
        # 显示当前配置
        self.show_current_settings()
        
        # 获取直播间状态并开始监控
        try:
            self.fetcher.get_room_status()
            self.running = True
            
            print("\n🎯 开始监控直播间聊天消息...")
            print("💬 符合条件的聊天消息将自动通过ESP32回复")
            print("🔍 系统会自动检查设备状态，确保在合适时机发送消息")
            print("⏹️ 按Ctrl+C停止监控")
            print("=" * 60)
            
            self.fetcher.start()
            
        except KeyboardInterrupt:
            print("\n👋 正在停止聊天机器人...")
            self.stop()
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            
    def stop(self):
        """停止聊天机器人"""
        self.running = False
        if self.fetcher and hasattr(self.fetcher, 'ws') and self.fetcher.ws:
            self.fetcher.stop()
        print("✅ 聊天机器人已停止")
        
    def show_current_settings(self):
        """显示当前设置"""
        print("\n📋 当前设置:")
        print(f"   🔄 自动回复: {'启用' if self.config.get('reply_settings.enabled') else '禁用'}")
        print(f"   🎯 回复模板: {self.config.get('reply_settings.template')}")
        print(f"   ⏱️ 冷却时间: {self.config.get('reply_settings.cooldown_seconds')}秒")
        print(f"   🔍 状态检查: {'启用' if self.config.get('status_check.enabled', True) else '禁用'}")
        
        blocked_keywords = self.config.get('filter_settings.blocked_keywords', [])
        if blocked_keywords:
            print(f"   🚫 屏蔽关键词: {', '.join(blocked_keywords)}")
            
        blocked_users = self.config.get('filter_settings.blocked_users', [])
        if blocked_users:
            print(f"   🚫 屏蔽用户: {', '.join(blocked_users)}")
            
        print(f"   📊 消息长度限制: {self.config.get('filter_settings.min_message_length', 1)}-{self.config.get('filter_settings.max_message_length', 100)}字符")
            
    def change_template(self, template_name: str):
        """更改回复模板"""
        templates = self.config.get('templates', {})
        if template_name in templates:
            new_template = templates[template_name]
            self.config.set('reply_settings.template', new_template)
            if self.fetcher:
                self.fetcher.set_reply_template(new_template)
            print(f"✅ 回复模板已更改为: {new_template}")
        else:
            print(f"❌ 模板不存在: {template_name}")
            print(f"可用模板: {', '.join(templates.keys())}")
            
    def show_status(self):
        """显示当前设备状态"""
        if self.fetcher:
            status_info = self.fetcher.get_device_state_summary()
            device_state = status_info.get('device_state', 'unknown')
            description = self.fetcher.get_status_description(device_state)

            print(f"\n📊 设备状态: {device_state}")
            print(f"💡 状态说明: {description}")
            print(f"🔄 可发送消息: {'是' if status_info.get('can_send') else '否'}")

            if status_info.get('cached'):
                last_check = status_info.get('last_check', 0)
                check_time = time.strftime('%H:%M:%S', time.localtime(last_check))
                print(f"🕐 状态更新时间: {check_time}")

            # 显示消息队列状态
            queue_status = self.fetcher.get_queue_status()
            print(f"\n📦 消息队列状态:")
            print(f"   启用状态: {'是' if queue_status['enabled'] else '否'}")
            print(f"   队列大小: {queue_status['size']}/{queue_status['max_size']}")
            print(f"   消息有效期: {queue_status['max_age_seconds']}秒")

            if queue_status['messages']:
                print(f"   队列中的消息:")
                for i, msg in enumerate(queue_status['messages'], 1):
                    print(f"     {i}. [{msg['time_str']}] {msg['user_name']}: {msg['content_preview']} (等待{msg['age_seconds']}秒)")
            else:
                print(f"   队列为空")
        else:
            print("❌ 聊天机器人未启动")

    def clear_queue(self):
        """清空消息队列"""
        if self.fetcher:
            cleared_count = self.fetcher.clear_message_queue()
            print(f"✅ 已清空消息队列，清除了 {cleared_count} 条消息")
        else:
            print("❌ 聊天机器人未启动")

    def process_queue(self):
        """手动处理消息队列"""
        if self.fetcher:
            print("📤 手动触发消息队列处理...")
            self.fetcher._process_message_queue()
        else:
            print("❌ 聊天机器人未启动")

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    config_file = "config.json"
    if len(sys.argv) > 1:
        config_file = sys.argv[1]

    print("🚀 正在启动抖音直播聊天机器人...")
    print(f"📋 使用配置文件: {config_file}")
    print("🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动")
    print("   - 只有在 'listening' 或 'idle' 状态时才发送消息")
    print("   - 在 'speaking' 状态时消息会自动加入队列")
    print("   - 说话结束后自动处理队列中的消息")
    print("   - 队列空闲超过1秒自动发送防冷场消息")
    print("   - 检测点赞事件并主动与点赞用户互动")
    print("   - 避免设备冲突，确保消息发送时机合适")
    print("   - 支持消息队列状态查看和手动处理")
    print()

    # 创建并启动聊天机器人
    bot = LiveChatBotController(config_file)

    try:
        bot.start()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")



if __name__ == '__main__':
    main()
