<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播间配置 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 导航栏样式 */
        .navbar-brand-github {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            text-decoration: none;
            color: var(--color-fg-default);
            font-weight: 600;
            font-size: 16px;
        }

        .douyin-logo {
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--color-canvas-overlay);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        .time-display {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 14px;
        }

        /* 配置卡片样式 */
        .config-section {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            margin-bottom: var(--base-size-16);
        }

        .config-header {
            padding: var(--base-size-16) var(--base-size-20);
            border-bottom: 1px solid var(--color-border-default);
            background: var(--color-canvas-subtle);
            border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
        }

        .config-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            font-size: 16px;
            font-weight: 600;
        }

        .config-body {
            padding: var(--base-size-20);
        }

        .form-group {
            margin-bottom: var(--base-size-16);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
            color: var(--color-fg-default);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: var(--base-size-8) var(--base-size-12);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            background-color: var(--color-canvas-default);
            color: var(--color-fg-default);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--color-accent-emphasis);
            box-shadow: 0 0 0 3px var(--color-accent-subtle);
        }

        .template-list {
            margin-top: var(--base-size-12);
        }

        .template-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-8) var(--base-size-12);
            background: var(--color-canvas-subtle);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            margin-bottom: var(--base-size-8);
        }

        .template-text {
            flex: 1;
            font-size: 14px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: var(--base-size-20);
            color: var(--color-fg-muted);
        }

        .error {
            color: var(--color-danger-fg);
            background: var(--color-danger-subtle);
            border: 1px solid var(--color-danger-emphasis);
            padding: var(--base-size-12);
            border-radius: var(--borderRadius-small);
            margin-bottom: var(--base-size-16);
        }

        .success {
            color: var(--color-success-fg);
            background: var(--color-success-subtle);
            border: 1px solid var(--color-success-emphasis);
            padding: var(--base-size-12);
            border-radius: var(--borderRadius-small);
            margin-bottom: var(--base-size-16);
        }
    </style>
</head>
<body>
    <div class="github-container">
        <!-- GitHub风格导航栏 -->
        <nav class="github-navbar">
            <div class="github-container">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <a class="navbar-brand-github" href="/">
                        <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                        <span>AI机器人自动化直播控制台</span>
                    </a>

                    <!-- 导航链接 -->
                    <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                        <a href="/" class="nav-link">
                            <i class="bi bi-house-fill"></i>
                            <span>首页</span>
                        </a>
                        <a href="/leaderboard" class="nav-link">
                            <i class="bi bi-trophy-fill"></i>
                            <span>直播间排行榜</span>
                        </a>

                        <!-- 管理员专用链接 -->
                        <a href="/users" class="nav-link admin-only">
                            <i class="bi bi-people-fill"></i>
                            <span>用户管理</span>
                        </a>
                        <a href="/invitation-codes" class="nav-link admin-only">
                            <i class="bi bi-key-fill"></i>
                            <span>邀请码</span>
                        </a>
                    </div>

                    <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                        <div class="time-display">
                            <i class="bi bi-clock"></i>
                            <span id="currentTime"></span>
                        </div>

                        <!-- 个人中心下拉菜单 -->
                        <div class="dropdown" id="userDropdown">
                            <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                                <i class="bi bi-person-circle"></i>
                                <span id="currentUsername">用户</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="userDropdownMenu">
                                <a href="/profile" class="dropdown-item">
                                    <i class="bi bi-person-gear"></i>
                                    个人设置
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="github-main">
            <div class="github-header">
                <h1>
                    <i class="bi bi-gear-fill"></i>
                    直播间配置
                </h1>
                <p>配置直播间 <code id="roomIdDisplay">{{ room_id }}</code> 的各项功能</p>
            </div>

            <!-- 消息显示区域 -->
            <div id="messageArea"></div>

            <!-- 加载中状态 -->
            <div id="loadingState" class="loading">
                <i class="bi bi-hourglass-split" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                <div>正在加载配置数据...</div>
            </div>

            <!-- 配置内容 -->
            <div id="configContent" style="display: none;">
                <!-- 欢迎消息配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <h3>
                            <i class="bi bi-heart-fill"></i>
                            欢迎消息配置
                        </h3>
                    </div>
                    <div class="config-body">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="welcomeEnabled"> 启用欢迎功能
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="welcomeTemplate">添加欢迎模板</label>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <input type="text" id="welcomeTemplate" placeholder="例如：欢迎{user_name}来到直播间！">
                                <button class="btn-github btn-primary btn-small" onclick="addWelcomeTemplate()">
                                    <i class="bi bi-plus"></i> 添加
                                </button>
                            </div>
                        </div>
                        <div class="template-list" id="welcomeTemplates"></div>
                        <button class="btn-github btn-success" onclick="saveWelcomeConfig()">
                            <i class="bi bi-check"></i> 保存欢迎配置
                        </button>
                    </div>
                </div>

                <!-- 防冷场配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <h3>
                            <i class="bi bi-chat-dots-fill"></i>
                            防冷场配置
                        </h3>
                    </div>
                    <div class="config-body">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="antiSilenceEnabled"> 启用防冷场功能
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="antiSilenceDelay">冷场检测延迟（秒）</label>
                            <input type="number" id="antiSilenceDelay" min="10" max="300" value="15">
                        </div>
                        <div class="form-group">
                            <label for="antiSilenceCooldown">冷却时间（秒）</label>
                            <input type="number" id="antiSilenceCooldown" min="10" max="300" value="30">
                        </div>
                        <div class="form-group">
                            <label for="antiSilenceTemplate">添加防冷场模板</label>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <input type="text" id="antiSilenceTemplate" placeholder="例如：现在直播间有点冷场，请主动和大家打个招呼">
                                <button class="btn-github btn-primary btn-small" onclick="addAntiSilenceTemplate()">
                                    <i class="bi bi-plus"></i> 添加
                                </button>
                            </div>
                        </div>
                        <div class="template-list" id="antiSilenceTemplates"></div>
                        <button class="btn-github btn-success" onclick="saveAntiSilenceConfig()">
                            <i class="bi bi-check"></i> 保存防冷场配置
                        </button>
                    </div>
                </div>

                <!-- 防刷屏配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <h3>
                            <i class="bi bi-shield-fill"></i>
                            防刷屏配置
                        </h3>
                    </div>
                    <div class="config-body">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="antiSpamEnabled"> 启用防刷屏功能
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="duplicateThreshold">重复消息阈值</label>
                            <input type="number" id="duplicateThreshold" min="2" max="10" value="3">
                        </div>
                        <div class="form-group">
                            <label for="frequencyLimit">频率限制（条/分钟）</label>
                            <input type="number" id="frequencyLimit" min="1" max="20" value="5">
                        </div>
                        <div class="form-group">
                            <label for="warningMessage">警告消息</label>
                            <input type="text" id="warningMessage" placeholder="请不要刷屏，谢谢配合！" value="请不要刷屏，谢谢配合！">
                        </div>
                        <button class="btn-github btn-success" onclick="saveAntiSpamConfig()">
                            <i class="bi bi-check"></i> 保存防刷屏配置
                        </button>
                    </div>
                </div>

                <!-- 自定义回复配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <h3>
                            <i class="bi bi-reply-fill"></i>
                            自定义回复配置
                        </h3>
                    </div>
                    <div class="config-body">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="customReplyEnabled"> 启用自定义回复功能
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="customReplyCooldown">回复冷却时间（秒）</label>
                            <input type="number" id="customReplyCooldown" min="1" max="60" value="5">
                        </div>
                        <div id="customReplyRules"></div>
                        <button class="btn-github btn-success" onclick="saveCustomReplyConfig()">
                            <i class="bi bi-check"></i> 保存自定义回复配置
                        </button>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div style="margin-top: var(--base-size-24); text-align: center;">
                    <a href="/" class="btn-github btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回首页
                    </a>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/js/auth.js"></script>
    <script>
        const ROOM_ID = '{{ room_id }}';
        let configs = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查认证
            if (!checkAuth()) {
                window.location.href = '/login';
                return;
            }

            const authResult = await checkUserAuth();
            if (!authResult) {
                return; // 认证失败，已经处理跳转
            }
            
            await loadAllConfigs();
            
            updateTime();
            setInterval(updateTime, 1000);
        });

        // 检查用户认证状态和权限
        async function checkUserAuth() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                window.location.href = '/login';
                return false;
            }

            try {
                console.log('🔍 开始检查用户认证...');
                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const data = await response.json();
                console.log('🔍 认证响应:', data);
                
                if (data.success) {
                    document.getElementById('currentUsername').textContent = data.user.display_name || data.user.username;
                    
                    // 检查房间访问权限
                    console.log('🔍 开始检查房间访问权限...');
                    const roomAccessCheck = await checkRoomAccess(token);
                    console.log('🔍 房间访问权限检查结果:', roomAccessCheck);
                    
                    if (!roomAccessCheck) {
                        showMessage('您没有访问此直播间配置的权限', 'error');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                        return false;
                    }
                    
                    // 如果是管理员，显示管理员专用链接
                    if (data.user.role === 'admin') {
                        document.querySelectorAll('.admin-only').forEach(link => {
                            link.style.display = 'flex';
                        });
                    }
                    
                    console.log('✅ 用户认证和权限检查通过');
                    return true;
                } else {
                    localStorage.removeItem('auth_token');
                    window.location.href = '/login';
                    return false;
                }
            } catch (error) {
                console.error('❌ 验证用户认证失败:', error);
                window.location.href = '/login';
                return false;
            }
        }

        // 检查房间访问权限
        async function checkRoomAccess(token) {
            try {
                console.log('🔍 检查房间访问权限，房间ID:', ROOM_ID);
                // 通过尝试获取房间配置来检查访问权限
                const response = await fetch(`/api/rooms/${ROOM_ID}/welcome-config`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                console.log('🔍 房间访问权限API响应状态:', response.status);
                
                if (response.status === 403) {
                    console.log('❌ 房间访问权限被拒绝');
                    return false; // 没有权限
                }
                
                const result = response.ok;
                console.log('🔍 房间访问权限检查结果:', result);
                return result; // 有权限或其他可处理的错误
            } catch (error) {
                console.error('❌ 检查房间访问权限失败:', error);
                return false;
            }
        }

        // 加载所有配置
        async function loadAllConfigs() {
            const token = localStorage.getItem('auth_token');
            
            try {
                console.log('🔍 开始加载配置数据...');
                
                // 并行加载所有配置
                const [welcomeResponse, antiSilenceResponse, antiSpamResponse, customReplyResponse] = await Promise.all([
                    fetch(`/api/rooms/${ROOM_ID}/welcome-config`, { headers: { 'Authorization': `Bearer ${token}` } }),
                    fetch(`/api/rooms/${ROOM_ID}/anti-silence`, { headers: { 'Authorization': `Bearer ${token}` } }),
                    fetch(`/api/rooms/${ROOM_ID}/anti-spam-config`, { headers: { 'Authorization': `Bearer ${token}` } }),
                    fetch(`/api/rooms/${ROOM_ID}/custom-reply`, { headers: { 'Authorization': `Bearer ${token}` } })
                ]);

                console.log('🔍 API响应状态:', {
                    welcome: welcomeResponse.status,
                    antiSilence: antiSilenceResponse.status,
                    antiSpam: antiSpamResponse.status,
                    customReply: customReplyResponse.status
                });

                // 处理欢迎配置
                if (welcomeResponse.ok) {
                    const welcomeData = await welcomeResponse.json();
                    console.log('🔍 欢迎配置数据:', welcomeData);
                    if (welcomeData.success) {
                        loadWelcomeConfig(welcomeData.config);
                    }
                } else if (welcomeResponse.status === 403) {
                    showMessage('您没有访问此直播间配置的权限', 'error');
                    setTimeout(() => window.location.href = '/', 2000);
                    return;
                }

                // 处理防冷场配置
                if (antiSilenceResponse.ok) {
                    const antiSilenceData = await antiSilenceResponse.json();
                    console.log('🔍 防冷场配置数据:', antiSilenceData);
                    if (antiSilenceData.success) {
                        loadAntiSilenceConfig(antiSilenceData.config);
                    }
                }

                // 处理防刷屏配置
                if (antiSpamResponse.ok) {
                    const antiSpamData = await antiSpamResponse.json();
                    console.log('🔍 防刷屏配置数据:', antiSpamData);
                    if (antiSpamData.success) {
                        loadAntiSpamConfig(antiSpamData.config);
                    }
                }

                // 处理自定义回复配置
                if (customReplyResponse.ok) {
                    const customReplyData = await customReplyResponse.json();
                    console.log('🔍 自定义回复配置数据:', customReplyData);
                    if (customReplyData.success) {
                        loadCustomReplyConfig(customReplyData.config);
                    }
                }

                // 隐藏加载状态，显示配置内容
                console.log('✅ 配置数据加载完成，显示配置界面');
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('configContent').style.display = 'block';

            } catch (error) {
                console.error('❌ 加载配置失败:', error);
                showMessage('加载配置失败: ' + error.message, 'error');
                
                // 即使出错也要隐藏加载状态
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('configContent').style.display = 'block';
            }
        }

        // 加载欢迎配置
        function loadWelcomeConfig(config) {
            document.getElementById('welcomeEnabled').checked = config.enabled || false;
            renderWelcomeTemplates(config.templates || []);
        }

        // 渲染欢迎模板
        function renderWelcomeTemplates(templates) {
            const container = document.getElementById('welcomeTemplates');
            container.innerHTML = templates.map((template, index) => `
                <div class="template-item">
                    <div class="template-text">${template}</div>
                    <button class="btn-github btn-danger btn-small" onclick="removeWelcomeTemplate(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        // 添加欢迎模板
        function addWelcomeTemplate() {
            const input = document.getElementById('welcomeTemplate');
            const template = input.value.trim();
            if (!template) return;

            const templates = getCurrentWelcomeTemplates();
            templates.push(template);
            renderWelcomeTemplates(templates);
            input.value = '';
        }

        // 获取当前欢迎模板
        function getCurrentWelcomeTemplates() {
            return Array.from(document.querySelectorAll('#welcomeTemplates .template-text')).map(el => el.textContent);
        }

        // 删除欢迎模板
        function removeWelcomeTemplate(index) {
            const templates = getCurrentWelcomeTemplates();
            templates.splice(index, 1);
            renderWelcomeTemplates(templates);
        }

        // 保存欢迎配置
        async function saveWelcomeConfig() {
            const config = {
                enabled: document.getElementById('welcomeEnabled').checked,
                templates: getCurrentWelcomeTemplates()
            };

            await saveConfig('/api/rooms/' + ROOM_ID + '/welcome-config', config, '欢迎配置');
        }

        // 加载防冷场配置
        function loadAntiSilenceConfig(config) {
            document.getElementById('antiSilenceEnabled').checked = config.enabled || false;
            document.getElementById('antiSilenceDelay').value = config.delay_seconds || 15;
            document.getElementById('antiSilenceCooldown').value = config.cooldown_seconds || 30;
            renderAntiSilenceTemplates(config.templates || []);
        }

        // 渲染防冷场模板
        function renderAntiSilenceTemplates(templates) {
            const container = document.getElementById('antiSilenceTemplates');
            container.innerHTML = templates.map((template, index) => `
                <div class="template-item">
                    <div class="template-text">${template}</div>
                    <button class="btn-github btn-danger btn-small" onclick="removeAntiSilenceTemplate(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        // 添加防冷场模板
        function addAntiSilenceTemplate() {
            const input = document.getElementById('antiSilenceTemplate');
            const template = input.value.trim();
            if (!template) return;

            const templates = getCurrentAntiSilenceTemplates();
            templates.push(template);
            renderAntiSilenceTemplates(templates);
            input.value = '';
        }

        // 获取当前防冷场模板
        function getCurrentAntiSilenceTemplates() {
            return Array.from(document.querySelectorAll('#antiSilenceTemplates .template-text')).map(el => el.textContent);
        }

        // 删除防冷场模板
        function removeAntiSilenceTemplate(index) {
            const templates = getCurrentAntiSilenceTemplates();
            templates.splice(index, 1);
            renderAntiSilenceTemplates(templates);
        }

        // 保存防冷场配置
        async function saveAntiSilenceConfig() {
            const config = {
                enabled: document.getElementById('antiSilenceEnabled').checked,
                delay_seconds: parseInt(document.getElementById('antiSilenceDelay').value),
                cooldown_seconds: parseInt(document.getElementById('antiSilenceCooldown').value),
                templates: getCurrentAntiSilenceTemplates()
            };

            await saveConfig('/api/rooms/' + ROOM_ID + '/anti-silence', config, '防冷场配置');
        }

        // 加载防刷屏配置
        function loadAntiSpamConfig(config) {
            document.getElementById('antiSpamEnabled').checked = config.enabled || false;
            document.getElementById('duplicateThreshold').value = config.duplicate_threshold || 3;
            document.getElementById('frequencyLimit').value = config.frequency_limit || 5;
            document.getElementById('warningMessage').value = config.warning_message || '请不要刷屏，谢谢配合！';
        }

        // 保存防刷屏配置
        async function saveAntiSpamConfig() {
            const config = {
                enabled: document.getElementById('antiSpamEnabled').checked,
                duplicate_threshold: parseInt(document.getElementById('duplicateThreshold').value),
                frequency_limit: parseInt(document.getElementById('frequencyLimit').value),
                time_window: 60,
                warning_enabled: true,
                warning_message: document.getElementById('warningMessage').value
            };

            await saveConfig('/api/rooms/' + ROOM_ID + '/anti-spam-config', config, '防刷屏配置');
        }

        // 加载自定义回复配置
        function loadCustomReplyConfig(config) {
            document.getElementById('customReplyEnabled').checked = config.enabled || false;
            document.getElementById('customReplyCooldown').value = config.cooldown_seconds || 5;
            // 这里可以添加自定义回复规则的渲染逻辑
        }

        // 保存自定义回复配置
        async function saveCustomReplyConfig() {
            const config = {
                enabled: document.getElementById('customReplyEnabled').checked,
                cooldown_seconds: parseInt(document.getElementById('customReplyCooldown').value),
                priority_weight: 8,
                match_mode: 'contains',
                case_sensitive: false
            };

            await saveConfig('/api/rooms/' + ROOM_ID + '/custom-reply', config, '自定义回复配置');
        }

        // 通用保存配置函数
        async function saveConfig(url, config, name) {
            const token = localStorage.getItem('auth_token');
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(config)
                });

                const result = await response.json();
                if (result.success) {
                    showMessage(name + '保存成功！', 'success');
                } else {
                    showMessage(name + '保存失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showMessage(name + '保存失败: ' + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type = 'success') {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            
            messageArea.appendChild(messageDiv);
            
            // 3秒后自动移除消息
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 导航栏相关函数
        function toggleUserDropdown() {
            const menu = document.getElementById('userDropdownMenu');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const menu = document.getElementById('userDropdownMenu');

            if (!dropdown.contains(event.target)) {
                menu.classList.remove('show');
            }
        });

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
    </script>
</body>
</html>