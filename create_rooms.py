#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量创建直播间脚本
"""

import json
import random
import string
import os
from datetime import datetime
from user_management import user_manager

def generate_room_id():
    """生成直播间ID"""
    # 生成11-12位数字ID，模拟真实直播间ID
    return str(random.randint(10000000000, 999999999999))

def generate_room_name():
    """生成直播间名称"""
    prefixes = [
        "小", "大", "美", "新", "老", "好", "超", "特", "精", "优",
        "热", "火", "酷", "萌", "甜", "暖", "清", "雅", "潮", "炫"
    ]
    
    subjects = [
        "主播", "达人", "网红", "明星", "歌手", "舞者", "画家", "厨师",
        "游戏", "音乐", "美食", "旅行", "时尚", "美妆", "健身", "读书"
    ]
    
    suffixes = [
        "直播间", "工作室", "小屋", "天地", "世界", "乐园", "基地", "中心",
        "频道", "空间", "角落", "花园", "城堡", "小站", "港湾", "家园"
    ]
    
    return random.choice(prefixes) + random.choice(subjects) + random.choice(suffixes)

def get_room_template():
    """获取房间模板"""
    template_file = "config/rooms/template_with_new_features.json"
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            template = json.load(f)
        return template
    except Exception as e:
        print(f"❌ 读取房间模板失败: {e}")
        return None

def create_room_config(room_id, room_name, owner_username=None):
    """创建房间配置"""
    template = get_room_template()
    if not template:
        return None
    
    # 复制模板
    room_config = template.copy()
    
    # 更新房间信息
    room_config.update({
        "room_id": room_id,
        "room_name": room_name,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "owner": owner_username or "admin",
        "status": "stopped",
        "last_started": None,
        "last_stopped": None,
        "total_runtime": 0,
        "error_count": 0,
        "last_error": None
    })
    
    # 随机分配设备（如果有的话）
    available_devices = ["device1", "device2", "device3", "device4", "device5"]
    room_config["device_id"] = random.choice(available_devices)
    
    # 随机设置一些参数
    room_config["auto_reply"]["enabled"] = random.choice([True, False])
    room_config["gift_thanks"]["enabled"] = random.choice([True, False])
    room_config["follow_thanks"]["enabled"] = random.choice([True, False])
    
    return room_config

def save_room_config(room_config):
    """保存房间配置"""
    room_id = room_config["room_id"]
    config_file = f"config/rooms/{room_id}.json"
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(room_config, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"❌ 保存房间配置失败 {room_id}: {e}")
        return False

def create_rooms_batch(count=10, assign_to_users=True):
    """批量创建直播间"""
    print(f"🏠 批量创建 {count} 个直播间...")
    
    # 获取所有用户（用于分配房间）
    users = []
    if assign_to_users:
        try:
            all_users = user_manager.get_all_users_with_member_info()
            # 只分配给会员用户
            users = [user for user in all_users if user.get('member_status') in ['active', 'permanent']]
            print(f"📊 找到 {len(users)} 个会员用户可分配直播间")
        except Exception as e:
            print(f"❌ 获取用户列表失败: {e}")
            users = []
    
    created_rooms = []
    
    for i in range(count):
        try:
            # 生成房间信息
            room_id = generate_room_id()
            room_name = generate_room_name()
            
            # 分配房间所有者
            owner = None
            if users and assign_to_users:
                owner = random.choice(users)['username']
            
            # 创建房间配置
            room_config = create_room_config(room_id, room_name, owner)
            if not room_config:
                print(f"❌ 创建房间配置失败: {room_id}")
                continue
            
            # 保存房间配置
            if save_room_config(room_config):
                print(f"✅ 创建直播间: {room_id} ({room_name}) - 所有者: {owner or 'admin'}")
                created_rooms.append({
                    'room_id': room_id,
                    'room_name': room_name,
                    'owner': owner or 'admin',
                    'device_id': room_config['device_id']
                })
            else:
                print(f"❌ 保存直播间失败: {room_id}")
                
        except Exception as e:
            print(f"❌ 创建直播间异常: {e}")
    
    print(f"✅ 成功创建 {len(created_rooms)} 个直播间")
    return created_rooms

def create_demo_rooms():
    """创建演示直播间"""
    demo_rooms = [
        {
            'room_id': '100001',
            'room_name': '演示直播间001',
            'owner': 'demo001'
        },
        {
            'room_id': '100002',
            'room_name': '演示直播间002', 
            'owner': 'demo002'
        },
        {
            'room_id': '100003',
            'room_name': '测试直播间',
            'owner': 'admin'
        }
    ]
    
    print("🎭 创建演示直播间...")
    
    created_demo_rooms = []
    
    for room_info in demo_rooms:
        try:
            # 创建房间配置
            room_config = create_room_config(
                room_info['room_id'],
                room_info['room_name'],
                room_info['owner']
            )
            
            if not room_config:
                print(f"❌ 创建演示房间配置失败: {room_info['room_id']}")
                continue
            
            # 保存房间配置
            if save_room_config(room_config):
                print(f"✅ 创建演示直播间: {room_info['room_id']} ({room_info['room_name']}) - 所有者: {room_info['owner']}")
                created_demo_rooms.append(room_info)
            else:
                print(f"❌ 保存演示直播间失败: {room_info['room_id']}")
                
        except Exception as e:
            print(f"❌ 创建演示直播间异常: {e}")
    
    return created_demo_rooms

def save_room_list(rooms, filename="created_rooms.json"):
    """保存直播间列表到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(rooms, f, ensure_ascii=False, indent=2)
        print(f"📁 直播间列表已保存到: {filename}")
    except Exception as e:
        print(f"❌ 保存直播间列表失败: {e}")

def main():
    """主函数"""
    print("🏠 直播间批量创建工具")
    print("=" * 50)
    
    # 检查模板文件
    template_file = "config/rooms/template_with_new_features.json"
    if not os.path.exists(template_file):
        print(f"❌ 房间模板文件不存在: {template_file}")
        print("请确保模板文件存在后再运行此脚本")
        return
    
    print("请选择创建方式:")
    print("1. 创建演示直播间 (3个固定直播间)")
    print("2. 批量创建随机直播间")
    print("3. 创建演示直播间 + 批量随机直播间")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    all_rooms = []
    
    if choice in ['1', '3']:
        # 创建演示直播间
        demo_rooms = create_demo_rooms()
        all_rooms.extend(demo_rooms)
    
    if choice in ['2', '3']:
        # 批量创建直播间
        try:
            count = int(input("请输入要创建的直播间数量 (默认10): ") or "10")
            assign_choice = input("是否分配给会员用户? (y/n, 默认y): ").strip().lower()
            assign_to_users = assign_choice != 'n'
            
            if count <= 0 or count > 500:
                print("❌ 直播间数量必须在1-500之间")
                return
            
            batch_rooms = create_rooms_batch(count, assign_to_users)
            all_rooms.extend(batch_rooms)
            
        except ValueError:
            print("❌ 输入格式错误")
            return
    
    if all_rooms:
        # 保存直播间列表
        save_room_list(all_rooms)
        
        print("\n" + "=" * 50)
        print("🎉 直播间创建完成！")
        print(f"📊 总计创建直播间: {len(all_rooms)} 个")
        print(f"📁 直播间信息已保存到: created_rooms.json")
        print("\n💡 提示:")
        print("   - 可以在主页面查看和管理这些直播间")
        print("   - 演示直播间ID: 100001, 100002, 100003")
        print("   - 所有直播间都基于模板创建，可以进一步自定义配置")
    else:
        print("❌ 没有创建任何直播间")

if __name__ == "__main__":
    main()
