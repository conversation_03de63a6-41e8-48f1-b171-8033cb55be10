# GitHub风格UI与后端功能集成文档

## 🎯 概述

本文档说明了如何使用新的GitHub风格UI界面与现有的后端功能进行交互。所有页面都已经连接到真实的API端点，提供完整的功能体验。

## 🔐 认证系统

### 登录流程
1. 访问 `http://localhost:15008/login`
2. 使用默认管理员账户：
   - 用户名: `admin`
   - 密码: `admin123`
3. 登录成功后会自动跳转到主仪表板

### 认证机制
- 使用JWT令牌进行身份验证
- 令牌存储在localStorage中
- 所有API请求都会自动携带认证头
- 令牌过期会自动跳转到登录页面

## 🏠 主仪表板 (`/`)

### 实时数据显示
- **房间统计**: 显示总房间数和活跃房间数
- **设备统计**: 显示总设备数和在线设备数
- **数据自动刷新**: 页面加载时自动获取最新数据

### 房间管理功能
- **查看房间列表**: 显示所有配置的直播间及其状态
- **启动/停止房间**: 点击对应按钮控制房间运行状态
- **创建新房间**: 点击"创建直播间"按钮，按提示输入信息
- **房间配置**: 点击"配置"按钮跳转到房间设置页面

### 设备管理功能
- **查看设备列表**: 显示所有注册的设备及其状态
- **添加新设备**: 点击"添加设备"按钮，输入设备信息
- **设备扫描**: 点击"扫描设备"自动发现网络中的设备
- **设备测试**: 测试设备连接状态

### 快捷操作
- **刷新数据**: 点击刷新按钮更新所有数据
- **紧急停止**: 一键停止所有运行中的房间

## 👥 用户管理 (`/admin`)

### 用户列表
- 显示所有系统用户
- 包含用户名、角色、状态、注册时间等信息
- 支持用户头像显示

### 用户操作
- **创建用户**: 点击"创建新用户"按钮
  - 输入用户名、密码、显示名称、邮箱
  - 选择用户角色（管理员/普通用户）
- **编辑用户**: 点击"编辑"按钮修改用户信息
- **删除用户**: 点击"删除"按钮移除用户（管理员账户不能删除）
- **用户状态**: 启用/禁用用户账户

### 权限管理
- **管理员**: 拥有所有权限
- **普通用户**: 只能查看和操作自己的数据

## 🏆 排行榜 (`/leaderboard`)

### 数据统计
- **总用户数**: 系统中的用户总数
- **总消息数**: 所有房间的消息总数
- **成功互动**: 有效互动次数
- **礼物价值**: 礼物总价值

### 排行榜功能
- **房间选择**: 可以选择特定房间或查看全部房间数据
- **消息排行榜**: 按用户消息数量排序
- **礼物排行榜**: 按用户礼物价值排序
- **实时更新**: 数据会定期自动更新

### 数据筛选
- 支持按房间筛选数据
- 支持设置显示条目数量
- 支持导出排行榜数据

## 🔑 激活码管理 (`/activation-codes`)

### 统计概览
- **总激活码**: 系统中的激活码总数
- **未使用**: 可用的激活码数量
- **已使用**: 已被使用的激活码数量
- **已过期**: 过期的激活码数量

### 激活码生成
- **批量生成**: 一次生成1-100个激活码
- **类型选择**: 高级版、标准版、试用版
- **有效期设置**: 1-365天有效期
- **自动编码**: 系统自动生成唯一激活码

### 激活码管理
- **查看列表**: 显示所有激活码及其状态
- **复制激活码**: 一键复制到剪贴板
- **删除激活码**: 删除未使用的激活码
- **查看详情**: 查看激活码的详细信息
- **导出功能**: 导出激活码列表为CSV文件

### 状态管理
- **未使用**: 绿色标签，可以被使用
- **已使用**: 黄色标签，显示使用者信息
- **已过期**: 红色标签，不能再使用

## 🔧 API端点说明

### 认证相关
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/verify` - 验证令牌

### 系统状态
- `GET /api/status` - 获取系统状态（房间、设备等）

### 房间管理
- `GET /api/rooms` - 获取房间列表
- `POST /api/rooms` - 创建新房间
- `POST /api/rooms/{id}/start` - 启动房间
- `POST /api/rooms/{id}/stop` - 停止房间
- `POST /api/rooms/stop-all` - 停止所有房间

### 设备管理
- `GET /api/devices` - 获取设备列表
- `POST /api/devices` - 添加新设备
- `POST /api/devices/scan-network` - 扫描网络设备

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建新用户
- `GET /api/users/{username}` - 获取用户信息
- `PUT /api/users/{username}` - 更新用户信息
- `DELETE /api/users/{username}` - 删除用户
- `PUT /api/users/{username}/status` - 切换用户状态

### 排行榜
- `GET /api/leaderboard/stats` - 获取统计数据
- `GET /api/leaderboard/interactions` - 获取互动排行榜
- `GET /api/leaderboard/gifts` - 获取礼物排行榜

### 激活码管理
- `GET /api/activation-codes` - 获取激活码列表
- `POST /api/activation-codes/batch` - 批量生成激活码
- `POST /api/activation-codes/batch-delete` - 批量删除激活码

## 🎨 UI特性

### GitHub风格设计
- 统一的色彩系统和组件库
- 响应式布局，适配各种屏幕尺寸
- 清晰的状态指示和反馈

### 交互体验
- 实时数据更新
- 友好的错误提示
- 加载状态指示
- 操作确认对话框

### 数据展示
- 表格形式展示列表数据
- 状态标签清晰标识
- 统计卡片直观显示数据
- 操作按钮便于快速操作

## 🚀 使用建议

1. **首次使用**: 先登录系统，熟悉各个页面的功能
2. **设备管理**: 先添加设备，再创建房间
3. **房间配置**: 创建房间时需要有效的激活码
4. **数据监控**: 定期查看排行榜了解系统运行状况
5. **用户管理**: 根据需要创建不同权限的用户账户

## 🔍 故障排除

### 常见问题
1. **登录失败**: 检查用户名密码是否正确
2. **数据不显示**: 检查网络连接和服务器状态
3. **操作失败**: 查看浏览器控制台的错误信息
4. **权限不足**: 确认当前用户有相应操作权限

### 调试方法
1. 打开浏览器开发者工具
2. 查看Network标签页的API请求
3. 查看Console标签页的错误信息
4. 检查localStorage中的认证令牌

## 📝 注意事项

1. **安全性**: 生产环境中请修改默认密码
2. **备份**: 定期备份用户数据和配置
3. **监控**: 关注系统资源使用情况
4. **更新**: 及时更新系统和依赖包

---

通过以上功能，您可以完全通过Web界面管理整个自动化直播系统，无需直接操作配置文件或命令行。
