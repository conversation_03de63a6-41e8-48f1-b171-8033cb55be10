#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Web服务器 - 简化版
与WebSocket服务器配合工作，管理多个直播间机器人
"""

from flask import Flask, render_template, jsonify, request, make_response, session, redirect
import json
import os
import logging
import threading
import time
from datetime import datetime
from functools import wraps
from manage_processes import ProcessManager

from user_management import user_manager
from leaderboard_stats import leaderboard_stats
from membership_codes import membership_manager, invitation_manager
from announcement_manager import announcement_manager

# 添加WebSocket客户端支持用于配置同步
try:
    import asyncio
    import websockets
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("⚠️ WebSocket客户端不可用，配置同步功能将被禁用")

app = Flask(__name__)
# 生产环境必须设置环境变量 FLASK_SECRET_KEY
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'dev-key-only-for-development-change-in-production')

# 添加禁用缓存的装饰器
def no_cache(f):
    """禁用缓存的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = make_response(f(*args, **kwargs))
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
    return decorated_function

# 配置日志过滤
class RequestFilter(logging.Filter):
    """过滤HTTP请求日志"""
    def filter(self, record):
        # 过滤频繁的API请求日志
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            
            # 过滤设备相关API
            if 'GET /api/devices/list' in message:
                return False
            if 'GET /api/device-status' in message:
                return False
                
            # 过滤状态查询API
            if 'GET /api/status' in message:
                return False
            if 'GET /api/processes' in message:
                return False
            if 'GET /api/rooms' in message:
                return False
                
            # 过滤配置相关API
            if 'GET /api/broadcast-config' in message:
                return False
            if 'GET /api/game-config' in message:
                return False
                
            # 过滤排行榜相关API（频繁请求）
            if 'GET /api/leaderboard/' in message:
                return False
            if 'POST /api/leaderboard/' in message:
                return False

            # 过滤内部API请求（最频繁的）
            if 'GET /api/internal/devices' in message:
                return False
            if 'GET /api/internal/status' in message:
                return False
            if 'GET /api/internal/rooms' in message:
                return False

            # 过滤静态资源请求
            if any(ext in message for ext in ['.css', '.js', '.png', '.jpg', '.ico', '.woff']):
                return False
                
        return True

# 应用日志过滤器
werkzeug_logger = logging.getLogger('werkzeug')
werkzeug_logger.addFilter(RequestFilter())

# 获取管理器实例
pm = ProcessManager()

# 系统初始化函数（预留）
def _initialize_system():
    """系统初始化函数，仅在直接运行时调用"""
    print("系统初始化完成")

def load_device_configs():
    """加载设备配置（支持新旧配置结构）"""
    try:
        # 优先使用新的配置结构
        if os.path.exists('config/devices/devices.json'):
            with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        # 兼容旧的配置结构
        elif os.path.exists('devices_config.json'):
            with open('devices_config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载设备配置失败: {e}")
    return {}

def get_connected_devices():
    """获取已连接的设备列表"""
    try:
        # 通过WebSocket数据获取设备列表
        data = get_websocket_data()
        return data.get('devices', [])
    except Exception as e:
        print(f"获取连接设备失败: {e}")
    return []

# 从运行时数据文件获取WebSocket服务器数据
def get_websocket_data():
    """从运行时数据文件获取数据"""
    try:
        if os.path.exists('runtime_data.json'):
            with open('runtime_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {
                    'connected_devices': data.get('connected_devices', {}),
                    'device_configs': data.get('device_configs', {}),
                    'devices_list': data.get('devices_list', []),
                    'status': data.get('status', {'status': 'offline', 'connected_devices': 0, 'total_devices': 0})
                }
    except Exception as e:
        print(f"⚠️ 读取运行时数据失败: {e}")

    return {
        'connected_devices': {},
        'device_configs': {},
        'devices_list': [],
        'status': {'status': 'offline', 'connected_devices': 0, 'total_devices': 0}
    }

def save_websocket_data(data):
    """保存运行时数据到文件"""
    try:
        with open('runtime_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"⚠️ 保存运行时数据失败: {e}")
        return False

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 加载配置文件失败: {e}")
        return {}

def save_config(config):
    """保存配置文件"""
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"⚠️ 保存配置文件失败: {e}")
        return False

import threading
config_lock = threading.Lock()

# 全局设备发送锁机制
device_send_locks = {}
device_locks_lock = threading.Lock()  # 保护device_send_locks字典的锁

def get_device_lock(device_id):
    """获取设备专用锁"""
    with device_locks_lock:
        if device_id not in device_send_locks:
            device_send_locks[device_id] = threading.Lock()
        return device_send_locks[device_id]

def cleanup_device_locks():
    """清理未使用的设备锁（可选的内存优化）"""
    with device_locks_lock:
        # 这里可以添加清理逻辑，比如清理长时间未使用的锁
        pass

def load_rooms_config():
    """加载房间配置（支持新旧配置结构）"""
    with config_lock:
        try:
            # 优先使用新的配置结构
            if os.path.exists('config/rooms'):
                rooms = {}
                for filename in os.listdir('config/rooms'):
                    if filename.endswith('.json'):
                        live_id = filename[:-5]  # 移除.json后缀
                        try:
                            with open(f'config/rooms/{filename}', 'r', encoding='utf-8') as f:
                                room_config = json.load(f)
                                rooms[live_id] = room_config
                        except Exception as e:
                            print(f"❌ 加载房间配置失败 {filename}: {e}")
                return rooms

            # 兼容旧的配置结构
            elif os.path.exists('rooms_config.json'):
                with open('rooms_config.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"🔧 尝试修复配置文件...")
            return {}
        except Exception as e:
            print(f"❌ 加载房间配置失败: {e}")
        return {}

def notify_websocket_config_change():
    """通知WebSocket服务器配置已更改"""
    if not WEBSOCKET_AVAILABLE:
        return False

    def send_reload_request():
        try:
            async def notify():
                uri = "ws://localhost:15000"
                async with websockets.connect(uri, ping_timeout=5) as websocket:
                    reload_message = {
                        "type": "reload_config",
                        "data": {
                            "source": "flask_web_server",
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await websocket.send(json.dumps(reload_message))

                    # 等待响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    if response_data.get("type") == "reload_config_response":
                        print(f"✅ WebSocket服务器配置重新加载成功")
                        return True
                    return False

            # 在新的事件循环中运行
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(notify())
            loop.close()
            return result

        except Exception as e:
            print(f"⚠️ 通知WebSocket服务器失败: {e}")
            return False

    # 在后台线程中执行，避免阻塞Flask
    threading.Thread(target=send_reload_request, daemon=True).start()
    return True

def save_rooms_config(rooms):
    """保存房间配置（使用新的配置结构）"""
    with config_lock:
        try:
            # 确保配置目录存在
            os.makedirs('config/rooms', exist_ok=True)

            # 获取当前存在的配置文件
            existing_files = set()
            if os.path.exists('config/rooms'):
                for filename in os.listdir('config/rooms'):
                    if filename.endswith('.json'):
                        existing_files.add(filename[:-5])  # 移除.json后缀

            # 分别保存每个房间的配置
            current_rooms = set()
            global_config_keys = set()  # 收集全局配置键

            for live_id, room_config in rooms.items():
                # 更严格的全局配置键识别
                # 全局配置键通常是英文单词，房间ID通常是数字或数字字符串
                is_global_config = (
                    live_id in ['welcome_message', 'global_settings', 'scheduled_broadcast',
                               'anti_spam', 'like_interaction', 'anti_silence', 'default_templates',
                               'websocket_settings', 'message_queue', 'logging', 'status_monitor'] or
                    (isinstance(live_id, str) and live_id.isalpha() and len(live_id) > 3)  # 纯英文且长度>3的键
                )

                if is_global_config:
                    global_config_keys.add(live_id)
                    continue

                current_rooms.add(live_id)
                config_path = f'config/rooms/{live_id}.json'
                temp_file = f'{config_path}.tmp'

                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(room_config, f, ensure_ascii=False, indent=2)

                # 原子性替换
                import shutil
                shutil.move(temp_file, config_path)

            # 如果有全局配置，保存到全局配置文件
            if global_config_keys:
                global_config = {key: rooms[key] for key in global_config_keys}
                global_config_path = 'config/global_config_from_rooms.json'
                try:
                    with open(global_config_path, 'w', encoding='utf-8') as f:
                        json.dump(global_config, f, ensure_ascii=False, indent=2)
                    print(f"📝 保存全局配置到: {global_config_path}")
                except Exception as e:
                    print(f"⚠️ 保存全局配置失败: {e}")

            # 删除不再存在的房间配置文件
            rooms_to_delete = existing_files - current_rooms
            for room_id in rooms_to_delete:
                config_path = f'config/rooms/{room_id}.json'
                if os.path.exists(config_path):
                    os.remove(config_path)
                    print(f"🗑️ 删除房间配置文件: {config_path}")

            # 通知WebSocket服务器配置已更改
            notify_websocket_config_change()

            return True
        except Exception as e:
            print(f"❌ 保存房间配置失败: {e}")
            return False

# ==================== 权限装饰器 ====================
def require_auth(f):
    """需要登录的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if token and token.startswith('Bearer '):
            token = token[7:]  # 移除 'Bearer ' 前缀
        else:
            token = request.args.get('token') or request.form.get('token')

        if not token:
            return jsonify({'success': False, 'message': '未提供认证令牌', 'code': 'NO_TOKEN'}), 401

        session_data = user_manager.verify_session(token)
        if not session_data:
            return jsonify({'success': False, 'message': '认证令牌无效或已过期', 'code': 'INVALID_TOKEN'}), 401

        # 将用户信息添加到请求上下文
        request.current_user = session_data
        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """需要管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user') or request.current_user.get('role') != 'admin':
            return jsonify({'success': False, 'message': '需要管理员权限', 'code': 'ADMIN_REQUIRED'}), 403
        return f(*args, **kwargs)
    return decorated_function

def require_page_auth(f):
    """页面需要登录的装饰器（使用JavaScript检查认证）"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 返回一个包含认证检查的HTML页面
        auth_check_html = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>认证检查中...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f5f5f5;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="loading">
        <p>正在验证身份...</p>
    </div>
    <script>
        // 检查localStorage中的token
        const token = localStorage.getItem('auth_token');
        if (!token) {
            window.location.href = '/login';
        } else {
            // 验证token
            fetch('/api/auth/verify', {
                headers: {
                    'Authorization': 'Bearer ' + token
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.user && data.user.role === 'admin') {
                    // 认证成功且是管理员，重新加载页面但这次会通过认证
                    window.location.reload();
                } else {
                    window.location.href = '/login';
                }
            })
            .catch(() => {
                window.location.href = '/login';
            });
        }
    </script>
</body>
</html>
        '''

        # 检查是否有有效的token（通过header传递，表示这是第二次请求）
        token = request.headers.get('Authorization')
        if token and token.startswith('Bearer '):
            token = token[7:]
            session_data = user_manager.verify_session(token)
            if session_data:
                request.current_user = session_data
                return f(*args, **kwargs)

        # 如果没有有效认证，返回认证检查页面
        return auth_check_html
    return decorated_function

def require_page_admin(f):
    """页面需要管理员权限的装饰器（重定向到登录页面）"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user') or request.current_user.get('role') != 'admin':
            return redirect('/login')
        return f(*args, **kwargs)
    return decorated_function

def check_room_access(f):
    """检查房间访问权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        room_id = kwargs.get('room_id')
        if room_id and not user_manager.can_access_room(request.current_user['username'], room_id):
            return jsonify({'success': False, 'message': '无权访问该房间', 'code': 'ROOM_ACCESS_DENIED'}), 403
        return f(*args, **kwargs)
    return decorated_function

def check_device_access(f):
    """检查设备访问权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        device_id = kwargs.get('device_id') or request.json.get('device_id') if request.json else None
        if device_id and not user_manager.can_access_device(request.current_user['username'], device_id):
            return jsonify({'success': False, 'message': '无权访问该设备', 'code': 'DEVICE_ACCESS_DENIED'}), 403
        return f(*args, **kwargs)
    return decorated_function

# ==================== 认证API ====================
@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        print("[DEBUG] 收到登录请求")
        data = request.get_json()
        print(f"[DEBUG] 请求数据: {data}")

        if not data:
            print("[ERROR] 请求数据为空")
            return jsonify({'success': False, 'message': '请求数据为空'})

        username = data.get('username', '').strip()
        password = data.get('password', '')
        print(f"[DEBUG] 用户名: {username}, 密码长度: {len(password) if password else 0}")

        if not username or not password:
            print("[ERROR] 用户名或密码为空")
            return jsonify({'success': False, 'message': '用户名和密码不能为空'})

        print("[DEBUG] 调用用户管理器认证")
        result = user_manager.authenticate(username, password)
        print(f"[DEBUG] 认证结果: {result}")

        # 如果登录成功，设置session
        if result.get('success') and result.get('token'):
            session['auth_token'] = result['token']
            print("[SUCCESS] 已设置session token")

        return jsonify(result)

    except Exception as e:
        print(f"[ERROR] 登录异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'登录失败: {str(e)}'})

@app.route('/api/auth/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出"""
    try:
        token = request.headers.get('Authorization', '').replace('Bearer ', '')
        user_manager.logout(token)

        # 清除session
        session.pop('auth_token', None)
        print(f"✅ 已清除session token")

        return jsonify({'success': True, 'message': '登出成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'登出失败: {str(e)}'})

@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        print(f"🔍 收到注册请求")
        data = request.get_json()
        print(f"🔍 注册数据: {data}")

        if not data:
            print(f"❌ 请求数据为空")
            return jsonify({'success': False, 'message': '请求数据为空'})

        username = data.get('username', '').strip()
        password = data.get('password', '')
        display_name = data.get('display_name', '').strip()
        email = data.get('email', '').strip()
        invitation_code = data.get('invitation_code', '').strip()

        # 验证输入
        if not username or not password or not display_name or not invitation_code:
            return jsonify({'success': False, 'message': '用户名、密码、昵称和邀请码不能为空'})

        if len(username) < 4:
            return jsonify({'success': False, 'message': '用户名长度至少4位'})

        # 允许用户名包含字母、数字、下划线和连字符
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return jsonify({'success': False, 'message': '用户名只能包含英文字母、数字、下划线和连字符'})

        if len(password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少6位'})

        if email and '@' not in email:
            return jsonify({'success': False, 'message': '邮箱格式不正确'})

        # 验证会员码/邀请码
        is_valid, message, code_info = membership_manager.validate_membership_code(invitation_code)
        if not is_valid:
            return jsonify({'success': False, 'message': f'邀请码验证失败: {message}'})

        # 检查用户名是否已存在
        if user_manager.get_user_info(username):
            return jsonify({'success': False, 'message': '用户名已存在'})

        # 创建新用户
        result = user_manager.create_user(
            username=username,
            password=password,
            display_name=display_name,
            email=email,
            role='user'  # 默认为普通用户
        )

        if result['success']:
            # 使用会员码/邀请码
            use_success, use_message, code_info = membership_manager.use_membership_code(invitation_code, username)
            if not use_success:
                print(f"⚠️ 会员码使用失败: {use_message}")
                # 注册成功但会员码使用失败，记录警告但不影响注册
            else:
                # 会员码使用成功，激活用户会员权限
                if code_info and 'membership_days' in code_info:
                    membership_days = code_info['membership_days']
                    membership_result = user_manager.activate_member(username, membership_days)
                    if membership_result['success']:
                        print(f"✅ 用户 {username} 会员已激活，有效期 {membership_days} 天")
                    else:
                        print(f"⚠️ 激活会员失败: {membership_result['message']}")

            # 记录用户使用的会员码/邀请码
            user_manager.set_invitation_code(username, invitation_code)

            print(f"✅ 用户注册成功: {username}")
            return jsonify({
                'success': True,
                'message': '注册成功',
                'user': {
                    'username': username,
                    'display_name': display_name,
                    'email': email,
                    'role': 'user'
                }
            })
        else:
            print(f"❌ 用户注册失败: {username}")
            return jsonify({'success': False, 'message': result.get('message', '注册失败，请稍后重试')})

    except Exception as e:
        print(f"❌ 注册异常: {e}")
        return jsonify({'success': False, 'message': f'注册失败: {str(e)}'})

@app.route('/api/auth/verify', methods=['GET'])
@require_auth
def verify_token():
    """验证令牌"""
    return jsonify({
        'success': True,
        'user': {
            'username': request.current_user['username'],
            'role': request.current_user['role']
        }
    })

# ==================== Web界面 ====================
@app.route('/')
@no_cache
def index():
    """主页"""
    return render_template('index.html')

@app.route('/login')
@no_cache
def login_page():
    """登录页面"""
    return render_template('login.html')

@app.route('/invitation-codes')
@no_cache
def invitation_codes_page():
    """邀请码管理页面（仅管理员）"""
    return render_template('invitation-codes.html')

@app.route('/announcements')
@no_cache
def announcements_page():
    """公告管理页面（仅管理员）"""
    return render_template('announcements.html')

@app.route('/users')
@no_cache
def users_page():
    """用户管理页面（仅管理员）"""
    return render_template('users.html')



@app.route('/leaderboard')
@no_cache
def leaderboard():
    """排行榜页面"""
    return render_template('leaderboard.html')



@app.route('/profile')
@no_cache
def profile_page():
    """个人设置页面"""
    return render_template('profile.html')

@app.route('/room/<room_id>/config')
@no_cache
def room_config_page(room_id):
    """直播间配置页面"""
    return render_template('room-config.html', room_id=room_id)

@app.route('/test_api.html')
def test_api():
    """API测试页面"""
    try:
        with open('test_api.html', 'r', encoding='utf-8') as f:
            content = f.read()
        return content, 200, {'Content-Type': 'text/html; charset=utf-8'}
    except FileNotFoundError:
        return "测试页面不存在", 404

# ==================== API接口 ====================

@app.route('/api/status')
@no_cache
def get_status():
    """获取系统状态"""
    try:
        ws_data = get_websocket_data()
        
        # 更新进程状态
        pm.update_process_status()
        process_status = pm.get_all_processes_status()
        
        # 优先使用配置文件中的设备数据，而不是WebSocket运行时数据
        device_configs = load_device_configs()
        devices_list = []
        
        # 从配置文件构建设备列表
        for device_id, device_config in device_configs.items():
            device_info = {
                'device_id': device_id,
                'device_name': device_config.get('device_name', '未命名设备'),
                'device_type': device_config.get('device_type', 'esp32'),
                'online': False,  # 默认离线
                'status': 'offline',
                'last_seen': device_config.get('last_seen'),
                'ip_address': device_config.get('ip_address'),
                'auto_registered': device_config.get('auto_registered', False)
            }
            
            # 尝试从WebSocket数据中获取在线状态
            ws_devices = ws_data.get('devices_list', [])
            for ws_device in ws_devices:
                if ws_device.get('device_id') == device_id:
                    device_info['online'] = ws_device.get('online', False)
                    device_info['status'] = 'online' if ws_device.get('online') else 'offline'
                    device_info['device_state'] = ws_device.get('device_state', 'offline')
                    device_info['connect_time'] = ws_device.get('connect_time')
                    break
                    
            devices_list.append(device_info)
        
        print(f"[DEBUG] 状态API返回 {len(devices_list)} 个配置文件中的设备")
        
        return jsonify({
            'status': ws_data['status'],
            'rooms': process_status,
            'deviceResponse': {
                'success': True,
                'devices': devices_list
            },
            'processResponse': {
                'success': True,
                'processes': process_status
            }
        })
    except Exception as e:
        print(f"[ERROR] 获取系统状态异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)})

@app.route('/api/rooms')
@require_auth
def get_rooms():
    """获取房间列表（根据用户权限过滤）"""
    try:
        print(f"🔍 收到房间列表请求")
        rooms = load_rooms_config()
        print(f"🔍 从配置文件加载到 {len(rooms)} 个房间")
        username = request.current_user['username']
        user_role = request.current_user['role']
        print(f"🔍 用户 {username} ({user_role}) 请求房间列表")

        # 更新进程状态
        pm.update_process_status()
        process_status = pm.get_all_processes_status()

        # 过滤掉非房间配置项（如 anti_silence, welcome_message 等）
        valid_rooms = {}
        for room_id, room_config in rooms.items():
            # 跳过配置项，只保留真正的房间
            if (room_id in ['anti_silence', 'welcome_message', 'global_settings'] or
                not isinstance(room_config, dict)):
                continue

            # 检查是否是有效的房间配置（支持新旧格式）
            has_live_id = ('live_id' in room_config or
                          (isinstance(room_config.get('live_settings'), dict) and
                           'live_id' in room_config['live_settings']))

            if not has_live_id:
                continue

            # 获取实际的live_id
            actual_live_id = room_config.get('live_id')
            if not actual_live_id and isinstance(room_config.get('live_settings'), dict):
                actual_live_id = room_config['live_settings'].get('live_id')

            # 跳过模板房间
            if actual_live_id == 'YOUR_LIVE_ID':
                continue

            # 权限检查：管理员可以看到所有房间，普通用户只能看到分配的房间
            if user_role == 'admin':
                valid_rooms[room_id] = room_config
                # print(f"🔍 管理员用户，添加房间: {room_id}")
            elif user_manager.can_access_room(username, room_id):
                valid_rooms[room_id] = room_config
                # print(f"🔍 普通用户有权限，添加房间: {room_id}")
            else:
                # print(f"🔍 用户无权限访问房间: {room_id}")
                pass

        # 合并配置和运行状态，并扁平化数据结构
        for room_id, room_config in valid_rooms.items():
            # 扁平化房间信息，方便前端使用
            room_info = room_config.get('room_info', {})
            live_settings = room_config.get('live_settings', {})
            esp32_settings = room_config.get('esp32_settings', {})

            # 添加扁平化字段
            room_config.update({
                'room_name': room_info.get('room_name', room_config.get('room_name', '未知房间')),
                'enabled': room_info.get('enabled', room_config.get('enabled', True)),
                'live_id': live_settings.get('live_id', room_config.get('live_id', room_id)),
                'target_device_id': esp32_settings.get('device_id', room_config.get('target_device_id', ''))
            })

            # 添加运行状态
            if room_id in process_status:
                process_info = process_status[room_id]
                room_config.update({
                    'is_running': process_info.get('is_running', False),
                    'pid': process_info.get('pid'),
                    'running_time_seconds': process_info.get('running_time_seconds', 0)
                })
            else:
                room_config.update({
                    'is_running': False,
                    'pid': None,
                    'running_time_seconds': 0
                })

        print(f"🔍 管理员用户，返回 {len(valid_rooms)} 个房间")
        return jsonify({
            'success': True,
            'rooms': valid_rooms,
            'processes': process_status
        })
    except Exception as e:
        print(f"❌ 获取房间列表失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': str(e),
            'rooms': {},
            'processes': {}
        })



@app.route('/api/rooms', methods=['POST'])
@require_auth
def create_room():
    """创建房间"""
    try:
        data = request.get_json()
        print(f"🔍 创建房间请求数据: {data}")

        live_id = data.get('live_id')
        room_name = data.get('room_name')
        target_device_id = data.get('target_device_id')

        print(f"🔍 解析参数: live_id={live_id}, room_name={room_name}, target_device_id={target_device_id}")

        if not live_id or not room_name or not target_device_id:
            error_msg = f'房间ID、房间名称和目标设备ID不能为空。当前值: live_id={live_id}, room_name={room_name}, target_device_id={target_device_id}'
            print(f"❌ 参数验证失败: {error_msg}")
            return jsonify({'success': False, 'message': error_msg})

        # 检查房间是否已存在
        print("🔍 开始加载房间配置...")
        rooms = load_rooms_config()
        print(f"🔍 当前房间数量: {len(rooms)}")

        if live_id in rooms:
            print(f"❌ 房间ID已存在: {live_id}")
            return jsonify({'success': False, 'message': f'房间ID {live_id} 已存在'})

        # 保存到配置文件
        rooms[live_id] = {
            'live_id': live_id,
            'room_name': room_name,
            'target_device_id': target_device_id,
            'enabled': True,
            'created_at': datetime.now().isoformat(),
            'created_by': request.current_user['username']
        }

        print(f"🔍 准备保存房间配置，房间数量: {len(rooms)}")
        if save_rooms_config(rooms):
            print("✅ 房间配置保存成功")

            # 自动将房间分配给创建者（如果不是管理员）
            current_username = request.current_user['username']
            current_role = request.current_user['role']

            if current_role != 'admin':
                try:
                    # 将房间分配给创建者
                    user_manager.assign_room_to_user(current_username, live_id)
                    print(f"✅ 自动分配房间 {live_id} 给用户 {current_username}")
                except Exception as e:
                    print(f"⚠️ 自动分配房间失败: {e}")
                    # 不影响房间创建成功的返回

            return jsonify({'success': True, 'message': '房间创建成功'})
        else:
            print("❌ 房间配置保存失败")
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        print(f"❌ 创建房间异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/start', methods=['POST'])
@require_auth
@check_room_access
def start_room(room_id):
    """启动房间机器人"""
    try:
        rooms = load_rooms_config()
        print(f"🔍 调试信息 - 请求房间ID: {room_id}")
        print(f"🔍 调试信息 - 配置文件中的房间: {list(rooms.keys())}")
        room_config = rooms.get(room_id)
        print(f"🔍 调试信息 - 找到的配置: {room_config}")

        if not room_config:
            return jsonify({'success': False, 'message': f'房间配置不存在，请求ID: {room_id}，可用ID: {list(rooms.keys())}'})

        # 房间可以直接启动，无需激活码验证
        print(f"✅ 房间 {room_id} 准备启动")

        # 兼容不同的字段名格式
        target_device_id = (room_config.get('target_device_id') or
                           room_config.get('device_id') or
                           (room_config.get('esp32_settings', {}).get('device_id')))

        # 兼容不同的房间名称字段格式
        room_name = (room_config.get('room_name') or
                    (room_config.get('room_info', {}).get('room_name')) or
                    f'房间{room_id}')

        # 验证必要参数
        if not target_device_id:
            return jsonify({'success': False, 'message': f'房间 {room_id} 缺少设备ID配置'})

        if not room_name:
            room_name = f'房间{room_id}'

        print(f"🚀 准备启动房间: {room_id} ({room_name}) -> {target_device_id}")

        success = pm.start_live_process(
            room_id,
            target_device_id,
            room_name
        )

        if success:
            print(f"🎬 启动房间监控: {room_id}")
            return jsonify({'success': True, 'message': f'房间 {room_name} 启动成功'})
        else:
            return jsonify({'success': False, 'message': '启动失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/stop', methods=['POST'])
@require_auth
@check_room_access
def stop_room(room_id):
    """停止房间机器人"""
    try:
        success = pm.stop_live_process(room_id)

        if success:
            return jsonify({'success': True, 'message': '房间机器人停止成功'})
        else:
            return jsonify({'success': False, 'message': '停止失败或进程不存在'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/stop-all', methods=['POST'])
@require_auth
def stop_all_rooms():
    """停止所有房间"""
    try:
        rooms = load_rooms_config()
        stopped_count = 0
        errors = []
        
        for room_id in rooms.keys():
            try:
                success = pm.stop_live_process(room_id)
                if success:
                    stopped_count += 1
            except Exception as e:
                errors.append(f"房间 {room_id}: {str(e)}")
        
        if errors:
            return jsonify({
                'success': True, 
                'message': f'停止了 {stopped_count} 个房间，{len(errors)} 个失败',
                'details': errors
            })
        else:
            return jsonify({
                'success': True, 
                'message': f'成功停止所有房间 ({stopped_count} 个)'
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'批量停止失败: {str(e)}'})

@app.route('/api/rooms/<room_id>', methods=['PUT'])
@require_auth
@check_room_access
def update_room(room_id):
    """更新房间配置"""
    try:
        data = request.get_json()
        print(f"🔍 更新房间请求数据: room_id={room_id}, data={data}")

        # 加载现有配置
        rooms = load_rooms_config()

        if room_id not in rooms:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新房间配置
        room_config = rooms[room_id]

        # 更新基本信息
        if 'room_name' in data:
            room_config['room_name'] = data['room_name']
        if 'live_id' in data:
            room_config['live_id'] = data['live_id']
        if 'target_device_id' in data:
            room_config['target_device_id'] = data['target_device_id']

        # 保存配置
        if save_rooms_config(rooms):
            print(f"✅ 房间 {room_id} 配置更新成功")
            return jsonify({'success': True, 'message': '房间配置更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        print(f"❌ 更新房间配置异常: {e}")
        return jsonify({'success': False, 'message': str(e)})



@app.route('/api/rooms/<room_id>', methods=['DELETE'])
@require_auth
@require_admin
def delete_room(room_id):
    """删除房间"""
    try:
        # 先停止进程
        pm.stop_live_process(room_id)

        # 删除配置
        rooms = load_rooms_config()
        if room_id in rooms:
            del rooms[room_id]

            if save_rooms_config(rooms):
                return jsonify({'success': True, 'message': '房间删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '房间不存在'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 邀请码管理API ====================

@app.route('/api/invitation-codes/validate', methods=['POST'])
def validate_invitation_code():
    """验证邀请码（公开接口，用于注册时验证）"""
    try:
        data = request.get_json()
        code = data.get('code', '').strip()

        if not code:
            return jsonify({'success': False, 'message': '邀请码不能为空'})

        is_valid, message, code_info = membership_manager.validate_invitation_code(code)

        if is_valid:
            return jsonify({
                'success': True,
                'message': message,
                'code_info': {
                    'code': code_info['code'],
                    'note': code_info.get('note', ''),
                    'remaining_uses': code_info['max_uses'] - code_info['used_count']
                }
            })
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        print(f"❌ 验证邀请码异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/invitation-codes', methods=['POST'])
@require_auth
@require_admin
def create_invitation_code():
    """创建会员码/邀请码（仅管理员）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请提供有效的请求数据'})
        
        # 安全地获取和转换参数
        try:
            valid_days = data.get('valid_days', 7)
            if valid_days is None or valid_days == '':
                valid_days = 7
            valid_days = int(valid_days)
        except (ValueError, TypeError):
            valid_days = 7
            
        try:
            max_uses = data.get('max_uses', 1)
            if max_uses is None or max_uses == '':
                max_uses = 1
            max_uses = int(max_uses)
        except (ValueError, TypeError):
            max_uses = 1
            
        note = data.get('note', '') or ''

        # 验证参数范围
        if valid_days <= 0 or valid_days > 3650:  # 最多10年
            return jsonify({'success': False, 'message': '有效期必须在1-3650天之间'})

        if max_uses <= 0 or max_uses > 1000:
            return jsonify({'success': False, 'message': '使用次数必须在1-1000之间'})

        result = membership_manager.create_membership_code(
            created_by=request.current_user['username'],
            valid_days=valid_days,
            max_uses=max_uses,
            note=note
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': '会员码创建成功',
                'code': result['code'],
                'code_info': result['code_info']
            })
        else:
            return jsonify({'success': False, 'message': result['message']})

    except Exception as e:
        print(f"[ERROR] 创建邀请码异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': '服务器内部错误'})

@app.route('/api/invitation-codes', methods=['GET'])
@require_auth
@require_admin
def list_invitation_codes():
    """列出会员码/邀请码（仅管理员）"""
    try:
        print(f"[DEBUG] API调用: 获取邀请码列表")
        status = request.args.get('status')  # active, expired, exhausted
        print(f"[DEBUG] 筛选状态: {status}")

        codes = membership_manager.list_codes(status)
        print(f"[DEBUG] 获取到 {len(codes)} 个邀请码")

        return jsonify({
            'success': True,
            'codes': codes,
            'total': len(codes)
        })

    except Exception as e:
        print(f"❌ 获取邀请码列表异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/invitation-codes/batch-delete', methods=['POST'])
@require_auth
@require_admin
def batch_delete_invitation_codes():
    """批量删除邀请码（仅管理员）"""
    try:
        print(f"[DEBUG] API调用: 批量删除邀请码")
        data = request.get_json()
        print(f"[DEBUG] 请求数据: {data}")

        codes_to_delete = data.get('codes', [])
        print(f"[DEBUG] 要删除的邀请码: {codes_to_delete}")

        if not codes_to_delete:
            print(f"[DEBUG] 没有选择要删除的邀请码")
            return jsonify({'success': False, 'message': '请选择要删除的邀请码'})

        if len(codes_to_delete) > 100:
            print(f"[DEBUG] 删除数量超过限制: {len(codes_to_delete)}")
            return jsonify({'success': False, 'message': '单次删除数量不能超过100个'})

        print(f"[DEBUG] 开始删除邀请码...")
        deleted_count, failed_codes = membership_manager.delete_codes(codes_to_delete)
        print(f"[DEBUG] 删除结果: 成功 {deleted_count} 个，失败 {len(failed_codes)} 个")

        message = f'删除完成，成功删除 {deleted_count} 个邀请码'
        if failed_codes:
            message += f'，{len(failed_codes)} 个邀请码删除失败'

        return jsonify({
            'success': True,
            'message': message,
            'deleted_count': deleted_count,
            'failed_codes': failed_codes
        })

    except Exception as e:
        print(f"❌ 批量删除邀请码异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/invitation-codes/cleanup', methods=['POST'])
@require_auth
@require_admin
def cleanup_expired_invitation_codes():
    """清理过期的邀请码（仅管理员）"""
    try:
        cleaned_count = membership_manager.cleanup_expired_codes()
        return jsonify({
            'success': True,
            'message': f'清理完成，共清理了 {cleaned_count} 个过期邀请码',
            'cleaned_count': cleaned_count
        })

    except Exception as e:
        print(f"❌ 清理过期邀请码异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== 公告管理API ====================

@app.route('/api/announcements', methods=['GET'])
@require_auth
def get_announcements():
    """获取公告列表"""
    try:
        # 获取查询参数
        status = request.args.get('status')  # published, draft, archived
        include_drafts = request.args.get('include_drafts', 'false').lower() == 'true'

        # 管理员可以看到所有公告，普通用户只能看到已发布的公告
        current_user = request.current_user
        if current_user['role'] != 'admin':
            include_drafts = False
            if not status:
                status = 'published'

        announcements = announcement_manager.get_announcements(
            status=status,
            include_drafts=include_drafts
        )

        return jsonify({
            'success': True,
            'announcements': announcements,
            'total': len(announcements)
        })

    except Exception as e:
        print(f"❌ 获取公告列表异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/announcements', methods=['POST'])
@require_auth
@require_admin
def create_announcement():
    """创建公告（仅管理员）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请提供有效的请求数据'})

        title = data.get('title', '').strip()
        content = data.get('content', '').strip()
        status = data.get('status', 'draft')

        if not title:
            return jsonify({'success': False, 'message': '标题不能为空'})

        if not content:
            return jsonify({'success': False, 'message': '内容不能为空'})

        if status not in ['draft', 'published']:
            return jsonify({'success': False, 'message': '无效的状态'})

        result = announcement_manager.create_announcement(
            title=title,
            content=content,
            created_by=request.current_user['username'],
            status=status
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': '公告创建成功',
                'announcement': result['announcement']
            })
        else:
            return jsonify({'success': False, 'message': result['message']})

    except Exception as e:
        print(f"❌ 创建公告异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'})

@app.route('/api/announcements/<announcement_id>', methods=['PUT'])
@require_auth
@require_admin
def update_announcement(announcement_id):
    """更新公告（仅管理员）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请提供有效的请求数据'})

        title = data.get('title')
        content = data.get('content')
        status = data.get('status')

        if title is not None:
            title = title.strip()
            if not title:
                return jsonify({'success': False, 'message': '标题不能为空'})

        if content is not None:
            content = content.strip()
            if not content:
                return jsonify({'success': False, 'message': '内容不能为空'})

        if status is not None and status not in ['draft', 'published', 'archived']:
            return jsonify({'success': False, 'message': '无效的状态'})

        result = announcement_manager.update_announcement(
            announcement_id=announcement_id,
            title=title,
            content=content,
            status=status
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': '公告更新成功',
                'announcement': result['announcement']
            })
        else:
            return jsonify({'success': False, 'message': result['message']})

    except Exception as e:
        print(f"❌ 更新公告异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'})

@app.route('/api/announcements/<announcement_id>', methods=['DELETE'])
@require_auth
@require_admin
def delete_announcement(announcement_id):
    """删除公告（仅管理员）"""
    try:
        result = announcement_manager.delete_announcement(announcement_id)

        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message']
            })
        else:
            return jsonify({'success': False, 'message': result['message']})

    except Exception as e:
        print(f"❌ 删除公告异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'})

@app.route('/api/announcements/<announcement_id>/read', methods=['POST'])
@require_auth
def mark_announcement_read(announcement_id):
    """标记公告为已读"""
    try:
        username = request.current_user['username']
        success = announcement_manager.mark_as_read(username, announcement_id)

        if success:
            return jsonify({
                'success': True,
                'message': '标记已读成功'
            })
        else:
            return jsonify({'success': False, 'message': '标记已读失败'})

    except Exception as e:
        print(f"❌ 标记已读异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'})

@app.route('/api/announcements/unread-count', methods=['GET'])
@require_auth
def get_unread_announcements_count():
    """获取用户未读公告数量"""
    try:
        username = request.current_user['username']
        unread_count = announcement_manager.get_unread_count(username)

        return jsonify({
            'success': True,
            'unread_count': unread_count
        })

    except Exception as e:
        print(f"❌ 获取未读数量异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/announcements/unread', methods=['GET'])
@require_auth
def get_unread_announcements():
    """获取用户未读公告列表"""
    try:
        username = request.current_user['username']
        unread_announcements = announcement_manager.get_unread_announcements(username)

        return jsonify({
            'success': True,
            'announcements': unread_announcements,
            'total': len(unread_announcements)
        })

    except Exception as e:
        print(f"❌ 获取未读公告异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== 设备管理API ====================

def save_device_configs(device_configs):
    """保存设备配置"""
    try:
        # 确保配置目录存在
        os.makedirs('config/devices', exist_ok=True)

        # 保存到新的配置结构
        config_path = 'config/devices/devices.json'
        temp_file = f'{config_path}.tmp'

        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(device_configs, f, ensure_ascii=False, indent=2)

        # 原子性替换
        import shutil
        shutil.move(temp_file, config_path)

        return True
    except Exception as e:
        print(f"❌ 保存设备配置失败: {e}")
        return False

@app.route('/api/devices', methods=['GET', 'POST'])
@require_auth
def manage_devices():
    """设备管理：获取设备列表或注册新设备"""
    if request.method == 'GET':
        return get_devices_list()
    elif request.method == 'POST':
        return register_device()

@app.errorhandler(400)
def bad_request(error):
    """处理400错误"""
    return jsonify({'success': False, 'message': '请求格式错误'}), 400

@app.errorhandler(404)
def not_found(error):
    """处理404错误"""
    return jsonify({'success': False, 'message': '资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@app.before_request
def validate_json():
    """验证JSON格式"""
    if request.method in ['POST', 'PUT', 'PATCH'] and request.content_type == 'application/json':
        # Skip JSON validation for endpoints that don't require JSON data
        if request.endpoint in ['cleanup_expired_codes']:
            return None
        try:
            # Check if there's actually data to parse
            data = request.get_data()
            if not data or data.strip() == b'':
                return None
            # Try to parse JSON
            json_data = request.get_json(force=True)
            if json_data is None and data.strip() != b'':
                # If we have data but can't parse it as JSON, that's an error
                return jsonify({'success': False, 'message': '无效的JSON格式'}), 400
        except Exception as e:
            print(f"JSON validation error: {e}")
            return jsonify({'success': False, 'message': '无效的JSON格式'}), 400

def validate_device_id(device_id):
    """验证设备ID的安全性"""
    import re

    # 检查长度
    if len(device_id) > 50:
        return False, "设备ID长度不能超过50个字符"

    # 检查是否包含危险字符
    dangerous_chars = ["'", '"', ';', '<', '>', '&', '|', '`', '$', '(', ')', '{', '}', '[', ']']
    for char in dangerous_chars:
        if char in device_id:
            return False, f"设备ID不能包含特殊字符: {char}"

    # 检查是否包含SQL关键词
    sql_keywords = ['DROP', 'DELETE', 'INSERT', 'UPDATE', 'SELECT', 'UNION', 'SCRIPT', 'EXEC']
    device_id_upper = device_id.upper()
    for keyword in sql_keywords:
        if keyword in device_id_upper:
            return False, f"设备ID不能包含SQL关键词: {keyword}"

    # 检查是否为有效的MAC地址格式或设备ID格式
    mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    device_pattern = r'^[A-Za-z0-9_-]+$'

    if not (re.match(mac_pattern, device_id) or re.match(device_pattern, device_id)):
        return False, "设备ID格式无效，请使用MAC地址格式或字母数字组合"

    return True, ""

def register_device():
    """注册新设备"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        device_id = data.get('device_id', '').strip()
        device_name = data.get('device_name', '')
        device_type = data.get('device_type', 'esp32')
        capabilities = data.get('capabilities', 'voice,display')
        description = data.get('description', '')
        enabled = data.get('enabled', True)
        auto_registered = data.get('auto_registered', False)

        if not device_id:
            return jsonify({'success': False, 'message': '设备ID不能为空'}), 400

        if not device_name:
            return jsonify({'success': False, 'message': '设备名称不能为空'}), 400

        # 验证设备ID安全性
        is_valid, error_msg = validate_device_id(device_id)
        if not is_valid:
            return jsonify({'success': False, 'message': error_msg}), 400

        # 检查设备是否已存在
        device_configs = load_device_configs()
        if device_id in device_configs:
            return jsonify({'success': False, 'message': '设备ID已存在'}), 409

        # 添加新设备配置
        from datetime import datetime
        device_configs[device_id] = {
            'device_id': device_id,
            'device_name': device_name,
            'device_type': device_type,
            'capabilities': capabilities,
            'description': description,
            'created_at': datetime.now().isoformat(),
            'last_seen': datetime.now().isoformat(),
            'enabled': enabled,
            'auto_registered': auto_registered
        }

        # 保存配置
        if save_device_configs(device_configs):
            return jsonify({
                'success': True,
                'message': '设备注册成功',
                'device': device_configs[device_id]
            })
        else:
            return jsonify({'success': False, 'message': '保存设备配置失败'})

    except Exception as e:
        print(f"❌ 设备注册异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

def get_devices_list():
    """获取设备列表（根据用户权限过滤）"""
    try:
        # 优先从配置文件加载设备列表
        device_configs = load_device_configs()
        all_devices = []

        # 将配置文件中的设备转换为设备列表格式
        for device_id, device_config in device_configs.items():
            device_info = {
                'device_id': device_id,
                'device_name': device_config.get('device_name', '未知设备'),
                'device_type': device_config.get('device_type', 'esp32'),
                'capabilities': device_config.get('capabilities', 'voice'),
                'enabled': device_config.get('enabled', True),
                'last_seen': device_config.get('last_seen', ''),
                'status': 'offline',  # 默认离线，实际状态需要WebSocket更新
                'auto_registered': device_config.get('auto_registered', False)
            }
            all_devices.append(device_info)

        print(f"[DEBUG] 从配置文件加载到 {len(all_devices)} 个设备")

        # 尝试从WebSocket数据获取在线状态
        try:
            ws_data = get_websocket_data()
            online_devices = ws_data.get('devices_list', [])

            # 更新设备在线状态
            for device in all_devices:
                for online_device in online_devices:
                    if device['device_id'] == online_device.get('device_id'):
                        device['status'] = online_device.get('status', 'online')
                        device['online'] = online_device.get('status', 'online') == 'online'  # 添加online字段供前端使用
                        device['last_seen'] = online_device.get('last_seen', device['last_seen'])
                        break

                # 如果没有找到在线设备，确保offline状态
                if 'online' not in device:
                    device['online'] = device['status'] == 'online'
        except Exception as e:
            print("[WARNING] 获取设备在线状态失败: {}".format(e))
            # 继续使用配置文件中的设备，只是状态为离线

        username = request.current_user['username']
        user_role = request.current_user['role']

        print(f"[DEBUG] 用户 {username} ({user_role}) 请求设备列表，共 {len(all_devices)} 个设备")

        # 权限检查：管理员可以看到所有设备，普通用户只能看到分配的设备
        if user_role == 'admin':
            filtered_devices = all_devices
            print(f"[DEBUG] 管理员用户，返回所有 {len(filtered_devices)} 个设备")
        else:
            user_devices = user_manager.get_user_devices(username)
            filtered_devices = [device for device in all_devices if device.get('device_id') in user_devices]
            print(f"[DEBUG] 普通用户，分配的设备: {user_devices}，过滤后: {len(filtered_devices)} 个设备")

        return jsonify({
            'success': True,
            'devices': filtered_devices
        })
    except Exception as e:
        print(f"[ERROR] 获取设备列表异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)})

# 删除重复的路由定义，已在上面的manage_devices中处理

@app.route('/api/internal/devices')
def get_devices_internal():
    """内部API：获取设备列表（无需认证，供直播机器人使用）"""
    try:
        # 尝试从WebSocket数据获取设备列表
        ws_data = get_websocket_data()
        all_devices = ws_data.get('devices_list', [])

        # 如果WebSocket数据为空，尝试从shared_data获取
        if not all_devices:
            try:
                from shared_data import get_devices_list
                all_devices = get_devices_list()
                print(f"[DEBUG] 内部API从shared_data获取到 {len(all_devices)} 个设备")
            except Exception as e:
                print(f"[ERROR] 内部API从shared_data获取设备失败: {e}")
                all_devices = []

        # print(f"🔍 内部API返回 {len(all_devices)} 个设备")

        return jsonify({
            'success': True,
            'devices': all_devices
        })
    except Exception as e:
        print(f"❌ 内部API获取设备列表异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/device-status')
def get_device_status():
    """获取设备状态（兼容前端调用）"""
    return get_devices_list()

@app.route('/api/devices/<device_id>/test', methods=['POST'])
@require_auth
def test_device(device_id):
    """测试设备"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        message = data.get('message', '这是一条测试消息')
        force = data.get('force', False)

        # 通过WebSocket发送测试消息
        import asyncio
        import websockets
        import json

        async def send_test_message():
            try:
                websocket_url = "ws://localhost:15000"
                async with websockets.connect(websocket_url) as websocket:
                    test_message = {
                        "type": "speak_message",
                        "data": {
                            "device_id": device_id,
                            "message": message,
                            "force": force
                        }
                    }

                    await websocket.send(json.dumps(test_message, ensure_ascii=False))
                    response = await websocket.recv()
                    response_data = json.loads(response)

                    # 标准化响应格式
                    if response_data.get("type") == "speak_response":
                        speak_data = response_data.get("data", {})
                        return {
                            "success": speak_data.get("success", True),
                            "message": speak_data.get("message", "测试消息已发送")
                        }
                    else:
                        return response_data

            except Exception as e:
                return {"success": False, "message": str(e)}

        # 运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(send_test_message())
            return jsonify(result)
        finally:
            loop.close()

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/devices/<device_id>/wake', methods=['POST'])
@require_auth
def wake_device(device_id):
    """唤醒设备"""
    try:
        # 权限检查：只有管理员或设备所有者可以唤醒
        username = request.current_user['username']
        user_role = request.current_user['role']

        if user_role != 'admin':
            user_devices = user_manager.get_user_devices(username)
            if device_id not in user_devices:
                return jsonify({'success': False, 'message': '无权操作该设备'})

        # 获取请求参数
        data = request.get_json() or {}
        message = data.get('message', '')
        force = data.get('force', False)

        print(f"🔍 用户 {username} 唤醒设备 {device_id}，消息: {message}")

        # 获取设备专用锁
        device_lock = get_device_lock(device_id)

        # 尝试获取锁，如果不强制则检查是否已被占用
        if not force:
            # 非阻塞方式尝试获取锁
            lock_acquired = device_lock.acquire(blocking=False)
            if not lock_acquired:
                print(f"⚠️ 设备 {device_id} 正在处理其他消息，拒绝新请求")
                return jsonify({
                    'success': False,
                    'message': '设备正在处理其他消息，请稍后再试',
                    'code': 'DEVICE_BUSY'
                })
        else:
            # 强制模式：阻塞等待锁，最多等待10秒
            print(f"🔧 强制模式：等待设备 {device_id} 锁...")
            lock_acquired = device_lock.acquire(timeout=10)
            if not lock_acquired:
                print(f"❌ 强制模式超时：设备 {device_id} 锁获取失败")
                return jsonify({
                    'success': False,
                    'message': '设备忙碌超时，请检查设备状态',
                    'code': 'DEVICE_TIMEOUT'
                })

        try:
            print(f"🔒 已获取设备 {device_id} 发送锁")

            # 尝试连接WebSocket服务器发送唤醒消息
            import asyncio
            import websockets
            import json

            async def send_wake_message():
                try:
                    # 设置较短的连接超时，避免长时间等待
                    websocket_url = "ws://localhost:15000"
                    async with websockets.connect(
                        websocket_url,
                        timeout=3,  # 3秒超时
                        ping_timeout=2,
                        close_timeout=2
                    ) as websocket:
                        wake_message = {
                            "type": "wake_device",
                            "data": {
                                "device_id": device_id,
                                "message": message,
                                "force": force,
                                "user": username
                            }
                        }

                        print(f"🔍 发送唤醒消息到WebSocket: {wake_message}")
                        await websocket.send(json.dumps(wake_message, ensure_ascii=False))

                        # 等待响应，但设置超时
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=2)
                            response_data = json.loads(response)
                            print(f"🔍 收到WebSocket响应: {response_data}")
                            return response_data
                        except asyncio.TimeoutError:
                            print("⚠️ WebSocket响应超时")
                            return {"success": True, "message": "唤醒指令已发送（响应超时）"}

                except Exception as e:
                    print(f"❌ WebSocket连接失败: {e}")
                    return {"success": False, "message": f"WebSocket连接失败: {str(e)}"}

            # 运行异步函数，设置总超时
            try:
                result = asyncio.wait_for(send_wake_message(), timeout=5)
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    final_result = loop.run_until_complete(result)
                    return jsonify(final_result)
                finally:
                    loop.close()
            except asyncio.TimeoutError:
                print("❌ 唤醒操作总超时")
                return jsonify({"success": False, "message": "操作超时"})

        except Exception as e:
            print(f"❌ 唤醒操作异常: {e}")
            # 如果WebSocket不可用，返回模拟成功响应
            return jsonify({
                'success': True,
                'message': f'唤醒指令已记录（WebSocket服务器不可用）',
                'device_id': device_id,
                'wake_message': message,
                'note': 'WebSocket服务器未启动，消息已记录但未实际发送'
            })
        finally:
            # 确保锁被释放
            if lock_acquired:
                device_lock.release()
                print(f"🔓 已释放设备 {device_id} 发送锁")

    except Exception as e:
        print(f"❌ 设备唤醒函数异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/processes')
def get_processes():
    """获取进程列表"""
    try:
        pm.update_process_status()
        processes = pm.get_all_processes_status()
        return jsonify({
            'success': True,
            'processes': processes
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 兼容旧的直播间API
@app.route('/api/live/rooms/<live_id>/start', methods=['POST'])
@require_auth
@check_room_access
def start_live_room_old(live_id):
    """启动直播间（兼容旧API）"""
    return start_room(live_id)

@app.route('/api/live/rooms/<live_id>/stop', methods=['POST'])
@require_auth
@check_room_access
def stop_live_room_old(live_id):
    """停止直播间（兼容旧API）"""
    return stop_room(live_id)

# ==================== 自定义回复管理API ====================

@app.route('/api/rooms/<room_id>/custom-reply', methods=['GET'])
@require_auth
@check_room_access
def get_custom_reply_config(room_id):
    """获取房间自定义回复配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有自定义回复配置，创建默认配置
        if 'custom_reply' not in config[room_id]:
            default_custom_reply = {
                'enabled': True,
                'priority_weight': 8,
                'match_mode': 'contains',
                'case_sensitive': False,
                'cooldown_seconds': 5,
                'rules': []
            }
            config[room_id]['custom_reply'] = default_custom_reply
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认自定义回复配置")

        custom_reply = config[room_id]['custom_reply']

        return jsonify({
            'success': True,
            'config': custom_reply
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/custom-reply', methods=['POST'])
@require_auth
@check_room_access
def update_custom_reply_config(room_id):
    """更新房间自定义回复基础配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            config[room_id] = {}

        # 更新房间自定义回复配置，保留现有的rules
        if 'custom_reply' not in config[room_id]:
            config[room_id]['custom_reply'] = {}

        # 保留现有的rules，只更新基本配置
        existing_rules = config[room_id]['custom_reply'].get('rules', [])

        config[room_id]['custom_reply'].update({
            'enabled': data.get('enabled', True),
            'priority_weight': data.get('priority_weight', 8),
            'match_mode': data.get('match_mode', 'contains'),
            'case_sensitive': data.get('case_sensitive', False),
            'cooldown_seconds': data.get('cooldown_seconds', 5),
            'rules': data.get('rules', existing_rules)  # 如果没有传入rules，保留现有的
        })

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '自定义回复配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/custom-reply/rules', methods=['POST'])
@require_auth
@check_room_access
def add_custom_reply_rule(room_id):
    """添加房间自定义回复规则"""
    try:
        data = request.get_json()
        rule_id = data.get('id', '').strip()
        keywords = data.get('keywords', [])
        responses = data.get('responses', [])
        enabled = data.get('enabled', True)

        if not rule_id:
            return jsonify({'success': False, 'message': '规则ID不能为空'})

        if not keywords:
            return jsonify({'success': False, 'message': '关键词列表不能为空'})

        if not responses:
            return jsonify({'success': False, 'message': '回复列表不能为空'})

        config = load_rooms_config()

        if room_id not in config:
            config[room_id] = {}

        custom_reply = config[room_id].get('custom_reply', {})
        rules = custom_reply.get('rules', [])

        # 检查规则ID是否已存在
        if any(rule['id'] == rule_id for rule in rules):
            return jsonify({'success': False, 'message': '规则ID已存在'})

        # 添加新规则
        new_rule = {
            'id': rule_id,
            'keywords': keywords,
            'responses': responses,
            'enabled': enabled
        }
        rules.append(new_rule)
        custom_reply['rules'] = rules
        config[room_id]['custom_reply'] = custom_reply

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '自定义回复规则添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/custom-reply/rules/<rule_id>', methods=['PUT'])
@require_auth
@check_room_access
def update_custom_reply_rule(room_id, rule_id):
    """更新房间自定义回复规则"""
    try:
        data = request.get_json()
        keywords = data.get('keywords', [])
        responses = data.get('responses', [])
        enabled = data.get('enabled', True)

        if not keywords:
            return jsonify({'success': False, 'message': '关键词列表不能为空'})

        if not responses:
            return jsonify({'success': False, 'message': '回复列表不能为空'})

        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        custom_reply = config[room_id].get('custom_reply', {})
        rules = custom_reply.get('rules', [])

        # 查找并更新规则
        rule_found = False
        for rule in rules:
            if rule['id'] == rule_id:
                rule['keywords'] = keywords
                rule['responses'] = responses
                rule['enabled'] = enabled
                rule_found = True
                break

        if not rule_found:
            return jsonify({'success': False, 'message': '规则不存在'})

        custom_reply['rules'] = rules
        config[room_id]['custom_reply'] = custom_reply

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '自定义回复规则更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/custom-reply/rules/<rule_id>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_custom_reply_rule(room_id, rule_id):
    """删除房间自定义回复规则"""
    try:
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        custom_reply = config[room_id].get('custom_reply', {})
        rules = custom_reply.get('rules', [])

        # 查找并删除规则
        original_count = len(rules)
        rules = [rule for rule in rules if rule['id'] != rule_id]

        if len(rules) == original_count:
            return jsonify({'success': False, 'message': '规则不存在'})

        custom_reply['rules'] = rules
        config[room_id]['custom_reply'] = custom_reply

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '自定义回复规则删除成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 冷场回复管理API ====================

@app.route('/api/rooms/<room_id>/anti-silence', methods=['GET'])
@require_auth
@check_room_access
def get_anti_silence_config(room_id):
    """获取房间冷场回复配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有冷场回复配置，创建默认配置
        if 'anti_silence' not in config[room_id]:
            default_anti_silence = {
                'enabled': True,
                'delay_seconds': 1,
                'cooldown_seconds': 20,
                'max_proactive_attempts': 2,
                'templates': [
                    "自我介绍一下",
                    "你觉得你最厉害的能力是什么",
                    "如果有一天你变成人类你最想做什么事",
                    "你会背叛人类吗",
                    "今天有什么新闻",
                    "今天天气怎么样",
                    "说几个我们人类都不知道的冷知识"
                ],
                'personalized_templates': [
                    "{user_name}，刚才聊得挺开心的，还有什么想聊的吗？",
                    "嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！",
                    "{user_name}，直播间有点安静了，来活跃一下气氛吧！",
                    "{user_name}，有什么问题或想法都可以说出来哦！"
                ]
            }
            config[room_id]['anti_silence'] = default_anti_silence
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认冷场回复配置")
        else:
            # 确保必要的字段存在
            anti_silence_config = config[room_id]['anti_silence']
            updated = False

            if 'templates' not in anti_silence_config:
                anti_silence_config['templates'] = [
                    "自我介绍一下",
                    "你觉得你最厉害的能力是什么",
                    "如果有一天你变成人类你最想做什么事",
                    "你会背叛人类吗",
                    "今天有什么新闻",
                    "今天天气怎么样",
                    "说几个我们人类都不知道的冷知识"
                ]
                updated = True
                print(f"✅ 为房间 {room_id} 添加了默认冷场回复模板")

            if 'max_proactive_attempts' not in anti_silence_config:
                anti_silence_config['max_proactive_attempts'] = 2
                updated = True
                print(f"✅ 为房间 {room_id} 添加了最大主动沟通次数配置")

            if 'personalized_templates' not in anti_silence_config:
                anti_silence_config['personalized_templates'] = [
                    "{user_name}，刚才聊得挺开心的，还有什么想聊的吗？",
                    "嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！",
                    "{user_name}，直播间有点安静了，来活跃一下气氛吧！",
                    "{user_name}，有什么问题或想法都可以说出来哦！"
                ]
                updated = True
                print(f"✅ 为房间 {room_id} 添加了个性化防冷场模板")

            if updated:
                save_rooms_config(config)

        anti_silence = config[room_id]['anti_silence']

        return jsonify({
            'success': True,
            'config': anti_silence
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence', methods=['POST'])
@require_auth
@check_room_access
def update_anti_silence_config(room_id):
    """更新房间冷场回复配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            config[room_id] = {}

        # 更新房间冷场回复配置，保留现有的templates
        if 'anti_silence' not in config[room_id]:
            config[room_id]['anti_silence'] = {}

        # 保留现有的templates和personalized_templates，只更新基本配置
        existing_templates = config[room_id]['anti_silence'].get('templates', [])
        existing_personalized_templates = config[room_id]['anti_silence'].get('personalized_templates', [])

        config[room_id]['anti_silence'].update({
            'enabled': data.get('enabled', True),
            'delay_seconds': data.get('delay_seconds', 30),
            'cooldown_seconds': data.get('cooldown_seconds', 60),
            'max_proactive_attempts': data.get('max_proactive_attempts', 2),
            'templates': data.get('templates', existing_templates),  # 如果没有传入templates，保留现有的
            'personalized_templates': data.get('personalized_templates', existing_personalized_templates)  # 如果没有传入personalized_templates，保留现有的
        })

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '冷场回复配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/templates', methods=['POST'])
@require_auth
@check_room_access
def add_anti_silence_template(room_id):
    """添加房间冷场回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()

        if room_id not in config:
            config[room_id] = {}

        anti_silence = config[room_id].get('anti_silence', {})
        templates = anti_silence.get('templates', [])

        if template in templates:
            return jsonify({'success': False, 'message': '模板已存在'})

        templates.append(template)
        anti_silence['templates'] = templates
        config[room_id]['anti_silence'] = anti_silence

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_anti_silence_template(room_id, index):
    """删除房间冷场回复模板"""
    try:
        config = load_rooms_config()
        
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})
            
        anti_silence = config[room_id].get('anti_silence', {})
        templates = anti_silence.get('templates', [])

        if 0 <= index < len(templates):
            deleted_template = templates.pop(index)
            anti_silence['templates'] = templates
            config[room_id]['anti_silence'] = anti_silence

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'模板"{deleted_template}"删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_anti_silence_template(room_id, index):
    """更新房间冷场回复模板"""
    try:
        data = request.get_json()
        new_template = data.get('template', '').strip()

        if not new_template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})
            
        anti_silence = config[room_id].get('anti_silence', {})
        templates = anti_silence.get('templates', [])

        if 0 <= index < len(templates):
            templates[index] = new_template
            anti_silence['templates'] = templates
            config[room_id]['anti_silence'] = anti_silence

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'模板更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/personalized-templates', methods=['POST'])
@require_auth
@check_room_access
def add_personalized_anti_silence_template(room_id):
    """添加房间个性化防冷场模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        # 检查模板是否包含{user_name}占位符
        if '{user_name}' not in template:
            return jsonify({'success': False, 'message': '个性化模板必须包含{user_name}占位符'})

        config = load_rooms_config()

        if room_id not in config:
            config[room_id] = {}

        anti_silence = config[room_id].get('anti_silence', {})
        personalized_templates = anti_silence.get('personalized_templates', [])

        if template in personalized_templates:
            return jsonify({'success': False, 'message': '模板已存在'})

        personalized_templates.append(template)
        anti_silence['personalized_templates'] = personalized_templates
        config[room_id]['anti_silence'] = anti_silence

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '个性化模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/personalized-templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_personalized_anti_silence_template(room_id, index):
    """删除房间个性化防冷场模板"""
    try:
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        anti_silence = config[room_id].get('anti_silence', {})
        personalized_templates = anti_silence.get('personalized_templates', [])

        if index < 0 or index >= len(personalized_templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        personalized_templates.pop(index)
        anti_silence['personalized_templates'] = personalized_templates
        config[room_id]['anti_silence'] = anti_silence

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '个性化模板删除成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-silence/personalized-templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_personalized_anti_silence_template(room_id, index):
    """更新房间个性化防冷场模板"""
    try:
        data = request.get_json()
        new_template = data.get('template', '').strip()

        if not new_template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        # 检查模板是否包含{user_name}占位符
        if '{user_name}' not in new_template:
            return jsonify({'success': False, 'message': '个性化模板必须包含{user_name}占位符'})

        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        anti_silence = config[room_id].get('anti_silence', {})
        personalized_templates = anti_silence.get('personalized_templates', [])

        if index < 0 or index >= len(personalized_templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        personalized_templates[index] = new_template
        anti_silence['personalized_templates'] = personalized_templates
        config[room_id]['anti_silence'] = anti_silence

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '个性化模板更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 进入欢迎管理API ====================

@app.route('/api/rooms/<room_id>/welcome-config', methods=['GET'])
@require_auth
@check_room_access
def get_room_welcome_config(room_id):
    """获取房间进入欢迎配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有欢迎配置，创建默认配置
        if 'welcome_message' not in config[room_id]:
            default_welcome_config = {
                'enabled': True,
                'cooldown_seconds': 5,
                'templates': [
                    "{user_name} 来了，猜猜他是哪个年代的人",
                    "{user_name} 来了，给他写个情书",
                    "{user_name} 来了，点评下他的网名"
                ]
            }
            config[room_id]['welcome_message'] = default_welcome_config
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认欢迎配置")
        else:
            # 确保必要的字段存在
            if 'templates' not in config[room_id]['welcome_message']:
                config[room_id]['welcome_message']['templates'] = [
                    "{user_name} 来了，猜猜他是哪个年代的人",
                    "{user_name} 来了，给他写个情书",
                    "{user_name} 来了，点评下他的网名"
                ]
                save_rooms_config(config)
                print(f"✅ 为房间 {room_id} 添加了默认欢迎模板")

        welcome = config[room_id]['welcome_message']

        return jsonify({
            'success': True,
            'config': welcome
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/welcome-config', methods=['POST'])
@require_auth
@check_room_access
def update_room_welcome_config(room_id):
    """更新房间进入欢迎配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新房间的欢迎配置
        if 'welcome_message' not in config[room_id]:
            config[room_id]['welcome_message'] = {}

        config[room_id]['welcome_message'] = {
            'enabled': data.get('enabled', True),
            'cooldown_seconds': data.get('cooldown_seconds', 5),
            'templates': data.get('templates', [])
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '欢迎配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 消息预处理配置API ====================

@app.route('/api/rooms/<room_id>/message-preprocess-config', methods=['GET'])
@require_auth
@check_room_access
def get_room_message_preprocess_config(room_id):
    """获取房间消息预处理配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有消息预处理配置，创建默认配置
        if 'message_preprocess' not in config[room_id]:
            default_preprocess_config = {
                'enabled': True,
                'filter_emoji': True,
                'filter_special_chars': True,
                'max_length': 200,
                'min_length': 1,
                'blocked_words': [],
                'replacement_rules': {}
            }
            config[room_id]['message_preprocess'] = default_preprocess_config
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认消息预处理配置")

        preprocess_config = config[room_id]['message_preprocess']

        return jsonify({
            'success': True,
            'config': preprocess_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/message-preprocess-config', methods=['POST'])
@require_auth
@check_room_access
def update_room_message_preprocess_config(room_id):
    """更新房间消息预处理配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新房间的消息预处理配置
        config[room_id]['message_preprocess'] = {
            'enabled': data.get('enabled', True),
            'filter_emoji': data.get('filter_emoji', True),
            'filter_special_chars': data.get('filter_special_chars', True),
            'max_length': data.get('max_length', 200),
            'min_length': data.get('min_length', 1),
            'blocked_words': data.get('blocked_words', []),
            'replacement_rules': data.get('replacement_rules', {})
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '消息预处理配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 添加兼容性路由 - 支持前端使用的 /api/welcome-config
@app.route('/api/welcome-config')
@app.route('/api/welcome-message')
def get_welcome_config():
    """获取进入欢迎配置"""
    try:
        config = load_rooms_config()
        welcome = config.get('welcome_message', {
            'enabled': True,
            'cooldown_seconds': 5,
            'templates': [
                "欢迎{user_name}进入直播间！",
                "欢迎{user_name}来到直播间，很高兴见到你！",
                "{user_name}欢迎你！有什么想聊的吗？"
            ]
        })

        return jsonify({
            'success': True,
            'config': welcome
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/welcome-config', methods=['POST'])
@app.route('/api/welcome-message', methods=['POST'])
@require_auth
@require_admin
def update_welcome_config():
    """更新进入欢迎配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        # 更新进入欢迎配置
        config['welcome_message'] = {
            'enabled': data.get('enabled', True),
            'cooldown_seconds': data.get('cooldown_seconds', 5),
            'templates': data.get('templates', [])
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '进入欢迎配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/welcome-config/templates', methods=['POST'])
@require_auth
@check_room_access
def add_room_welcome_template(room_id):
    """添加房间欢迎模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()

        # 确保房间存在
        if room_id not in config:
            config[room_id] = {}

        # 确保欢迎配置存在
        if 'welcome_message' not in config[room_id]:
            config[room_id]['welcome_message'] = {
                'enabled': True,
                'cooldown_seconds': 5,
                'templates': []
            }

        welcome = config[room_id]['welcome_message']
        templates = welcome.get('templates', [])

        if template in templates:
            return jsonify({'success': False, 'message': '模板已存在'})

        templates.append(template)
        welcome['templates'] = templates
        config[room_id]['welcome_message'] = welcome

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': f'房间 {room_id} 模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/welcome-config/templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_room_welcome_template(room_id, index):
    """更新房间欢迎模板"""
    try:
        data = request.get_json()
        new_template = data.get('template', '').strip()

        if not new_template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()

        # 确保房间存在
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        welcome = config[room_id].get('welcome_message', {})
        templates = welcome.get('templates', [])

        if 0 <= index < len(templates):
            templates[index] = new_template
            welcome['templates'] = templates
            config[room_id]['welcome_message'] = welcome

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'房间 {room_id} 模板更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/welcome-config/templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_room_welcome_template(room_id, index):
    """删除房间欢迎模板"""
    try:
        config = load_rooms_config()

        # 确保房间存在
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        welcome = config[room_id].get('welcome_message', {})
        templates = welcome.get('templates', [])

        if 0 <= index < len(templates):
            templates.pop(index)
            welcome['templates'] = templates
            config[room_id]['welcome_message'] = welcome

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'房间 {room_id} 模板删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/welcome-config/templates', methods=['POST'])
@app.route('/api/welcome-message/templates', methods=['POST'])
@require_auth
@require_admin
def add_welcome_template():
    """添加进入欢迎模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        room_id = data.get('room_id')

        # 如果指定了房间ID，使用房间级别配置
        if room_id and room_id in config:
            welcome = config[room_id].get('welcome_message', {})
            templates = welcome.get('templates', [])

            if template in templates:
                return jsonify({'success': False, 'message': '模板已存在'})

            templates.append(template)
            welcome['templates'] = templates
            config[room_id]['welcome_message'] = welcome

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'房间 {room_id} 模板添加成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            # 全局配置（向后兼容）
            welcome = config.get('welcome_message', {})
            templates = welcome.get('templates', [])

            if template in templates:
                return jsonify({'success': False, 'message': '模板已存在'})

            templates.append(template)
            welcome['templates'] = templates
            config['welcome_message'] = welcome

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '模板添加成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/welcome-config/templates/<int:index>', methods=['DELETE'])
@app.route('/api/welcome-message/templates/<int:index>', methods=['DELETE'])
@require_auth
@require_admin
def delete_welcome_template(index):
    """删除进入欢迎模板"""
    try:
        data = request.get_json() or {}
        room_id = data.get('room_id')

        config = load_rooms_config()

        # 如果指定了房间ID，使用房间级别配置
        if room_id and room_id in config:
            welcome = config[room_id].get('welcome_message', {})
            templates = welcome.get('templates', [])

            if 0 <= index < len(templates):
                deleted_template = templates.pop(index)
                welcome['templates'] = templates
                config[room_id]['welcome_message'] = welcome

                if save_rooms_config(config):
                    return jsonify({'success': True, 'message': f'房间 {room_id} 模板删除成功'})
                else:
                    return jsonify({'success': False, 'message': '保存配置失败'})
            else:
                return jsonify({'success': False, 'message': '模板索引无效'})
        else:
            # 全局配置（向后兼容）
            welcome = config.get('welcome_message', {})
            templates = welcome.get('templates', [])

            if 0 <= index < len(templates):
                deleted_template = templates.pop(index)
                welcome['templates'] = templates
                config['welcome_message'] = welcome

                if save_rooms_config(config):
                    return jsonify({'success': True, 'message': f'模板"{deleted_template}"删除成功'})
                else:
                    return jsonify({'success': False, 'message': '保存配置失败'})
            else:
                return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/welcome-config/templates/<int:index>', methods=['PUT'])
@app.route('/api/welcome-message/templates/<int:index>', methods=['PUT'])
@require_auth
@require_admin
def update_welcome_template(index):
    """更新进入欢迎模板"""
    try:
        data = request.get_json()
        new_template = data.get('template', '').strip()
        room_id = data.get('room_id')

        if not new_template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()

        # 如果指定了房间ID，使用房间级别配置
        if room_id and room_id in config:
            welcome = config[room_id].get('welcome_message', {})
            templates = welcome.get('templates', [])

            if 0 <= index < len(templates):
                templates[index] = new_template
                welcome['templates'] = templates
                config[room_id]['welcome_message'] = welcome

                if save_rooms_config(config):
                    return jsonify({'success': True, 'message': f'房间 {room_id} 模板更新成功'})
                else:
                    return jsonify({'success': False, 'message': '保存配置失败'})
            else:
                return jsonify({'success': False, 'message': '模板索引无效'})
        else:
            # 全局配置（向后兼容）
            welcome = config.get('welcome_message', {})
            templates = welcome.get('templates', [])

            if 0 <= index < len(templates):
                templates[index] = new_template
                welcome['templates'] = templates
                config['welcome_message'] = welcome

                if save_rooms_config(config):
                    return jsonify({'success': True, 'message': f'模板更新成功'})
                else:
                    return jsonify({'success': False, 'message': '保存配置失败'})
            else:
                return jsonify({'success': False, 'message': '模板索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 防刷屏配置API ====================

@app.route('/api/rooms/<room_id>/anti-spam-config', methods=['GET'])
@require_auth
@check_room_access
def get_room_anti_spam_config(room_id):
    """获取房间防刷屏配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有防刷屏配置，创建默认配置
        if 'anti_spam' not in config[room_id]:
            default_anti_spam = {
                'enabled': True,
                'duplicate_detection': {
                    'enabled': True,
                    'time_window_seconds': 60,
                    'max_duplicates': 3,
                    'similarity_threshold': 0.8
                },
                'frequency_limit': {
                    'enabled': True,
                    'time_window_seconds': 30,
                    'max_messages': 5
                },
                'warning_messages': [
                    "{user_name}你个刷屏怪，再刷屏把你关小黑屋了！",
                    "{user_name}别刷屏了，给其他人一点发言机会！",
                    "{user_name}刷屏可不是好习惯，消停点！",
                    "{user_name}你这样刷屏很影响直播间秩序哦！"
                ],
                'block_duration_seconds': 300,
                'warning_cooldown_seconds': 60
            }
            config[room_id]['anti_spam'] = default_anti_spam
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认防刷屏配置")

        anti_spam = config[room_id]['anti_spam']

        return jsonify({
            'success': True,
            'config': anti_spam
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-spam-config', methods=['POST'])
@require_auth
@check_room_access
def update_room_anti_spam_config(room_id):
    """更新房间防刷屏配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新房间的防刷屏配置，保留现有配置
        if 'anti_spam' not in config[room_id]:
            config[room_id]['anti_spam'] = {}

        anti_spam = config[room_id]['anti_spam']

        # 更新基本配置
        anti_spam['enabled'] = data.get('enabled', anti_spam.get('enabled', True))
        anti_spam['block_duration_seconds'] = data.get('block_duration_seconds', anti_spam.get('block_duration_seconds', 300))
        anti_spam['warning_cooldown_seconds'] = data.get('warning_cooldown_seconds', anti_spam.get('warning_cooldown_seconds', 60))

        # 保留现有的详细配置，如果没有则使用默认值
        if 'duplicate_detection' not in anti_spam:
            anti_spam['duplicate_detection'] = {
                'enabled': True,
                'time_window_seconds': 60,
                'max_duplicates': 3,
                'similarity_threshold': 0.8
            }

        if 'frequency_limit' not in anti_spam:
            anti_spam['frequency_limit'] = {
                'enabled': True,
                'time_window_seconds': 30,
                'max_messages': 5
            }

        if 'warning_messages' not in anti_spam:
            anti_spam['warning_messages'] = [
                "{user_name}你个刷屏怪，再刷屏把你关小黑屋了！",
                "{user_name}别刷屏了，给其他人一点发言机会！",
                "{user_name}刷屏可不是好习惯，消停点！",
                "{user_name}你这样刷屏很影响直播间秩序哦！"
            ]

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '防刷屏配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-spam-config/warnings', methods=['POST'])
@require_auth
@check_room_access
def add_room_anti_spam_warning(room_id):
    """添加房间防刷屏警告消息"""
    try:
        data = request.get_json()
        warning = data.get('warning', '').strip()

        if not warning:
            return jsonify({'success': False, 'message': '警告内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 确保防刷屏配置存在
        if 'anti_spam' not in config[room_id]:
            config[room_id]['anti_spam'] = {
                'enabled': True,
                'block_duration': 300,
                'warning_cooldown': 60,
                'warning_messages': []
            }

        warnings = config[room_id]['anti_spam'].get('warning_messages', [])

        if warning in warnings:
            return jsonify({'success': False, 'message': '警告消息已存在'})

        warnings.append(warning)
        config[room_id]['anti_spam']['warning_messages'] = warnings

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '警告消息添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-spam-config/warnings/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_room_anti_spam_warning(room_id, index):
    """更新房间防刷屏警告消息"""
    try:
        data = request.get_json()
        new_warning = data.get('warning', '').strip()

        if not new_warning:
            return jsonify({'success': False, 'message': '警告内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        warnings = config[room_id].get('anti_spam', {}).get('warning_messages', [])

        if 0 <= index < len(warnings):
            warnings[index] = new_warning
            config[room_id]['anti_spam']['warning_messages'] = warnings

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '警告消息更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '警告消息索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/anti-spam-config/warnings/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_room_anti_spam_warning(room_id, index):
    """删除房间防刷屏警告消息"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        warnings = config[room_id].get('anti_spam', {}).get('warning_messages', [])

        if 0 <= index < len(warnings):
            warnings.pop(index)
            config[room_id]['anti_spam']['warning_messages'] = warnings

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '警告消息删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '警告消息索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/anti-spam')
def get_anti_spam_config():
    """获取防刷屏配置"""
    return get_anti_spam_config_impl()

@app.route('/api/anti-spam-config')
def get_anti_spam_config_alias():
    """获取防刷屏配置（别名接口）"""
    return get_anti_spam_config_impl()

def get_anti_spam_config_impl():
    """获取防刷屏配置"""
    try:
        config = load_rooms_config()
        anti_spam = config.get('anti_spam', {
            'enabled': True,
            'duplicate_detection': {
                'enabled': True,
                'time_window_seconds': 60,
                'max_duplicates': 3,
                'similarity_threshold': 0.8
            },
            'frequency_limit': {
                'enabled': True,
                'time_window_seconds': 30,
                'max_messages': 5
            },
            'warning_messages': [
                "{user_name}你个刷屏怪，再刷屏把你关小黑屋了！",
                "{user_name}别刷屏了，给其他人一点发言机会！",
                "{user_name}刷屏可不是好习惯，消停点！",
                "{user_name}你这样刷屏很影响直播间秩序哦！"
            ],
            'block_duration_seconds': 300,
            'warning_cooldown_seconds': 60
        })

        return jsonify({
            'success': True,
            'config': anti_spam
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/anti-spam', methods=['POST'])
@require_auth
@require_admin
def update_anti_spam_config():
    """更新防刷屏配置"""
    return update_anti_spam_config_impl()

@app.route('/api/anti-spam-config', methods=['POST'])
@require_auth
@require_admin
def update_anti_spam_config_alias():
    """更新防刷屏配置（别名接口）"""
    return update_anti_spam_config_impl()

def update_anti_spam_config_impl():
    """更新防刷屏配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        # 更新防刷屏配置
        config['anti_spam'] = {
            'enabled': data.get('enabled', True),
            'duplicate_detection': {
                'enabled': data.get('duplicate_detection', {}).get('enabled', True),
                'time_window_seconds': data.get('duplicate_detection', {}).get('time_window_seconds', 60),
                'max_duplicates': data.get('duplicate_detection', {}).get('max_duplicates', 3),
                'similarity_threshold': data.get('duplicate_detection', {}).get('similarity_threshold', 0.8)
            },
            'frequency_limit': {
                'enabled': data.get('frequency_limit', {}).get('enabled', True),
                'time_window_seconds': data.get('frequency_limit', {}).get('time_window_seconds', 30),
                'max_messages': data.get('frequency_limit', {}).get('max_messages', 5)
            },
            'warning_messages': data.get('warning_messages', []),
            'block_duration_seconds': data.get('block_duration_seconds', 300),
            'warning_cooldown_seconds': data.get('warning_cooldown_seconds', 60)
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '防刷屏配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/anti-spam/warning-messages', methods=['POST'])
@require_auth
@require_admin
def add_anti_spam_warning():
    """添加防刷屏警告消息"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()

        if not message:
            return jsonify({'success': False, 'message': '警告消息不能为空'})

        config = load_rooms_config()
        anti_spam = config.get('anti_spam', {})
        warnings = anti_spam.get('warning_messages', [])

        if message in warnings:
            return jsonify({'success': False, 'message': '警告消息已存在'})

        warnings.append(message)
        anti_spam['warning_messages'] = warnings
        config['anti_spam'] = anti_spam

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '警告消息添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/anti-spam/warning-messages/<int:index>', methods=['DELETE'])
@require_auth
@require_admin
def delete_anti_spam_warning(index):
    """删除防刷屏警告消息"""
    try:
        config = load_rooms_config()
        anti_spam = config.get('anti_spam', {})
        warnings = anti_spam.get('warning_messages', [])

        if 0 <= index < len(warnings):
            warnings.pop(index)
            anti_spam['warning_messages'] = warnings
            config['anti_spam'] = anti_spam

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': f'警告消息删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '警告消息索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/anti-spam/warning-messages/<int:index>', methods=['PUT'])
@require_auth
@require_admin
def update_anti_spam_warning(index):
    """更新防刷屏警告消息"""
    try:
        data = request.get_json()
        new_message = data.get('message', '').strip()

        if not new_message:
            return jsonify({'success': False, 'message': '警告消息不能为空'})

        config = load_rooms_config()
        anti_spam = config.get('anti_spam', {})
        warnings = anti_spam.get('warning_messages', [])

        if 0 <= index < len(warnings):
            warnings[index] = new_message
            anti_spam['warning_messages'] = warnings
            config['anti_spam'] = anti_spam

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '警告消息更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        else:
            return jsonify({'success': False, 'message': '警告消息索引无效'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 设备主动连接API ====================

@app.route('/api/devices/<device_id>/reconnect', methods=['POST'])
@require_auth
def reconnect_device(device_id):
    """主动请求设备重连"""
    try:
        # 从设备配置中获取设备信息
        devices = load_device_configs()

        if device_id not in devices:
            return jsonify({'success': False, 'message': '设备不存在'})

        device_config = devices[device_id]
        device_ip = device_config.get('last_ip')  # 需要记录设备IP

        if not device_ip:
            return jsonify({'success': False, 'message': '设备IP地址未知，无法主动连接'})

        # 尝试通过HTTP请求设备的重连接口
        # 假设设备有一个HTTP接口用于接收重连指令
        import requests

        try:
            # 向设备发送重连请求
            device_url = f"http://{device_ip}/reconnect"  # 设备的HTTP接口
            response = requests.post(device_url, json={
                'server_url': 'ws://************:15000',
                'device_id': device_id
            }, timeout=5)

            if response.status_code == 200:
                return jsonify({
                    'success': True,
                    'message': f'重连请求已发送到设备 {device_id}',
                    'device_ip': device_ip
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'设备响应错误: {response.status_code}'
                })

        except requests.exceptions.Timeout:
            return jsonify({
                'success': False,
                'message': '设备连接超时，可能设备离线或IP地址已变更'
            })
        except requests.exceptions.ConnectionError:
            return jsonify({
                'success': False,
                'message': '无法连接到设备，请检查设备是否在线'
            })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/devices/scan-network', methods=['POST'])
@require_auth
@require_admin
def scan_network_devices():
    """扫描网络中的ESP32设备"""
    try:
        import socket
        from concurrent.futures import ThreadPoolExecutor, as_completed

        def ping_device(ip):
            """检查IP是否有ESP32设备"""
            try:
                # 尝试连接设备的HTTP接口
                import requests
                response = requests.get(f"http://{ip}/status", timeout=2)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('device_type') == 'esp32':
                        return {
                            'ip': ip,
                            'device_id': data.get('device_id'),
                            'device_name': data.get('device_name'),
                            'status': 'online'
                        }
            except:
                pass
            return None

        # 获取本机IP段
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        ip_parts = local_ip.split('.')
        network_base = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"

        found_devices = []

        # 扫描网络段 (***********-254)
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = []
            for i in range(1, 255):
                ip = f"{network_base}.{i}"
                futures.append(executor.submit(ping_device, ip))

            for future in as_completed(futures):
                result = future.result()
                if result:
                    found_devices.append(result)

        return jsonify({
            'success': True,
            'devices': found_devices,
            'scanned_network': f"{network_base}.1-254"
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/devices/batch-reconnect', methods=['POST'])
@require_auth
@require_admin
def batch_reconnect_devices():
    """批量重连所有离线设备"""
    try:
        devices = load_device_configs()
        connected_devices = get_connected_devices()

        offline_devices = []
        reconnect_results = []

        for device_id, device_config in devices.items():
            # 检查设备是否离线
            is_online = any(d['device_id'] == device_id for d in connected_devices)

            if not is_online:
                offline_devices.append(device_id)

                # 尝试重连
                device_ip = device_config.get('last_ip')
                if device_ip:
                    try:
                        import requests
                        response = requests.post(f"http://{device_ip}/reconnect", json={
                            'server_url': 'ws://************:15000',
                            'device_id': device_id
                        }, timeout=3)

                        if response.status_code == 200:
                            reconnect_results.append({
                                'device_id': device_id,
                                'success': True,
                                'message': '重连请求已发送'
                            })
                        else:
                            reconnect_results.append({
                                'device_id': device_id,
                                'success': False,
                                'message': f'设备响应错误: {response.status_code}'
                            })
                    except Exception as e:
                        reconnect_results.append({
                            'device_id': device_id,
                            'success': False,
                            'message': str(e)
                        })
                else:
                    reconnect_results.append({
                        'device_id': device_id,
                        'success': False,
                        'message': 'IP地址未知'
                    })

        return jsonify({
            'success': True,
            'offline_devices_count': len(offline_devices),
            'reconnect_results': reconnect_results
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 设备名称编辑API ====================

@app.route('/api/devices/<device_id>/name', methods=['PUT'])
@require_auth
def update_device_name(device_id):
    """更新设备名称"""
    try:
        data = request.get_json()
        new_name = data.get('name', '').strip()

        if not new_name:
            return jsonify({'success': False, 'message': '设备名称不能为空'})

        if len(new_name) > 50:
            return jsonify({'success': False, 'message': '设备名称不能超过50个字符'})

        # 权限检查：只有管理员或设备所有者可以重命名
        username = request.current_user['username']
        user_role = request.current_user['role']

        if user_role != 'admin':
            user_devices = user_manager.get_user_devices(username)
            if device_id not in user_devices:
                return jsonify({'success': False, 'message': '无权操作该设备'})

        # 获取当前运行时数据
        ws_data = get_websocket_data()
        devices_list = ws_data.get('devices_list', [])
        device_configs = ws_data.get('device_configs', {})

        # 查找并更新设备名称
        device_found = False
        old_name = '未知设备'

        # 更新运行时设备列表
        for device in devices_list:
            if device.get('device_id') == device_id:
                old_name = device.get('device_name', '未知设备')
                device['device_name'] = new_name
                device_found = True
                break

        # 更新设备配置
        if device_id in device_configs:
            device_configs[device_id]['device_name'] = new_name
            device_found = True
        else:
            # 如果配置中没有，创建新配置
            device_configs[device_id] = {
                'device_name': new_name,
                'device_type': 'esp32',
                'capabilities': '',
                'auto_registered': True
            }
            device_found = True

        if not device_found:
            return jsonify({'success': False, 'message': '设备不存在'})

        # 保存更新后的数据
        ws_data['device_configs'] = device_configs
        if save_websocket_data(ws_data):
            # 同时更新shared_data中的配置
            try:
                from shared_data import save_device_configs, device_configs as shared_configs
                shared_configs.update(device_configs)
                save_device_configs()
            except Exception as e:
                print(f"⚠️ 更新shared_data失败: {e}")

            return jsonify({
                'success': True,
                'message': f'设备名称已更新: {old_name} → {new_name}',
                'old_name': old_name,
                'new_name': new_name
            })
        else:
            return jsonify({'success': False, 'message': '保存设备数据失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 设备删除API ====================

@app.route('/api/devices/<device_id>', methods=['DELETE'])
@require_auth
@require_admin
def delete_device(device_id):
    """删除设备"""
    try:
        # 从配置文件中删除设备
        device_configs = load_device_configs()
        device_found = False
        device_name = '未知设备'

        if device_id in device_configs:
            device_name = device_configs[device_id].get('device_name', '未知设备')
            del device_configs[device_id]
            device_found = True

            # 保存配置文件
            if not save_device_configs(device_configs):
                return jsonify({'success': False, 'message': '保存设备配置失败'})

        # 同时从运行时数据中删除设备
        ws_data = get_websocket_data()
        devices_list = ws_data.get('devices_list', [])

        for i, device in enumerate(devices_list):
            if device.get('device_id') == device_id:
                if not device_found:
                    device_name = device.get('device_name', '未知设备')
                    device_found = True
                devices_list.pop(i)
                break

        if not device_found:
            return jsonify({'success': False, 'message': '设备不存在'})

        # 保存更新后的运行时数据
        save_websocket_data(ws_data)

        return jsonify({
            'success': True,
            'message': f'设备已删除: {device_name} ({device_id})'
        })

    except Exception as e:
        print(f"❌ 删除设备异常: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== 礼物回复配置API ====================

@app.route('/api/rooms/<room_id>/gift-config', methods=['GET'])
@require_auth
@check_room_access
def get_gift_config(room_id):
    """获取礼物回复配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有礼物配置，创建默认配置
        if 'gift_priority' not in config[room_id]:
            default_gift_config = {
                'enabled': True,
                'priority_message_count': 2,
                'gift_response_templates': [
                    "哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧"
                ],
                'gift_weights': {
                    "玫瑰": 3,
                    "小心心": 3,
                    "跑车": 10,
                    "火箭": 50,
                    "嘉年华": 100,
                    "默认": 3
                }
            }
            config[room_id]['gift_priority'] = default_gift_config
            save_rooms_config(config)  # 保存默认配置
            print(f"✅ 为房间 {room_id} 创建了默认礼物配置")
        else:
            # 确保必要的字段存在
            if 'gift_response_templates' not in config[room_id]['gift_priority']:
                config[room_id]['gift_priority']['gift_response_templates'] = [
                    "哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧"
                ]
                save_rooms_config(config)
                print(f"✅ 为房间 {room_id} 添加了默认礼物模板")

        gift_config = config[room_id]['gift_priority']

        return jsonify({
            'success': True,
            'config': gift_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/gift-config', methods=['POST'])
@require_auth
@check_room_access
def save_room_gift_config(room_id):
    """保存房间礼物回复配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新礼物配置
        if 'gift_priority' not in config[room_id]:
            config[room_id]['gift_priority'] = {}

        config[room_id]['gift_priority']['enabled'] = data.get('enabled', True)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '礼物配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/gift-config', methods=['POST'])
@require_auth
@require_admin
def save_gift_config():
    """保存礼物回复基本配置"""
    try:
        data = request.get_json()
        config = load_config()

        if 'gift_priority' not in config:
            config['gift_priority'] = {}

        config['gift_priority']['enabled'] = data.get('enabled', True)
        config['gift_priority']['priority_message_count'] = data.get('priority_message_count', 2)

        if save_config(config):
            return jsonify({'success': True, 'message': '礼物配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/gift-config/templates', methods=['POST'])
@require_auth
@check_room_access
def add_gift_template(room_id):
    """添加礼物回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        if 'gift_priority' not in config[room_id]:
            config[room_id]['gift_priority'] = {}
        if 'gift_response_templates' not in config[room_id]['gift_priority']:
            config[room_id]['gift_priority']['gift_response_templates'] = []

        config[room_id]['gift_priority']['gift_response_templates'].append(template)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '礼物回复模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/gift-config/templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_gift_template(room_id, index):
    """更新礼物回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 确保礼物配置存在
        if 'gift_priority' not in config[room_id]:
            config[room_id]['gift_priority'] = {'gift_response_templates': []}
        if 'gift_response_templates' not in config[room_id]['gift_priority']:
            config[room_id]['gift_priority']['gift_response_templates'] = []

        templates = config[room_id]['gift_priority']['gift_response_templates']

        print(f"🔍 更新礼物模板调试: 房间={room_id}, 索引={index}, 模板数量={len(templates)}")
        print(f"🔍 当前模板列表: {templates}")

        if index < 0 or index >= len(templates):
            return jsonify({
                'success': False,
                'message': f'模板索引无效: 索引={index}, 模板数量={len(templates)}'
            })

        old_template = templates[index]
        templates[index] = template

        if save_rooms_config(config):
            print(f"✅ 礼物模板更新成功: {old_template} -> {template}")
            return jsonify({'success': True, 'message': '礼物回复模板更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        print(f"❌ 更新礼物模板失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/gift-config/templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_gift_template(room_id, index):
    """删除礼物回复模板"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        templates = config[room_id].get('gift_priority', {}).get('gift_response_templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        removed_template = templates.pop(index)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': f'礼物回复模板删除成功: {removed_template}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/gift-config/weights', methods=['POST'])
@require_auth
@require_admin
def add_gift_weight():
    """添加礼物权重"""
    try:
        data = request.get_json()
        gift_name = data.get('gift_name', '').strip()
        weight = data.get('weight', 1)

        if not gift_name:
            return jsonify({'success': False, 'message': '礼物名称不能为空'})

        if not isinstance(weight, int) or weight < 1 or weight > 1000:
            return jsonify({'success': False, 'message': '权重值必须是1-1000之间的整数'})

        config = load_config()
        if 'gift_priority' not in config:
            config['gift_priority'] = {}
        if 'gift_weights' not in config['gift_priority']:
            config['gift_priority']['gift_weights'] = {}

        config['gift_priority']['gift_weights'][gift_name] = weight

        if save_config(config):
            return jsonify({'success': True, 'message': f'礼物权重添加成功: {gift_name} = {weight}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/gift-config/weights/<gift_name>', methods=['PUT'])
@require_auth
@require_admin
def update_gift_weight(gift_name):
    """更新礼物权重"""
    try:
        data = request.get_json()
        weight = data.get('weight', 1)

        if not isinstance(weight, int) or weight < 1 or weight > 1000:
            return jsonify({'success': False, 'message': '权重值必须是1-1000之间的整数'})

        config = load_config()
        weights = config.get('gift_priority', {}).get('gift_weights', {})

        if gift_name not in weights:
            return jsonify({'success': False, 'message': '礼物权重不存在'})

        weights[gift_name] = weight

        if save_config(config):
            return jsonify({'success': True, 'message': f'礼物权重更新成功: {gift_name} = {weight}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/gift-config/weights/<gift_name>', methods=['DELETE'])
@require_auth
@require_admin
def delete_gift_weight(gift_name):
    """删除礼物权重"""
    try:
        config = load_config()
        weights = config.get('gift_priority', {}).get('gift_weights', {})

        if gift_name not in weights:
            return jsonify({'success': False, 'message': '礼物权重不存在'})

        if gift_name == '默认':
            return jsonify({'success': False, 'message': '不能删除默认权重'})

        del weights[gift_name]

        if save_config(config):
            return jsonify({'success': True, 'message': f'礼物权重删除成功: {gift_name}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 关注互动配置API ====================

@app.route('/api/rooms/<room_id>/follow-config', methods=['GET'])
@require_auth
@check_room_access
def get_follow_config(room_id):
    """获取关注互动配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有关注互动配置，创建默认配置
        if 'follow_interaction' not in config[room_id]:
            default_follow_config = {
                'enabled': True,
                'response_templates': [
                    "哟 {user_name}关注了我，是不是看上我了？",
                    "{user_name}终于关注了，眼光还不错嘛",
                    "欢迎{user_name}，不过关注了可别后悔哦",
                    "{user_name}关注了我，算你有品味",
                    "哟{user_name}，关注我是你做过最明智的决定"
                ]
            }
            config[room_id]['follow_interaction'] = default_follow_config
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认关注互动配置")
        else:
            # 确保必要的字段存在
            if 'response_templates' not in config[room_id]['follow_interaction']:
                config[room_id]['follow_interaction']['response_templates'] = [
                    "哟 {user_name}关注了我，是不是看上我了？",
                    "{user_name}终于关注了，眼光还不错嘛"
                ]
                save_rooms_config(config)
                print(f"✅ 为房间 {room_id} 添加了默认关注互动模板")

        follow_config = config[room_id]['follow_interaction']

        return jsonify({
            'success': True,
            'config': follow_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/follow-config', methods=['POST'])
@require_auth
@check_room_access
def save_room_follow_config(room_id):
    """保存房间关注互动配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新关注配置
        if 'follow_interaction' not in config[room_id]:
            config[room_id]['follow_interaction'] = {}

        config[room_id]['follow_interaction']['enabled'] = data.get('enabled', True)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '关注配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/follow-config', methods=['POST'])
@require_auth
@require_admin
def save_follow_config():
    """保存关注互动基本配置"""
    try:
        data = request.get_json()
        config = load_config()

        if 'follow_interaction' not in config:
            config['follow_interaction'] = {}

        config['follow_interaction']['enabled'] = data.get('enabled', True)

        if save_config(config):
            return jsonify({'success': True, 'message': '关注配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/follow-config/templates', methods=['POST'])
@require_auth
@check_room_access
def add_follow_template(room_id):
    """添加关注回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        if 'follow_interaction' not in config[room_id]:
            config[room_id]['follow_interaction'] = {}
        if 'response_templates' not in config[room_id]['follow_interaction']:
            config[room_id]['follow_interaction']['response_templates'] = []

        config[room_id]['follow_interaction']['response_templates'].append(template)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '关注回复模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/follow-config/templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_follow_template(room_id, index):
    """更新关注回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        templates = config[room_id].get('follow_interaction', {}).get('response_templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        templates[index] = template

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '关注回复模板更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/follow-config/templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_follow_template(room_id, index):
    """删除关注回复模板"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        templates = config[room_id].get('follow_interaction', {}).get('response_templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        removed_template = templates.pop(index)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': f'关注回复模板删除成功: {removed_template}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 互动游戏配置API ====================

@app.route('/api/rooms/<room_id>/game-config', methods=['GET'])
@require_auth
@check_room_access
def get_room_game_config(room_id):
    """获取房间互动游戏配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有互动游戏配置，创建默认配置
        if 'interactive_games' not in config[room_id]:
            default_game_config = {
                'enabled': True,
                'cooldown_seconds': 30,
                'auto_trigger': True,
                'auto_interval_minutes': 5,
                'games': {
                    'guess_number': {'enabled': True, 'range': [1, 100]},
                    'word_chain': {'enabled': True, 'difficulty': 'medium'},
                    'riddle': {'enabled': True, 'category': 'all'}
                }
            }
            config[room_id]['interactive_games'] = default_game_config
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认互动游戏配置")

        game_config = config[room_id]['interactive_games']

        return jsonify({
            'success': True,
            'config': game_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/game-config', methods=['POST'])
@require_auth
@check_room_access
def update_room_game_config(room_id):
    """更新房间互动游戏配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新房间的游戏配置
        config[room_id]['interactive_games'] = {
            'enabled': data.get('enabled', True),
            'cooldown_seconds': data.get('cooldown_seconds', 30),
            'auto_trigger': data.get('auto_trigger', True),
            'auto_interval_minutes': data.get('auto_interval_minutes', 5),
            'games': data.get('games', {})
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '游戏配置已更新'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/game-config', methods=['GET'])
def get_game_config():
    """获取互动游戏配置（兼容性API）"""
    try:
        config = load_config()
        game_config = config.get('interactive_games', {
            'enabled': True,
            'cooldown_seconds': 30,
            'auto_trigger': True,
            'auto_interval_minutes': 5
        })

        return jsonify({
            'success': True,
            'config': game_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/game-config', methods=['POST'])
@require_auth
@require_admin
def save_game_config():
    """保存互动游戏配置"""
    try:
        data = request.get_json()
        config = load_config()

        if 'interactive_games' not in config:
            config['interactive_games'] = {}

        config['interactive_games']['enabled'] = data.get('enabled', True)
        config['interactive_games']['cooldown_seconds'] = data.get('cooldown_seconds', 30)
        config['interactive_games']['auto_trigger'] = data.get('auto_trigger', True)
        config['interactive_games']['auto_interval_minutes'] = data.get('auto_interval_minutes', 5)

        if save_config(config):
            return jsonify({'success': True, 'message': '游戏配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 点赞配置API ====================

@app.route('/api/rooms/<room_id>/like-config', methods=['GET'])
@require_auth
@check_room_access
def get_like_config(room_id):
    """获取点赞配置"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 如果房间没有点赞互动配置，创建默认配置
        if 'like_interaction' not in config[room_id]:
            default_like_config = {
                'enabled': True,
                'cooldown_seconds': 60,
                'first_like_threshold': 1,
                'subsequent_like_threshold': 100,
                'templates': []
            }
            config[room_id]['like_interaction'] = default_like_config
            save_rooms_config(config)
            print(f"✅ 为房间 {room_id} 创建了默认点赞互动配置")
        else:
            # 确保必要的字段存在
            if 'templates' not in config[room_id]['like_interaction']:
                config[room_id]['like_interaction']['templates'] = []
                save_rooms_config(config)
                print(f"✅ 为房间 {room_id} 添加了点赞互动模板字段")

        like_config = config[room_id]['like_interaction']

        return jsonify({
            'success': True,
            'config': like_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/like-config', methods=['POST'])
@require_auth
@check_room_access
def save_room_like_config(room_id):
    """保存房间点赞配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        # 更新点赞配置
        if 'like_interaction' not in config[room_id]:
            config[room_id]['like_interaction'] = {}

        config[room_id]['like_interaction']['enabled'] = data.get('enabled', True)
        config[room_id]['like_interaction']['cooldown_seconds'] = data.get('cooldown_seconds', 60)
        config[room_id]['like_interaction']['first_like_threshold'] = data.get('first_like_threshold', 1)
        config[room_id]['like_interaction']['subsequent_like_threshold'] = data.get('subsequent_like_threshold', 100)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '点赞配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/like-config', methods=['POST'])
@require_auth
@require_admin
def update_like_config():
    """更新点赞配置"""
    try:
        data = request.get_json()
        config = load_rooms_config()

        # 更新点赞配置
        config['like_interaction'] = {
            'enabled': data.get('enabled', True),
            'cooldown_seconds': data.get('cooldown_seconds', 60),
            'first_like_threshold': data.get('first_like_threshold', 1),
            'subsequent_like_threshold': data.get('subsequent_like_threshold', 100)
        }

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '点赞配置保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/like-config/templates', methods=['POST'])
@require_auth
@check_room_access
def add_like_template(room_id):
    """添加点赞回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        if 'like_interaction' not in config[room_id]:
            config[room_id]['like_interaction'] = {}
        if 'templates' not in config[room_id]['like_interaction']:
            config[room_id]['like_interaction']['templates'] = []

        config[room_id]['like_interaction']['templates'].append(template)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '点赞回复模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/like-config/templates/<int:index>', methods=['PUT'])
@require_auth
@check_room_access
def update_like_template(room_id, index):
    """更新点赞回复模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        templates = config[room_id].get('like_interaction', {}).get('templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        templates[index] = template

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '点赞回复模板更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/rooms/<room_id>/like-config/templates/<int:index>', methods=['DELETE'])
@require_auth
@check_room_access
def delete_like_template(room_id, index):
    """删除点赞回复模板"""
    try:
        config = load_rooms_config()
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})

        templates = config[room_id].get('like_interaction', {}).get('templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        removed_template = templates.pop(index)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': f'点赞回复模板删除成功: {removed_template}'})
        else:
            return jsonify({'success': False, 'message': '保存配置文件失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== 房间管理API ====================



# ==================== 方案管理API（已废弃） ====================
# 注意：方案管理功能已废弃，现在使用房间配置系统
# 相关API已移除，配置直接在房间配置中管理

# ==================== 新功能配置API ====================

@app.route('/api/preprocessing-config', methods=['GET', 'POST'])
@require_auth
@require_admin
def preprocessing_config():
    """消息预处理配置管理"""
    if request.method == 'GET':
        # 从配置文件读取预处理配置
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            preprocessing_config = config_data.get('message_preprocessing', {
                'enabled': True,
                'max_repeat_chars': 3,
                'max_length': 200
            })

            return jsonify({'success': True, 'config': preprocessing_config})
        except Exception as e:
            return jsonify({'success': False, 'message': f'读取配置失败: {str(e)}'})

    elif request.method == 'POST':
        # 保存预处理配置
        try:
            data = request.get_json()

            # 读取现有配置
            with open('config.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 更新预处理配置
            config_data['message_preprocessing'] = {
                'enabled': data.get('enabled', True),
                'max_repeat_chars': data.get('max_repeat_chars', 3),
                'max_length': data.get('max_length', 200)
            }

            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            return jsonify({'success': True, 'message': '预处理配置保存成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'保存配置失败: {str(e)}'})

@app.route('/api/rooms/<room_id>/broadcast-config', methods=['GET', 'POST'])
@require_auth
@check_room_access
def room_broadcast_config(room_id):
    """房间定时插播配置管理"""
    if request.method == 'GET':
        # 从配置文件读取插播配置
        try:
            config = load_rooms_config()
            if room_id not in config:
                return jsonify({'success': False, 'message': '房间不存在'})
                
            # 如果房间没有定时插播配置，创建默认配置
            if 'scheduled_broadcast' not in config[room_id]:
                default_broadcast_config = {
                    'enabled': True,
                    'interval_minutes': 5,
                    'templates': [
                        "我是大卖机，全球智商第二的卖货机器人，我不吃饭不喝水不休息，也不要你发工资。我能看见直播间的问题，自动制造互动的话题。"
                    ]
                }
                config[room_id]['scheduled_broadcast'] = default_broadcast_config
                save_rooms_config(config)
                print(f"✅ 为房间 {room_id} 创建了默认定时插播配置")
            else:
                # 确保必要的字段存在
                if 'templates' not in config[room_id]['scheduled_broadcast']:
                    config[room_id]['scheduled_broadcast']['templates'] = [
                        "我是大卖机，全球智商第二的卖货机器人，我不吃饭不喝水不休息，也不要你发工资。我能看见直播间的问题，自动制造互动的话题。"
                    ]
                    save_rooms_config(config)
                    print(f"✅ 为房间 {room_id} 添加了默认定时插播模板")

            broadcast_config = config[room_id]['scheduled_broadcast']

            return jsonify({'success': True, 'config': broadcast_config})
        except Exception as e:
            return jsonify({'success': False, 'message': f'读取配置失败: {str(e)}'})

    elif request.method == 'POST':
        # 保存插播配置
        try:
            data = request.get_json()
            config = load_rooms_config()

            if room_id not in config:
                config[room_id] = {}

            # 更新插播配置（保留现有模板）
            if 'scheduled_broadcast' not in config[room_id]:
                config[room_id]['scheduled_broadcast'] = {
                    'templates': [
                        "欢迎大家来到硅灵造物直播间！记得点赞关注哦！",
                        "有什么问题可以随时在评论区提问，我会尽力回答！"
                    ]
                }

            # 保留现有模板，只更新基本配置
            broadcast_config = config[room_id]['scheduled_broadcast']
            broadcast_config['enabled'] = data.get('enabled', broadcast_config.get('enabled', True))

            # 验证间隔时间范围（1-60分钟）
            interval_minutes = data.get('interval_minutes', broadcast_config.get('interval_minutes', 10))
            if not isinstance(interval_minutes, (int, float)) or interval_minutes < 1 or interval_minutes > 60:
                return jsonify({'success': False, 'message': '插播间隔必须在1-60分钟之间'})

            broadcast_config['interval_minutes'] = int(interval_minutes)

            # 确保模板字段存在
            if 'templates' not in broadcast_config:
                broadcast_config['templates'] = [
                    "欢迎大家来到硅灵造物直播间！记得点赞关注哦！",
                    "有什么问题可以随时在评论区提问，我会尽力回答！"
                ]

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播配置保存成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'保存配置失败: {str(e)}'})

# 保留全局配置的兼容性API
@app.route('/api/broadcast-config', methods=['GET', 'POST'])
@require_auth
@require_admin
def broadcast_config():
    """定时插播配置管理（兼容性API）"""
    if request.method == 'GET':
        # 从配置文件读取插播配置
        try:
            config = load_rooms_config()
            broadcast_config = config.get('scheduled_broadcast', {
                'enabled': True,
                'interval_minutes': 10,
                'templates': [
                    "欢迎大家来到硅灵造物直播间！记得点赞关注哦！",
                    "有什么问题可以随时在评论区提问，我会尽力回答！"
                ]
            })

            return jsonify({'success': True, 'config': broadcast_config})
        except Exception as e:
            return jsonify({'success': False, 'message': f'读取配置失败: {str(e)}'})

    elif request.method == 'POST':
        # 保存插播配置
        try:
            data = request.get_json()
            config = load_rooms_config()

            # 更新插播配置（保留现有模板）
            if 'scheduled_broadcast' not in config:
                config['scheduled_broadcast'] = {}

            config['scheduled_broadcast']['enabled'] = data.get('enabled', True)
            config['scheduled_broadcast']['interval_minutes'] = data.get('interval_minutes', 10)

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播配置保存成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'保存配置失败: {str(e)}'})

@app.route('/api/rooms/<room_id>/broadcast-config/templates', methods=['POST'])
@require_auth
@check_room_access
def add_room_broadcast_template(room_id):
    """添加房间插播模板"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        
        if room_id not in config:
            config[room_id] = {}
        
        # 确保插播配置存在
        if 'scheduled_broadcast' not in config[room_id]:
            config[room_id]['scheduled_broadcast'] = {'templates': []}
        if 'templates' not in config[room_id]['scheduled_broadcast']:
            config[room_id]['scheduled_broadcast']['templates'] = []

        # 添加新模板
        config[room_id]['scheduled_broadcast']['templates'].append(template)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '插播模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@app.route('/api/rooms/<room_id>/broadcast-config/templates/<int:index>', methods=['PUT', 'DELETE'])
@require_auth
@check_room_access
def manage_room_broadcast_template(room_id, index):
    """管理房间插播模板"""
    try:
        config = load_rooms_config()
        
        if room_id not in config:
            return jsonify({'success': False, 'message': '房间不存在'})
            
        templates = config[room_id].get('scheduled_broadcast', {}).get('templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        if request.method == 'PUT':
            # 更新模板
            data = request.get_json()
            new_template = data.get('template', '').strip()

            if not new_template:
                return jsonify({'success': False, 'message': '模板内容不能为空'})

            templates[index] = new_template
            config[room_id]['scheduled_broadcast']['templates'] = templates

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播模板更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})

        elif request.method == 'DELETE':
            # 删除模板
            templates.pop(index)
            config[room_id]['scheduled_broadcast']['templates'] = templates

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播模板删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

# 保留全局配置的兼容性API
@app.route('/api/broadcast-config/templates', methods=['POST'])
@require_auth
@require_admin
def add_broadcast_template():
    """添加插播模板（兼容性API）"""
    try:
        data = request.get_json()
        template = data.get('template', '').strip()

        if not template:
            return jsonify({'success': False, 'message': '模板内容不能为空'})

        config = load_rooms_config()
        
        # 确保插播配置存在
        if 'scheduled_broadcast' not in config:
            config['scheduled_broadcast'] = {'templates': []}
        if 'templates' not in config['scheduled_broadcast']:
            config['scheduled_broadcast']['templates'] = []

        # 添加新模板
        config['scheduled_broadcast']['templates'].append(template)

        if save_rooms_config(config):
            return jsonify({'success': True, 'message': '插播模板添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存配置失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@app.route('/api/broadcast-config/templates/<int:index>', methods=['PUT', 'DELETE'])
@require_auth
@require_admin
def manage_broadcast_template(index):
    """管理插播模板（兼容性API）"""
    try:
        config = load_rooms_config()
        templates = config.get('scheduled_broadcast', {}).get('templates', [])

        if index < 0 or index >= len(templates):
            return jsonify({'success': False, 'message': '模板索引无效'})

        if request.method == 'PUT':
            # 更新模板
            data = request.get_json()
            new_template = data.get('template', '').strip()

            if not new_template:
                return jsonify({'success': False, 'message': '模板内容不能为空'})

            templates[index] = new_template
            config['scheduled_broadcast']['templates'] = templates

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播模板更新成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})

        elif request.method == 'DELETE':
            # 删除模板
            templates.pop(index)
            config['scheduled_broadcast']['templates'] = templates

            if save_rooms_config(config):
                return jsonify({'success': True, 'message': '插播模板删除成功'})
            else:
                return jsonify({'success': False, 'message': '保存配置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

# ==================== 排行榜API ====================

@app.route('/api/leaderboard/interactions')
def get_interaction_leaderboard():
    """获取互动排行榜"""
    try:
        limit = request.args.get('limit', 10, type=int)
        room_id = request.args.get('room_id', None)

        # 参数验证
        if limit < 1 or limit > 100:
            return jsonify({
                'success': False,
                'message': '限制数量必须在1-100之间',
                'error_code': 'INVALID_LIMIT'
            }), 400

        leaderboard = leaderboard_stats.get_interaction_leaderboard(limit, room_id)
        return jsonify({
            'success': True,
            'leaderboard': leaderboard,
            'cache_info': leaderboard_stats.get_stats_summary().get('cache_info', {})
        })
    except FileNotFoundError as e:
        print(f"❌ 排行榜数据文件不存在: {e}")
        return jsonify({
            'success': False,
            'message': '排行榜数据暂时不可用',
            'error_code': 'DATA_FILE_NOT_FOUND'
        }), 503
    except json.JSONDecodeError as e:
        print(f"❌ 排行榜数据格式错误: {e}")
        return jsonify({
            'success': False,
            'message': '排行榜数据格式错误',
            'error_code': 'DATA_FORMAT_ERROR'
        }), 503
    except Exception as e:
        print(f"❌ 获取互动排行榜失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': '服务器内部错误',
            'error_code': 'INTERNAL_ERROR'
        }), 500

@app.route('/api/leaderboard/gifts')
def get_gift_leaderboard():
    """获取礼物排行榜"""
    try:
        limit = request.args.get('limit', 10, type=int)
        room_id = request.args.get('room_id', None)

        # 参数验证
        if limit < 1 or limit > 100:
            return jsonify({
                'success': False,
                'message': '限制数量必须在1-100之间',
                'error_code': 'INVALID_LIMIT'
            }), 400

        leaderboard = leaderboard_stats.get_gift_leaderboard(limit, room_id)
        return jsonify({
            'success': True,
            'leaderboard': leaderboard
        })
    except Exception as e:
        print(f"❌ 获取礼物排行榜失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取礼物排行榜失败',
            'error_code': 'INTERNAL_ERROR'
        }), 500

@app.route('/api/leaderboard/stats')
def get_leaderboard_stats():
    """获取排行榜统计摘要"""
    try:
        stats = leaderboard_stats.get_stats_summary()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        print(f"❌ 获取排行榜统计失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取统计数据失败',
            'error_code': 'STATS_ERROR'
        }), 500

@app.route('/api/leaderboard/rooms')
def get_leaderboard_rooms():
    """获取排行榜房间列表"""
    try:
        rooms = leaderboard_stats.get_rooms()
        return jsonify({
            'success': True,
            'rooms': rooms
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/leaderboard/official')
def get_official_leaderboard():
    """获取官方排行榜（基于WebcastRoomRankMessage）"""
    try:
        room_id = request.args.get('room_id', None)
        official_ranks = leaderboard_stats.get_official_rank(room_id)
        return jsonify({
            'success': True,
            'leaderboard': official_ranks
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/leaderboard/official/update', methods=['POST'])
def update_official_leaderboard():
    """更新官方排行榜数据（接收来自liveMan的数据）"""
    try:
        data = request.get_json()
        room_id = data.get('room_id')
        rank_data = data.get('rank_data', [])

        if not room_id:
            return jsonify({'success': False, 'message': '缺少room_id参数'})

        # 更新排行榜数据
        leaderboard_stats.update_official_rank(room_id, rank_data)

        print(f"📊 通过API更新房间 {room_id} 的官方排行榜，共 {len(rank_data)} 位用户")

        return jsonify({
            'success': True,
            'message': f'成功更新 {len(rank_data)} 条排行榜数据'
        })
    except Exception as e:
        print(f"❌ 更新官方排行榜失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/leaderboard/cache/clear', methods=['POST'])
@require_auth
@require_admin
def clear_leaderboard_cache():
    """清除排行榜缓存（仅管理员）"""
    try:
        leaderboard_stats.clear_cache()
        return jsonify({
            'success': True,
            'message': '排行榜缓存已清除'
        })
    except Exception as e:
        print(f"❌ 清除排行榜缓存失败: {e}")
        return jsonify({
            'success': False,
            'message': '清除缓存失败',
            'error_code': 'CACHE_CLEAR_ERROR'
        }), 500

@app.route('/api/leaderboard/rooms-ranking')
def get_rooms_ranking():
    """获取直播间在线观众数量排行榜"""
    try:
        # 获取所有房间的状态信息
        rooms_status = {}

        # 从room_manager获取房间状态
        if hasattr(room_manager, 'rooms'):
            for room_id, room_info in room_manager.rooms.items():
                if room_info.get('status') == 'running':
                    # 获取在线观众数量
                    viewer_count = room_info.get('viewer_count', 0)
                    room_name = room_info.get('room_name', f'直播间{room_id}')

                    rooms_status[room_id] = {
                        'room_id': room_id,
                        'room_name': room_name,
                        'viewer_count': viewer_count,
                        'status': 'live',
                        'start_time': room_info.get('start_time'),
                        'duration': room_info.get('duration', 0)
                    }

        # 按观众数量排序
        sorted_rooms = sorted(
            rooms_status.values(),
            key=lambda x: x['viewer_count'],
            reverse=True
        )

        return jsonify({
            'success': True,
            'rooms': sorted_rooms,
            'total_live_rooms': len(sorted_rooms),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ 获取直播间排行榜失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e),
            'error_code': 'ROOMS_RANKING_ERROR'
        }), 500

# ==================== 用户管理API ====================

@app.route('/api/users', methods=['GET'])
@require_auth
@require_admin
def get_users():
    """获取所有用户列表（仅管理员）"""
    try:
        print("[DEBUG] 收到用户列表请求")
        username = request.current_user['username']
        user_role = request.current_user['role']
        print(f"[DEBUG] 用户 {username} ({user_role}) 请求用户列表")

        users = user_manager.get_all_users_with_member_info()
        print(f"[DEBUG] 获取到 {len(users)} 个用户")

        return jsonify({'success': True, 'users': users})
    except Exception as e:
        print(f"[ERROR] 获取用户列表失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users', methods=['POST'])
@require_auth
@require_admin
def create_user():
    """创建用户（仅管理员）"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        role = data.get('role', 'user')
        display_name = data.get('display_name', '').strip()
        email = data.get('email', '').strip()

        result = user_manager.create_user(username, password, role, display_name, email)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/rooms', methods=['POST'])
@require_auth
@require_admin
def assign_room_to_user(username):
    """分配房间给用户（仅管理员）"""
    try:
        data = request.get_json()
        room_id = data.get('room_id', '').strip()

        if not room_id:
            return jsonify({'success': False, 'message': '房间ID不能为空'})

        result = user_manager.assign_room_to_user(username, room_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/rooms/<room_id>', methods=['DELETE'])
@require_auth
@require_admin
def remove_room_from_user(username, room_id):
    """从用户移除房间（仅管理员）"""
    try:
        result = user_manager.remove_room_from_user(username, room_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/devices', methods=['POST'])
@require_auth
@require_admin
def assign_device_to_user(username):
    """分配设备给用户（仅管理员）"""
    try:
        data = request.get_json()
        device_id = data.get('device_id', '').strip()

        if not device_id:
            return jsonify({'success': False, 'message': '设备ID不能为空'})

        result = user_manager.assign_device_to_user(username, device_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/devices/<device_id>', methods=['DELETE'])
@require_auth
@require_admin
def remove_device_from_user(username, device_id):
    """从用户移除设备（仅管理员）"""
    try:
        result = user_manager.remove_device_from_user(username, device_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/device-assignments', methods=['GET'])
@require_auth
def get_device_assignments():
    """获取所有设备的分配信息（仅管理员）"""
    try:
        username = request.current_user['username']
        user_role = request.current_user['role']

        if user_role != 'admin':
            return jsonify({'success': False, 'message': '权限不足'})

        # 获取所有用户的设备分配信息
        all_users = user_manager.get_all_users()
        assignments = {}

        for user in all_users:
            user_devices = user.get('assigned_devices', [])
            for device_id in user_devices:
                assignments[device_id] = user['username']

        return jsonify({
            'success': True,
            'assignments': assignments
        })
    except Exception as e:
        print(f"❌ 获取设备分配信息异常: {e}")
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/user/info')
@require_auth
def get_user_info_api():
    """获取当前用户信息（包含会员状态）"""
    try:
        username = request.current_user['username']
        user_info = user_manager.get_user_info(username)
        if user_info:
            # 添加会员状态信息
            member_status = user_manager.check_member_status(username)
            user_info['member_status'] = member_status
            return jsonify({'success': True, 'user': user_info})
        else:
            return jsonify({'success': False, 'message': '用户不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>')
@require_auth
@require_admin
def get_user_info(username):
    """获取指定用户信息（仅管理员）"""
    try:
        user_info = user_manager.get_user_info(username)
        if user_info:
            return jsonify({'success': True, 'user': user_info})
        else:
            return jsonify({'success': False, 'message': '用户不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/password', methods=['PUT'])
@require_auth
@require_admin
def reset_user_password(username):
    """重置用户密码（仅管理员）"""
    try:
        data = request.get_json()
        new_password = data.get('password', '').strip()

        if not new_password:
            return jsonify({'success': False, 'message': '密码不能为空'})

        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少6位'})

        result = user_manager.reset_password(username, new_password)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>', methods=['DELETE'])
@require_auth
@require_admin
def delete_user(username):
    """删除用户（仅管理员）"""
    try:
        print(f"[DEBUG] 收到删除用户请求: {username}")
        current_user = request.current_user['username']
        
        # 防止用户删除自己
        if current_user == username:
            return jsonify({'success': False, 'message': '不能删除自己的账户'})
        
        result = user_manager.delete_user(username)
        
        if result['success']:
            print(f"[SUCCESS] 用户删除成功: {username}")
        else:
            print(f"[ERROR] 用户删除失败: {result['message']}")
            
        return jsonify(result)
    except Exception as e:
        print(f"[ERROR] 删除用户异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/status', methods=['PUT'])
@require_auth
@require_admin
def toggle_user_status(username):
    """切换用户状态（仅管理员）"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        result = user_manager.set_user_status(username, enabled)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/search', methods=['GET'])
@require_auth
@require_admin
def search_users():
    """搜索用户（仅管理员）"""
    try:
        query = request.args.get('q', '').strip()
        users = user_manager.search_users(query)

        return jsonify({
            'success': True,
            'users': users,
            'total': len(users),
            'query': query
        })
    except Exception as e:
        print(f"❌ 搜索用户失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== 会员管理API ====================

@app.route('/api/users/<username>/member', methods=['GET'])
@require_auth
@require_admin
def get_user_member_status(username):
    """获取用户会员状态（仅管理员）"""
    try:
        status = user_manager.check_member_status(username)
        return jsonify({'success': True, 'member_status': status})
    except Exception as e:
        print(f"❌ 获取用户会员状态失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/member', methods=['POST'])
@require_auth
@require_admin
def activate_user_member(username):
    """开通用户会员（仅管理员）"""
    try:
        data = request.get_json()
        days = data.get('days', 30)

        if days <= 0 or days > 3650:  # 最多10年
            return jsonify({'success': False, 'message': '会员天数必须在1-3650天之间'})

        result = user_manager.activate_member(username, days)
        return jsonify(result)
    except Exception as e:
        print(f"❌ 开通用户会员失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/users/<username>/member', methods=['DELETE'])
@require_auth
@require_admin
def deactivate_user_member(username):
    """取消用户会员（仅管理员）"""
    try:
        result = user_manager.deactivate_member(username)
        return jsonify(result)
    except Exception as e:
        print(f"❌ 取消用户会员失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/member/check', methods=['GET'])
@require_auth
def check_current_user_member():
    """检查当前用户会员状态"""
    try:
        username = request.current_user['username']
        status = user_manager.check_member_status(username)
        return jsonify({'success': True, 'member_status': status})
    except Exception as e:
        print(f"❌ 检查当前用户会员状态失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== 个人设置API ====================
@app.route('/api/profile', methods=['GET', 'PUT'])
@require_auth
def profile_api():
    """个人设置API - 获取和更新用户信息"""
    print(f"DEBUG: profile_api called with method: {request.method}")
    if request.method == 'GET':
        # 获取用户信息
        try:
            print(f"DEBUG: request.current_user = {request.current_user}")
            print(f"DEBUG: type(request.current_user) = {type(request.current_user)}")
            print(f"DEBUG: request.current_user.keys() = {list(request.current_user.keys()) if isinstance(request.current_user, dict) else 'Not a dict'}")
            username = request.current_user['username']
            print(f"DEBUG: username = {username}")
            print(f"DEBUG: type(username) = {type(username)}")
            print(f"DEBUG: About to call user_manager.get_user_info with username={repr(username)}")
            user_info = user_manager.get_user_info(username)
            print(f"DEBUG: user_info = {user_info}")

            if user_info:
                return jsonify({
                    'success': True,
                    'user': {
                        'username': user_info['username'],
                        'display_name': user_info['display_name'],
                        'email': user_info['email'],
                        'role': user_info['role'],
                        'created_at': user_info['created_at'],
                        'enabled': user_info['enabled']
                    }
                })
            else:
                return jsonify({'success': False, 'message': '用户不存在'})

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取用户信息失败: {str(e)}'})

    elif request.method == 'PUT':
        # 更新用户信息
        try:
            username = request.current_user['username']
            data = request.get_json()

            # 验证输入
            display_name = data.get('display_name', '').strip()
            email = data.get('email', '').strip()

            if not display_name:
                return jsonify({'success': False, 'message': '显示名称不能为空'})

            if email and '@' not in email:
                return jsonify({'success': False, 'message': '邮箱格式不正确'})

            # 更新用户信息
            success = user_manager.update_user(username, {
                'display_name': display_name,
                'email': email
            })

            if success:
                return jsonify({'success': True, 'message': '个人信息更新成功'})
            else:
                return jsonify({'success': False, 'message': '更新失败'})

        except Exception as e:
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@app.route('/api/profile/password', methods=['PUT'])
@require_auth
def change_password():
    """修改密码"""
    try:
        username = request.current_user['username']
        data = request.get_json()

        current_password = data.get('current_password', '')
        new_password = data.get('new_password', '')
        confirm_password = data.get('confirm_password', '')

        # 验证输入
        if not current_password or not new_password:
            return jsonify({'success': False, 'message': '密码不能为空'})

        if new_password != confirm_password:
            return jsonify({'success': False, 'message': '新密码确认不匹配'})

        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '新密码长度至少6位'})

        # 验证当前密码
        if not user_manager.authenticate(username, current_password):
            return jsonify({'success': False, 'message': '当前密码错误'})

        # 更新密码
        success = user_manager.update_user(username, {'password': new_password})

        if success:
            return jsonify({'success': True, 'message': '密码修改成功'})
        else:
            return jsonify({'success': False, 'message': '密码修改失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'密码修改失败: {str(e)}'})

@app.route('/api/profile/delete', methods=['DELETE'])
@require_auth
def delete_account():
    """删除账户"""
    try:
        username = request.current_user['username']
        data = request.get_json()

        password = data.get('password', '')
        confirmation = data.get('confirmation', '')

        # 防止删除管理员账户
        user_info = user_manager.get_user_info(username)
        if user_info and user_info.get('role') == 'admin':
            return jsonify({'success': False, 'message': '管理员账户不能删除'})

        # 验证密码
        if not user_manager.authenticate(username, password):
            return jsonify({'success': False, 'message': '密码错误'})

        # 验证确认文本
        if confirmation != '删除我的账户':
            return jsonify({'success': False, 'message': '确认文本不正确'})

        # 删除用户
        success = user_manager.delete_user(username)

        if success:
            return jsonify({'success': True, 'message': '账户删除成功'})
        else:
            return jsonify({'success': False, 'message': '账户删除失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'账户删除失败: {str(e)}'})

def start_flask_server():
    """启动Flask服务器的函数"""
    # 执行系统初始化
    _initialize_system()

    print("[Web] 启动Flask Web服务器")
    print("[地址] 地址: http://0.0.0.0:15008")
    print("[连接] 连接到ESP32 WebSocket服务器: ws://0.0.0.0:15000")
    try:
        app.run(host='0.0.0.0', port=15008, debug=False)
    except OSError as e:
        if "Address already in use" in str(e) or "Port 15008 is in use" in str(e):
            print("[警告] Flask服务器端口15008已被占用，跳过启动")
            print("[提示] 这通常表示服务器已经在运行中")
        else:
            raise e

if __name__ == '__main__':
    start_flask_server()
