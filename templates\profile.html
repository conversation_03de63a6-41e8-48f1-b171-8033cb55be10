<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 全局字体设置 */
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 抖音Logo样式 */
        .douyin-logo {
            margin-right: 8px;
            vertical-align: middle;
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 页面布局 */
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--base-size-24);
        }

        .profile-header {
            text-align: center;
            margin-bottom: var(--base-size-32);
            padding-bottom: var(--base-size-24);
            border-bottom: 1px solid var(--color-border-default);
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--color-accent-subtle);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--base-size-16);
            font-size: 32px;
            color: var(--color-accent-fg);
        }

        .profile-sections {
            display: grid;
            gap: var(--base-size-24);
        }

        .profile-section {
            background: var(--color-canvas-subtle);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            padding: var(--base-size-24);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-16);
            font-size: 18px;
            font-weight: 600;
            color: var(--color-fg-default);
        }

        .form-group {
            margin-bottom: var(--base-size-16);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--base-size-8);
            font-weight: 500;
            color: var(--color-fg-default);
        }

        .form-group input {
            width: 100%;
            padding: var(--base-size-8) var(--base-size-12);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            background-color: var(--color-canvas-default);
            color: var(--color-fg-default);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--color-accent-emphasis);
            box-shadow: 0 0 0 3px var(--color-accent-subtle);
        }

        .form-group input:disabled {
            background-color: var(--color-canvas-subtle);
            color: var(--color-fg-muted);
            cursor: not-allowed;
        }

        .form-actions {
            display: flex;
            gap: var(--base-size-12);
            margin-top: var(--base-size-20);
        }

        .danger-zone {
            border-color: var(--color-danger-muted);
            background-color: var(--color-danger-subtle);
        }

        .danger-zone .section-title {
            color: var(--color-danger-fg);
        }

        .warning-text {
            background: var(--color-attention-subtle);
            border: 1px solid var(--color-attention-muted);
            border-radius: var(--borderRadius-small);
            padding: var(--base-size-12);
            margin-bottom: var(--base-size-16);
            color: var(--color-attention-fg);
            font-size: 14px;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .profile-container {
                padding: var(--base-size-16);
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- GitHub风格导航栏 -->
    <nav class="github-navbar">
        <div class="github-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <a class="navbar-brand-github" href="/">
                    <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                    <span>AI机器人自动化直播控制台</span>
                </a>
                <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                    <a href="/" class="btn-github btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        返回主页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="profile-container">
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="bi bi-person-fill"></i>
            </div>
            <h1 id="profileTitle">个人设置</h1>
            <p id="profileSubtitle">管理您的账户信息和偏好设置</p>
        </div>

        <div class="profile-sections">
            <!-- 基本信息 -->
            <div class="profile-section">
                <h2 class="section-title">
                    <i class="bi bi-person-gear"></i>
                    基本信息
                </h2>
                <form id="profileForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" disabled>
                    </div>
                    <div class="form-group">
                        <label for="displayName">显示名称</label>
                        <input type="text" id="displayName" placeholder="输入您的显示名称" required>
                    </div>
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" placeholder="输入您的邮箱地址">
                    </div>
                    <div class="form-group">
                        <label for="role">账户角色</label>
                        <input type="text" id="role" disabled>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-github btn-primary">
                            <i class="bi bi-check-circle"></i>
                            保存更改
                        </button>
                        <button type="button" class="btn-github btn-secondary" onclick="loadProfile()">
                            <i class="bi bi-arrow-clockwise"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>

            <!-- 密码修改 -->
            <div class="profile-section">
                <h2 class="section-title">
                    <i class="bi bi-shield-lock"></i>
                    密码安全
                </h2>
                <form id="passwordForm">
                    <div class="form-group">
                        <label for="currentPassword">当前密码</label>
                        <input type="password" id="currentPassword" placeholder="输入当前密码" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码</label>
                        <input type="password" id="newPassword" placeholder="输入新密码（至少6位）" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码</label>
                        <input type="password" id="confirmPassword" placeholder="再次输入新密码" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-github btn-primary">
                            <i class="bi bi-key"></i>
                            修改密码
                        </button>
                    </div>
                </form>
            </div>

            <!-- 危险操作 -->
            <div class="profile-section danger-zone">
                <h2 class="section-title">
                    <i class="bi bi-exclamation-triangle"></i>
                    危险操作
                </h2>
                <div class="warning-text">
                    <strong>警告：</strong>删除账户是不可逆的操作，将永久删除您的所有数据。管理员账户无法删除。
                </div>
                <form id="deleteForm">
                    <div class="form-group">
                        <label for="deletePassword">确认密码</label>
                        <input type="password" id="deletePassword" placeholder="输入您的密码以确认删除" required>
                    </div>
                    <div class="form-group">
                        <label for="deleteConfirmation">确认文本</label>
                        <input type="text" id="deleteConfirmation" placeholder="请输入：删除我的账户" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-github btn-danger">
                            <i class="bi bi-trash"></i>
                            删除账户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 检查认证状态
        function checkAuth() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                window.location.href = '/login';
                return false;
            }
            return true;
        }

        // 加载用户信息
        async function loadProfile() {
            if (!checkAuth()) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    const user = data.user;
                    document.getElementById('username').value = user.username;
                    document.getElementById('displayName').value = user.display_name || '';
                    document.getElementById('email').value = user.email || '';
                    document.getElementById('role').value = user.role === 'admin' ? '管理员' : '普通用户';
                    
                    // 更新页面标题
                    document.getElementById('profileTitle').textContent = `${user.display_name || user.username} 的个人设置`;
                } else {
                    alert('加载用户信息失败: ' + data.message);
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
                alert('加载用户信息失败，请刷新页面重试');
            }
        }

        // 更新个人信息
        document.getElementById('profileForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const displayName = document.getElementById('displayName').value.trim();
            const email = document.getElementById('email').value.trim();
            
            if (!displayName) {
                alert('显示名称不能为空');
                return;
            }
            
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        display_name: displayName,
                        email: email
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('个人信息更新成功！');
                    loadProfile(); // 重新加载信息
                } else {
                    alert('更新失败: ' + data.message);
                }
            } catch (error) {
                console.error('更新个人信息失败:', error);
                alert('更新失败，请重试');
            }
        });

        // 修改密码
        document.getElementById('passwordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                alert('新密码确认不匹配');
                return;
            }
            
            if (newPassword.length < 6) {
                alert('新密码长度至少6位');
                return;
            }
            
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/profile/password', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword,
                        confirm_password: confirmPassword
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('密码修改成功！');
                    document.getElementById('passwordForm').reset();
                } else {
                    alert('密码修改失败: ' + data.message);
                }
            } catch (error) {
                console.error('密码修改失败:', error);
                alert('密码修改失败，请重试');
            }
        });

        // 删除账户
        document.getElementById('deleteForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const password = document.getElementById('deletePassword').value;
            const confirmation = document.getElementById('deleteConfirmation').value;
            
            if (confirmation !== '删除我的账户') {
                alert('确认文本不正确，请输入：删除我的账户');
                return;
            }
            
            if (!confirm('您确定要删除账户吗？此操作不可撤销！')) {
                return;
            }
            
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/profile/delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        password: password,
                        confirmation: confirmation
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('账户删除成功');
                    localStorage.removeItem('auth_token');
                    window.location.href = '/login';
                } else {
                    alert('账户删除失败: ' + data.message);
                }
            } catch (error) {
                console.error('账户删除失败:', error);
                alert('账户删除失败，请重试');
            }
        });

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProfile();
        });
    </script>
</body>
</html>
