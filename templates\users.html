<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 导航栏样式 */
        .navbar-brand-github {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            text-decoration: none;
            color: var(--color-fg-default);
            font-weight: 600;
            font-size: 16px;
        }

        .douyin-logo {
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--color-canvas-overlay);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        .time-display {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 14px;
        }

        .search-bar {
            display: flex;
            gap: var(--base-size-12);
            margin-bottom: var(--base-size-24);
            align-items: center;
        }

        .search-input {
            flex: 1;
            max-width: 400px;
        }

        .user-card {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-20);
            margin-bottom: var(--base-size-16);
        }

        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--base-size-16);
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--base-size-4);
        }

        .user-username {
            color: var(--color-fg-muted);
            font-family: 'Courier New', monospace;
            margin-bottom: var(--base-size-8);
        }

        .user-badges {
            display: flex;
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-8);
        }

        .badge {
            padding: 2px 8px;
            border-radius: var(--border-radius-small);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-admin {
            background: var(--color-danger-subtle);
            color: var(--color-danger-fg);
        }

        .badge-user {
            background: var(--color-neutral-subtle);
            color: var(--color-fg-muted);
        }

        .badge-member {
            background: var(--color-success-subtle);
            color: var(--color-success-fg);
        }

        .badge-expired {
            background: var(--color-attention-subtle);
            color: var(--color-attention-fg);
        }

        .user-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--base-size-12);
            font-size: 0.875rem;
            color: var(--color-fg-muted);
            margin-bottom: var(--base-size-16);
        }

        .user-actions {
            display: flex;
            gap: var(--base-size-8);
            flex-wrap: wrap;
        }

        .member-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .member-modal-content {
            background: var(--color-canvas-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-24);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .member-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--base-size-20);
            padding-bottom: var(--base-size-12);
            border-bottom: 1px solid var(--color-border-default);
        }

        .member-form {
            display: flex;
            flex-direction: column;
            gap: var(--base-size-16);
        }

        .days-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-12);
        }

        .days-preset {
            padding: var(--base-size-8);
            border: 1px solid var(--color-border-default);
            background: var(--color-canvas-subtle);
            border-radius: var(--border-radius-small);
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }

        .days-preset:hover {
            background: var(--color-canvas-default);
            border-color: var(--color-accent-fg);
        }

        .days-preset.active {
            background: var(--color-accent-subtle);
            border-color: var(--color-accent-fg);
            color: var(--color-accent-fg);
        }

        .empty-state {
            text-align: center;
            padding: var(--base-size-40);
            color: var(--color-fg-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: var(--base-size-16);
            opacity: 0.5;
        }

        .loading {
            text-align: center;
            padding: var(--base-size-40);
            color: var(--color-fg-muted);
        }

        .stats-summary {
            background: var(--color-canvas-subtle);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-16);
            margin-bottom: var(--base-size-24);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--base-size-16);
            text-align: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            gap: var(--base-size-4);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .stat-label {
            color: var(--color-fg-muted);
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="github-container">
        <!-- GitHub风格导航栏 -->
        <nav class="github-navbar">
            <div class="github-container">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <a class="navbar-brand-github" href="/">
                        <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                        <span>AI机器人自动化直播控制台</span>
                    </a>

                    <!-- 导航链接 -->
                    <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                        <a href="/" class="nav-link">
                            <i class="bi bi-house-fill"></i>
                            <span>首页</span>
                        </a>
                        <a href="/leaderboard" class="nav-link">
                            <i class="bi bi-trophy-fill"></i>
                            <span>直播间排行榜</span>
                        </a>

                        <!-- 公告按钮将通过JavaScript动态添加 -->

                        <!-- 管理员专用链接 -->
                        <a href="/users" class="nav-link active admin-only">
                            <i class="bi bi-people-fill"></i>
                            <span>用户管理</span>
                        </a>
                        <a href="/invitation-codes" class="nav-link admin-only">
                            <i class="bi bi-key-fill"></i>
                            <span>会员码</span>
                        </a>
                        <a href="/announcements" class="nav-link admin-only">
                            <i class="bi bi-megaphone-fill"></i>
                            <span>公告管理</span>
                        </a>
                    </div>

                    <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                        <div class="time-display">
                            <i class="bi bi-clock"></i>
                            <span id="currentTime"></span>
                        </div>

                        <!-- 个人中心下拉菜单 -->
                        <div class="dropdown" id="userDropdown">
                            <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                                <i class="bi bi-person-circle"></i>
                                <span id="currentUsername">用户</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="userDropdownMenu">
                                <a href="/profile" class="dropdown-item">
                                    <i class="bi bi-person-gear"></i>
                                    个人设置
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                        <span id="userDisplayName">加载中...</span>
                        <button onclick="logout()" class="btn-github btn-sm">
                            <i class="bi bi-box-arrow-right"></i>
                            退出
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="github-main">
            <div class="github-header">
                <h1>
                    <i class="bi bi-people-fill"></i>
                    用户管理
                </h1>
                <p>管理系统用户和会员权限</p>
            </div>

            <!-- 统计摘要 -->
            <div class="stats-summary">
                <div class="stat-item">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="activeMembers">0</div>
                    <div class="stat-label">活跃会员</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="expiredMembers">0</div>
                    <div class="stat-label">过期会员</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="regularUsers">0</div>
                    <div class="stat-label">普通用户</div>
                </div>
            </div>

            <!-- 搜索栏 -->
            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索用户名、昵称或邮箱...">
                <button id="searchBtn" class="btn-github btn-primary">
                    <i class="bi bi-search"></i>
                    搜索
                </button>
                <button id="refreshBtn" class="btn-github">
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                </button>
            </div>

            <!-- 用户列表 -->
            <div id="usersList">
                <div class="loading">
                    <i class="bi bi-arrow-clockwise" style="animation: spin 1s linear infinite;"></i>
                    <p>正在加载用户列表...</p>
                </div>
            </div>
        </main>
    </div>

    <!-- 会员管理模态框 -->
    <div id="memberModal" class="member-modal">
        <div class="member-modal-content">
            <div class="member-modal-header">
                <h3 id="memberModalTitle">会员管理</h3>
                <button onclick="closeMemberModal()" class="btn-github btn-sm">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="member-form">
                <div>
                    <label>用户信息</label>
                    <div id="memberUserInfo" style="padding: 12px; background: var(--color-canvas-subtle); border-radius: 6px; margin-bottom: 16px;">
                        <!-- 用户信息将在这里显示 -->
                    </div>
                </div>
                
                <div>
                    <label>当前会员状态</label>
                    <div id="memberCurrentStatus" style="padding: 12px; background: var(--color-canvas-subtle); border-radius: 6px; margin-bottom: 16px;">
                        <!-- 当前状态将在这里显示 -->
                    </div>
                </div>

                <div>
                    <label>开通会员天数</label>
                    <div class="days-presets">
                        <div class="days-preset" data-days="7">7天</div>
                        <div class="days-preset" data-days="30">30天</div>
                        <div class="days-preset" data-days="90">90天</div>
                        <div class="days-preset" data-days="180">180天</div>
                        <div class="days-preset" data-days="365">365天</div>
                    </div>
                    <input type="number" id="memberDays" min="1" max="3650" placeholder="自定义天数" style="width: 100%;">
                </div>

                <div style="display: flex; gap: 12px;">
                    <button id="activateMemberBtn" class="btn-github btn-primary" style="flex: 1;">
                        <i class="bi bi-star"></i>
                        开通会员
                    </button>
                    <button id="deactivateMemberBtn" class="btn-github btn-danger">
                        <i class="bi bi-star"></i>
                        取消会员
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/auth.js"></script>
    <script>
        let allUsers = [];
        let currentUser = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查认证和管理员权限
            if (!checkAuth()) {
                window.location.href = '/login';
                return;
            }

            checkAdminPermission();
            loadUserInfo();
            loadUsers();
            
            // 绑定事件
            document.getElementById('searchBtn').addEventListener('click', handleSearch);
            document.getElementById('refreshBtn').addEventListener('click', loadUsers);
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });

            // 会员管理事件
            document.getElementById('activateMemberBtn').addEventListener('click', activateMember);
            document.getElementById('deactivateMemberBtn').addEventListener('click', deactivateMember);

            // 天数预设选择
            document.querySelectorAll('.days-preset').forEach(preset => {
                preset.addEventListener('click', function() {
                    document.querySelectorAll('.days-preset').forEach(p => p.classList.remove('active'));
                    this.classList.add('active');
                    document.getElementById('memberDays').value = this.dataset.days;
                });
            });
        });

        // 检查管理员权限
        async function checkAdminPermission() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (!data.success || data.user.role !== 'admin') {
                        alert('您没有访问此页面的权限，需要管理员权限');
                        window.location.href = '/';
                        return;
                    }
                } else {
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('权限检查失败:', error);
                window.location.href = '/login';
            }
        }

        // 加载用户信息
        async function loadUserInfo() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/user/info', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user) {
                        document.getElementById('userDisplayName').textContent = data.user.display_name || data.user.username;
                    }
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                console.log('开始加载用户列表...');
                const token = localStorage.getItem('auth_token');
                
                if (!token) {
                    console.error('未找到认证令牌');
                    showError('未登录，请先登录');
                    window.location.href = '/login';
                    return;
                }
                
                console.log('发送API请求到 /api/users');
                const response = await fetch('/api/users', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                console.log('API响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应数据:', data);
                    
                    if (data.success) {
                        allUsers = data.users;
                        console.log(`成功加载 ${allUsers.length} 个用户`);
                        updateStats();
                        renderUsers(allUsers);
                    } else {
                        console.error('API返回失败:', data.message);
                        showError('加载用户列表失败: ' + data.message);
                    }
                } else {
                    console.error('HTTP错误:', response.status, response.statusText);
                    if (response.status === 401) {
                        showError('认证失败，请重新登录');
                        localStorage.removeItem('auth_token');
                        window.location.href = '/login';
                    } else if (response.status === 403) {
                        showError('权限不足，需要管理员权限');
                    } else {
                        showError('加载用户列表失败: HTTP ' + response.status);
                    }
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 搜索用户
        async function handleSearch() {
            const query = document.getElementById('searchInput').value.trim();
            
            if (!query) {
                renderUsers(allUsers);
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        renderUsers(data.users);
                    } else {
                        showError('搜索失败: ' + data.message);
                    }
                } else {
                    showError('搜索失败');
                }
            } catch (error) {
                console.error('搜索用户失败:', error);
                showError('网络错误');
            }
        }

        // 更新统计信息
        function updateStats() {
            const total = allUsers.length;
            const activeMembers = allUsers.filter(user => user.member_status === 'active').length;
            const expiredMembers = allUsers.filter(user => user.member_status === 'expired').length;
            const regularUsers = allUsers.filter(user => user.member_status === 'none').length;

            document.getElementById('totalUsers').textContent = total;
            document.getElementById('activeMembers').textContent = activeMembers;
            document.getElementById('expiredMembers').textContent = expiredMembers;
            document.getElementById('regularUsers').textContent = regularUsers;
        }

        // 渲染用户列表
        function renderUsers(users) {
            const container = document.getElementById('usersList');

            if (users.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-people"></i>
                        <p>没有找到用户</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = users.map(user => `
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-info">
                            <div class="user-name">${user.display_name}</div>
                            <div class="user-username">@${user.username}</div>
                            <div class="user-badges">
                                <span class="badge badge-${user.role}">${getRoleText(user.role)}</span>
                                ${getMemberBadge(user)}
                            </div>
                        </div>
                    </div>
                    <div class="user-details">
                        <div><strong>邮箱:</strong> ${user.email || '未设置'}</div>
                        <div><strong>注册时间:</strong> ${formatDate(user.created_at)}</div>
                        <div><strong>最后登录:</strong> ${user.last_login ? formatDate(user.last_login) : '从未登录'}</div>
                        <div><strong>邀请码:</strong> ${user.invitation_code || '未知'}</div>
                        ${getMemberInfo(user)}
                    </div>
                    <div class="user-actions">
                        <button class="btn-github btn-sm" onclick="openMemberModal('${user.username}')">
                            <i class="bi bi-star"></i>
                            会员管理
                        </button>
                        <button class="btn-github btn-sm" onclick="resetUserPassword('${user.username}')">
                            <i class="bi bi-key"></i>
                            重置密码
                        </button>
                        <button class="btn-github btn-sm ${user.enabled ? 'btn-danger' : 'btn-success'}" 
                                onclick="toggleUserStatus('${user.username}', ${!user.enabled})">
                            <i class="bi bi-${user.enabled ? 'person-x' : 'person-check'}"></i>
                            ${user.enabled ? '禁用' : '启用'}
                        </button>
                        ${user.role !== 'admin' ? `
                        <button class="btn-github btn-sm btn-danger" 
                                onclick="deleteUser('${user.username}', '${user.display_name}')">
                            <i class="bi bi-trash"></i>
                            删除用户
                        </button>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 获取角色文本
        function getRoleText(role) {
            const roleMap = {
                'admin': '管理员',
                'user': '用户'
            };
            return roleMap[role] || role;
        }

        // 获取会员徽章
        function getMemberBadge(user) {
            if (user.member_status === 'active') {
                return '<span class="badge badge-member">会员</span>';
            } else if (user.member_status === 'expired') {
                return '<span class="badge badge-expired">会员已过期</span>';
            }
            return '';
        }

        // 获取会员信息
        function getMemberInfo(user) {
            if (user.member_status === 'active') {
                return `<div><strong>会员到期:</strong> ${formatDate(user.member_expires_at)} (剩余${user.remaining_days}天)</div>`;
            } else if (user.member_status === 'expired') {
                return `<div><strong>会员已过期:</strong> ${formatDate(user.member_expires_at)}</div>`;
            }
            return '<div><strong>会员状态:</strong> 非会员</div>';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // 打开会员管理模态框
        async function openMemberModal(username) {
            currentUser = allUsers.find(user => user.username === username);
            if (!currentUser) return;

            document.getElementById('memberModalTitle').textContent = `会员管理 - ${currentUser.display_name}`;
            
            // 显示用户信息
            document.getElementById('memberUserInfo').innerHTML = `
                <div><strong>用户名:</strong> ${currentUser.username}</div>
                <div><strong>昵称:</strong> ${currentUser.display_name}</div>
                <div><strong>邮箱:</strong> ${currentUser.email || '未设置'}</div>
                <div><strong>注册时间:</strong> ${formatDate(currentUser.created_at)}</div>
            `;

            // 获取最新会员状态
            await loadMemberStatus(username);

            document.getElementById('memberModal').style.display = 'flex';
        }

        // 加载会员状态
        async function loadMemberStatus(username) {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${username}/member`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const status = data.member_status;
                        let statusHtml = '';
                        
                        if (status.is_member) {
                            if (status.expired) {
                                statusHtml = `<div style="color: var(--color-attention-fg);">会员已过期 (${status.expires_at ? formatDate(status.expires_at) : ''})</div>`;
                            } else {
                                statusHtml = `<div style="color: var(--color-success-fg);">会员有效 (${status.message})</div>`;
                            }
                        } else {
                            statusHtml = '<div style="color: var(--color-fg-muted);">非会员用户</div>';
                        }
                        
                        document.getElementById('memberCurrentStatus').innerHTML = statusHtml;
                    }
                }
            } catch (error) {
                console.error('加载会员状态失败:', error);
            }
        }

        // 关闭会员管理模态框
        function closeMemberModal() {
            document.getElementById('memberModal').style.display = 'none';
            currentUser = null;
            
            // 清空表单
            document.getElementById('memberDays').value = '';
            document.querySelectorAll('.days-preset').forEach(p => p.classList.remove('active'));
        }

        // 开通会员
        async function activateMember() {
            if (!currentUser) return;

            const days = parseInt(document.getElementById('memberDays').value);
            if (!days || days <= 0) {
                showError('请输入有效的天数');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${currentUser.username}/member`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ days })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message);
                    closeMemberModal();
                    loadUsers(); // 刷新用户列表
                } else {
                    showError('开通会员失败: ' + result.message);
                }
            } catch (error) {
                console.error('开通会员失败:', error);
                showError('网络错误');
            }
        }

        // 取消会员
        async function deactivateMember() {
            if (!currentUser) return;

            if (!confirm('确定要取消该用户的会员权限吗？')) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${currentUser.username}/member`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message);
                    closeMemberModal();
                    loadUsers(); // 刷新用户列表
                } else {
                    showError('取消会员失败: ' + result.message);
                }
            } catch (error) {
                console.error('取消会员失败:', error);
                showError('网络错误');
            }
        }

        // 重置用户密码
        async function resetUserPassword(username) {
            const newPassword = prompt('请输入新密码:');
            if (!newPassword) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${username}/password`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ new_password: newPassword })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess('密码重置成功');
                } else {
                    showError('密码重置失败: ' + result.message);
                }
            } catch (error) {
                console.error('重置密码失败:', error);
                showError('网络错误');
            }
        }

        // 切换用户状态
        async function toggleUserStatus(username, enabled) {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${username}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ enabled })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(enabled ? '用户已启用' : '用户已禁用');
                    loadUsers(); // 刷新用户列表
                } else {
                    showError('操作失败: ' + result.message);
                }
            } catch (error) {
                console.error('切换用户状态失败:', error);
                showError('网络错误');
            }
        }

        // 删除用户
        async function deleteUser(username, displayName) {
            if (!username) {
                showError('用户名不能为空');
                return;
            }

            // 双重确认删除操作
            if (!confirm(`确定要删除用户"${displayName}"(${username})吗？\n\n此操作将永久删除用户数据，无法恢复！`)) {
                return;
            }

            if (!confirm('请再次确认：您确定要永久删除这个用户吗？')) {
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/users/${username}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message || '用户删除成功');
                    loadUsers(); // 刷新用户列表
                } else {
                    showError('删除用户失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            alert(message);
        }

        // 显示错误消息
        function showError(message) {
            alert(message);
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }

        // 点击模态框外部关闭
        document.getElementById('memberModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMemberModal();
            }
        });

        // 导航栏相关函数
        function toggleUserDropdown() {
            const menu = document.getElementById('userDropdownMenu');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const menu = document.getElementById('userDropdownMenu');

            if (!dropdown.contains(event.target)) {
                menu.classList.remove('show');
            }
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即更新一次

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
    </script>

    <!-- 公告系统 -->
    <script src="/static/js/announcement-modal.js"></script>
    <script src="/static/js/announcement-button.js"></script>
</body>
</html>
