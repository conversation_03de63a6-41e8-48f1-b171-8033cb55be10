#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket客户端测试
用于测试WebSocket服务器连接
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_connection():
    """测试WebSocket连接"""
    uri = "ws://localhost:15000"
    
    try:
        print(f"🔗 尝试连接到: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功！")
            
            # 等待欢迎消息
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 收到欢迎消息: {welcome}")
                
                # 解析欢迎消息
                welcome_data = json.loads(welcome)
                if welcome_data.get('type') == 'connection_established':
                    print("✅ 连接确认成功")
                    client_id = welcome_data.get('data', {}).get('client_id')
                    server_info = welcome_data.get('data', {}).get('server')
                    print(f"📋 客户端ID: {client_id}")
                    print(f"🖥️ 服务器: {server_info}")
                
            except asyncio.TimeoutError:
                print("⚠️ 未收到欢迎消息（超时）")
            except json.JSONDecodeError:
                print("⚠️ 欢迎消息不是有效的JSON")
            
            # 发送测试消息
            test_message = {
                "type": "test_ping",
                "data": {
                    "message": "Hello from test client",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await websocket.send(json.dumps(test_message, ensure_ascii=False))
            print("📤 发送测试消息成功")
            
            # 等待一段时间看是否有响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"📨 收到响应: {response}")
            except asyncio.TimeoutError:
                print("⏰ 没有收到响应（这是正常的，服务器可能不回复测试消息）")
            
            print("✅ 测试完成，连接正常")
            
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保WebSocket服务器正在运行")
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的URI")
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ 连接被关闭: {e}")
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_device_registration():
    """测试ESP32设备注册"""
    uri = "ws://localhost:15000"
    
    try:
        print(f"\n🤖 测试ESP32设备注册...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ 连接成功")
            
            # 等待欢迎消息
            await asyncio.wait_for(websocket.recv(), timeout=3.0)
            
            # 发送设备注册消息
            register_message = {
                "type": "device_register",
                "data": {
                    "device_id": "TEST_ESP32_001",
                    "device_name": "测试ESP32设备",
                    "device_type": "esp32",
                    "capabilities": "voice,led,sensor"
                }
            }
            
            await websocket.send(json.dumps(register_message, ensure_ascii=False))
            print("📤 发送设备注册消息")
            
            # 等待注册响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 注册响应: {response}")
                
                response_data = json.loads(response)
                if response_data.get('type') == 'device_register_response':
                    if response_data.get('data', {}).get('success'):
                        print("✅ 设备注册成功")
                    else:
                        print(f"❌ 设备注册失败: {response_data.get('data', {}).get('message')}")
                
            except asyncio.TimeoutError:
                print("⏰ 未收到注册响应")
            
            # 发送设备状态更新
            status_message = {
                "type": "device_status",
                "data": {
                    "device_id": "TEST_ESP32_001",
                    "status": "idle",
                    "voice_detected": False,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await websocket.send(json.dumps(status_message, ensure_ascii=False))
            print("📤 发送设备状态更新")
            
            # 保持连接一段时间
            await asyncio.sleep(2)
            print("✅ 设备注册测试完成")
            
    except Exception as e:
        print(f"❌ 设备注册测试失败: {e}")

async def main():
    """主测试函数"""
    print("🔍 WebSocket服务器简单测试")
    print("=" * 50)
    
    # 基本连接测试
    await test_connection()
    
    # 设备注册测试
    await test_device_registration()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")
    print("如果看到连接成功的消息，说明WebSocket服务器工作正常")
    print("如果连接失败，请检查:")
    print("1. WebSocket服务器是否正在运行 (python test_websocket_simple.py)")
    print("2. 端口15000是否被占用")
    print("3. 防火墙设置")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()
