/**
 * 公告按钮组件
 * 用于在导航栏中添加公告按钮和未读徽章
 */

class AnnouncementButton {
    constructor() {
        this.button = null;
        this.isInitialized = false;
    }

    // 初始化公告按钮
    init(containerSelector = '.navbar-nav') {
        if (this.isInitialized) return;

        try {
            // 查找导航栏容器
            const container = document.querySelector(containerSelector);
            if (!container) {
                console.warn('未找到导航栏容器:', containerSelector);
                return;
            }

            // 创建公告按钮
            this.createButton(container);
            
            // 初始化公告模态窗口
            if (window.announcementModal) {
                window.announcementModal.init();
            }

            this.isInitialized = true;
            console.log('✅ 公告按钮初始化完成');
        } catch (error) {
            console.error('❌ 公告按钮初始化失败:', error);
        }
    }

    // 创建公告按钮
    createButton(container) {
        // 创建按钮HTML
        const buttonHTML = `
            <div class="announcement-badge">
                <a href="#" class="nav-link" onclick="announcementButton.showAnnouncements(event)">
                    <i class="bi bi-megaphone-fill"></i>
                    <span>公告</span>
                </a>
                <span class="badge hidden">0</span>
            </div>
        `;

        // 查找合适的插入位置（在管理员链接之前）
        const adminLinks = container.querySelector('.admin-only');
        if (adminLinks) {
            // 在管理员链接之前插入
            adminLinks.insertAdjacentHTML('beforebegin', buttonHTML);
        } else {
            // 在容器末尾插入
            container.insertAdjacentHTML('beforeend', buttonHTML);
        }

        this.button = container.querySelector('.announcement-badge');
    }

    // 显示公告
    showAnnouncements(event) {
        if (event) {
            event.preventDefault();
        }

        if (window.announcementModal) {
            window.announcementModal.show();
        }
    }

    // 检查并自动弹出公告
    async checkAutoPopup() {
        try {
            // 检查是否是首次访问主页
            const currentPath = window.location.pathname;
            const isHomePage = currentPath === '/' || currentPath === '/index.html';
            
            if (!isHomePage) return;

            // 检查是否已经显示过公告（使用sessionStorage，每次会话只显示一次）
            const hasShownAnnouncements = sessionStorage.getItem('announcements_shown');
            if (hasShownAnnouncements) return;

            // 检查是否有未读公告
            const token = localStorage.getItem('auth_token');
            if (!token) return;

            const response = await fetch('/api/announcements/unread-count', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.unread_count > 0) {
                    // 延迟1秒后自动弹出公告
                    setTimeout(() => {
                        this.showAnnouncements();
                        sessionStorage.setItem('announcements_shown', 'true');
                    }, 1000);
                }
            }
        } catch (error) {
            console.error('检查自动弹出失败:', error);
        }
    }

    // 更新未读徽章
    updateBadge(count) {
        if (!this.button) return;

        const badge = this.button.querySelector('.badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }
    }
}

// 创建全局实例
window.announcementButton = new AnnouncementButton();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有认证token
    const token = localStorage.getItem('auth_token');
    if (token) {
        // 初始化公告按钮
        window.announcementButton.init();
        
        // 检查自动弹出
        setTimeout(() => {
            window.announcementButton.checkAutoPopup();
        }, 2000);
    }
});

// 导出到全局作用域，方便在HTML中调用
window.showAnnouncements = function(event) {
    window.announcementButton.showAnnouncements(event);
};
