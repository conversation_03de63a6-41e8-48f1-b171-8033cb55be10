# 🤖 抖音直播聊天机器人系统

一个功能完整的抖音直播聊天机器人系统，支持多直播间管理、ESP32设备语音播报、智能交互、用户管理等功能。

## ✨ 核心功能特性

### 🎯 **智能消息处理系统**
- **实时消息监听**：监听抖音直播间的聊天、进入、点赞、关注、礼物等消息
- **智能队列管理**：10条消息队列，FIFO处理机制，确保消息按序处理
- **新用户优先**：首次发言用户享有高优先级权重(15)，鼓励新用户参与
- **消息权重系统**：
  - 系统插播(999) > 游戏指令(500/200) > 礼物回复(100-15) 
  - 新用户(15) > 优先用户(12) > 普通用户(10) > 防冷场(0)

### 🔊 **设备通信系统**
- **ESP32语音播报**：通过WebSocket与ESP32设备通信，实现语音播报
- **设备状态管理**：实时监控设备状态(idle/listening/speaking)，避免消息冲突
- **防卡死机制**：统一队列系统，消除并发发送导致的设备卡死问题
- **状态监控线程**：1秒间隔监控，设备空闲时自动处理队列消息

### 🤖 **智能交互功能**
- **防刷屏系统**：频率检测+重复内容检测，智能警告和屏蔽机制
- **防冷场机制**：自动检测直播间冷场，主动发起话题互动
- **定时插播**：可配置间隔的定时插播消息，保持直播间活跃
- **互动游戏**：猜数字、成语接龙、谜语等多种互动游戏
- **礼物优先权**：礼物用户获得消息优先处理权限

### 📊 **数据统计系统**
- **用户互动统计**：实时记录用户聊天、点赞、关注、礼物等互动数据
- **排行榜系统**：动态排行榜，展示活跃用户和贡献排名
- **数据持久化**：用户数据、房间统计、设备状态等数据持久化存储
- **内存管理**：spoken_users集合定期清理，防止内存泄漏

### 🌐 **Web管理系统**
- **多直播间管理**：支持同时管理多个直播间，独立配置
- **设备管理**：设备注册、状态监控、配置管理
- **用户管理**：用户权限管理、黑名单管理
- **实时监控**：消息队列状态、设备状态、系统运行状态

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理界面    │    │  WebSocket服务器  │    │   ESP32设备群    │
│   (端口15008)   │    │   (端口15000)    │    │  (语音播报设备)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    直播聊天机器人核心系统                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ 消息监听模块  │  │ 队列管理模块  │  │ 设备通信模块  │  │ 数据统计模块  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ 智能交互模块  │  │ 配置管理模块  │  │ 用户管理模块  │  │ 日志监控模块  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   抖音直播间1    │    │   抖音直播间2    │    │   抖音直播间N    │
│   (消息源)      │    │   (消息源)      │    │   (消息源)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
live_chat_bot/
├── 📋 核心程序
│   ├── live_chat_bot.py          # 主程序 - 聊天机器人核心逻辑
│   ├── liveMan.py               # 抖音直播间消息抓取核心
│   ├── flask_web_server.py       # Web管理界面服务器
│   ├── test_websocket_simple.py  # WebSocket服务器
│   ├── leaderboard_stats.py      # 排行榜统计模块
│   ├── shared_data.py           # 共享数据模块
│   ├── user_management.py       # 用户管理模块
│   └── start_system.py         # 系统启动脚本
├── 🔧 工具脚本
│   ├── manage_processes.py      # 进程管理工具
│   ├── config_manager.py        # 配置管理工具
│   └── migrate_config.py        # 配置迁移脚本
├── ⚙️ 配置系统
│   ├── config/                  # 配置目录
│   │   ├── README.md           # 配置说明文档
│   │   ├── global_config.json   # 全局默认配置
│   │   ├── rooms/              # 房间配置目录
│   │   │   ├── 337356796001.json # 具体房间配置
│   │   │   ├── 211953907443.json # 硅灵造物直播间
│   │   │   └── ...             # 其他房间配置
│   │   └── devices/            # 设备配置目录
│   │       └── devices.json    # 设备注册信息
│   └── requirements.txt        # Python依赖包
├── 📊 数据系统
│   └── data/                   # 数据目录
│       ├── runtime/            # 运行时数据
│       │   ├── runtime_data.json # 系统运行时状态
│       │   └── users.json      # 用户管理数据
│       ├── stats/             # 统计数据
│       │   └── leaderboard_data.json # 排行榜数据
│       └── logs/              # 日志文件目录
├── 🌐 Web界面
│   └── templates/             # Web模板文件
│       ├── index.html         # 主页 - 系统概览
│       ├── users.html         # 用户管理页面
│       ├── invitation-codes.html # 邀请码管理页面
│       ├── leaderboard.html   # 排行榜页面
│       ├── profile.html       # 个人设置页面
│       └── login.html         # 登录页面
├── 📡 协议支持
│   └── protobuf/              # Protobuf相关文件
│       ├── douyin.proto       # 抖音协议定义
│       ├── douyin.py         # 生成的Python代码
│       └── protoc.exe        # 协议编译器
└── 🗂️ 备份数据
    └── backup_YYYYMMDD_HHMMSS/ # 自动备份目录
        ├── rooms_config.json   # 备份的配置文件
        ├── devices_config.json
        └── ...
```

## 🚀 快速启动

### 环境要求
- Python 3.8+
- Windows/Linux/macOS
- ESP32设备（可选，用于语音播报）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd live_chat_bot
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置系统**
```bash
# 运行配置管理工具查看当前配置
python config_manager.py

# 如果需要迁移旧配置
python migrate_config.py
```

4. **启动系统**

**方法1: 一键启动（推荐）**
```bash
python start_system.py
```

**方法2: 分步启动**
```bash
# 1. 启动WebSocket服务器（端口15000）
python test_websocket_simple.py

# 2. 启动Web管理界面（端口15008）
python flask_web_server.py

# 3. 启动特定直播间机器人
python live_chat_bot.py config/rooms/337356796001.json
```

5. **访问Web界面**
```
http://localhost:15008
```

## ⚙️ 配置说明

### 配置文件层次结构
```
配置优先级: 房间配置 > 全局配置 > 代码默认值
```

### 全局配置 (config/global_config.json)
```json
{
  "websocket_settings": {
    "url": "ws://localhost:15000",
    "timeout_seconds": 10
  },
  "message_queue": {
    "enabled": true,
    "max_size": 10,
    "max_age_seconds": 60
  },
  "anti_spam": {
    "enabled": true,
    "frequency_limit": {
      "time_window_seconds": 30,
      "max_messages": 5
    }
  }
}
```

### 房间配置 (config/rooms/{live_id}.json)
```json
{
  "live_settings": {
    "live_id": "337356796001"
  },
  "esp32_settings": {
    "device_id": "34:cd:b0:b3:df:28"
  },
  "room_info": {
    "room_name": "东教兽号",
    "enabled": true
  },
  "welcome_message": {
    "enabled": true,
    "cooldown_seconds": 10,
    "templates": [
      "欢迎{user_name}进入直播间！"
    ]
  },
  "anti_silence": {
    "enabled": true,
    "delay_seconds": 1,
    "cooldown_seconds": 60,
    "templates": [
      "现在直播间有点冷场，请主动和大家打个招呼"
    ]
  }
}
```

## 🎮 功能使用指南

### 消息队列管理
- **队列大小**: 10条消息，超出时智能替换低优先级消息
- **处理机制**: FIFO先进先出，保证消息公平处理
- **优先级**: 新用户首次发言享有高优先级

### 设备状态管理
- **自动监控**: 1秒间隔检查设备状态
- **状态类型**: idle(空闲) / listening(监听) / speaking(播报中)
- **智能发送**: 只在设备空闲时发送消息，避免冲突

### 防刷屏系统
- **频率检测**: 30秒内超过5条消息触发警告
- **重复检测**: 60秒内超过3条相似消息(相似度≥0.8)触发警告
- **自动屏蔽**: 警告后继续刷屏将被临时屏蔽5分钟

### 互动游戏
- **猜数字**: 1-100范围内的数字竞猜
- **成语接龙**: 智能成语接龙游戏
- **谜语竞猜**: 多类别谜语问答

## 🔧 高级配置

### 消息权重自定义
```json
{
  "message_weights": {
    "system_broadcast": 999,
    "game_start": 500,
    "game_timeout": 200,
    "gift_carnival": 100,
    "gift_rocket": 50,
    "gift_car": 20,
    "gift_basic": 15,
    "new_user": 15,
    "priority_user": 12,
    "normal_user": 10,
    "welcome": 1,
    "anti_silence": 0
  }
}
```

### 设备通信配置
```json
{
  "websocket_settings": {
    "url": "ws://localhost:15000",
    "timeout_seconds": 10,
    "retry_attempts": 3,
    "heartbeat_interval": 30
  }
}
```

### 日志配置
```json
{
  "logging": {
    "level": "INFO",
    "filter_http_requests": true,
    "filter_heartbeat": true,
    "merge_duplicate_logs": true,
    "show_timestamps": true,
    "highlight_important": true
  }
}
```

## 🛠️ 故障排除

### 常见问题

**Q: 设备连接失败**
```
A: 检查WebSocket服务器是否启动(端口15000)
   检查设备ID配置是否正确
   确认设备与服务器网络连通性
```

**Q: 消息不回复**
```
A: 检查直播间ID是否正确
   确认房间配置中reply_settings.enabled=true
   查看设备状态是否正常
```

**Q: 队列积压严重**
```
A: 检查设备状态是否卡在speaking状态
   适当调整message_queue.max_size大小
   检查消息处理速度是否正常
```

**Q: 配置文件错误**
```
A: 运行python config_manager.py检查配置
   使用备份文件恢复配置
   参考config/README.md配置说明
```

### 调试模式
```bash
# 启用详细日志
python live_chat_bot.py --debug

# 检查配置文件
python config_manager.py

# 测试WebSocket连接
python test_websocket_simple.py --test
```

### 性能监控
- **内存使用**: spoken_users集合每小时自动清理
- **队列状态**: Web界面实时显示队列长度和处理速度
- **设备状态**: 实时监控设备连接状态和响应时间

## 📈 更新日志

### v2.0.0 (2025-01-28)
- ✨ 新增新用户优先机制，首次发言享有高优先级
- 🔧 优化消息队列为FIFO机制，确保消息公平处理
- 🛡️ 完善设备状态管理，消除并发发送导致的卡死问题
- 📁 重构配置文件结构，支持分层配置管理
- 🧹 添加内存管理机制，防止spoken_users集合内存泄漏
- 🔄 统一消息发送机制，所有消息类型使用队列处理

### v1.5.0
- 🎮 新增互动游戏系统
- 📊 完善数据统计和排行榜功能
- 🚫 增强防刷屏检测机制
- 🌐 优化Web管理界面

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**🎉 感谢使用抖音直播聊天机器人系统！**
