
// ESP32 WebSocket客户端示例代码
#include <WiFi.h>
#include <ArduinoWebsockets.h>

const char* ssid = "your_wifi_ssid";
const char* password = "your_wifi_password";

// 重要：使用 ws:// 而不是 wss://
const char* websocket_server = "ws://*************:15000/";

using namespace websockets;
WebsocketsClient client;

void setup() {
    Serial.begin(115200);
    
    // 连接WiFi
    WiFi.begin(ssid, password);
    while (WiFi.status() != WL_CONNECTED) {
        delay(1000);
        Serial.println("连接WiFi中...");
    }
    Serial.println("WiFi连接成功");
    Serial.print("IP地址: ");
    Serial.println(WiFi.localIP());
    
    // 设置WebSocket回调
    client.onMessage(onMessageCallback);
    client.onEvent(onEventsCallback);
    
    // 连接WebSocket服务器
    connectToWebSocket();
}

void loop() {
    client.poll();
    delay(100);
}

void connectToWebSocket() {
    Serial.println("连接WebSocket服务器...");
    
    // 重要：不要使用SSL选项
    bool connected = client.connect(websocket_server);
    
    if (connected) {
        Serial.println("WebSocket连接成功!");
        
        // 发送设备注册消息
        String registerMsg = "{\"type\":\"device_register\",\"data\":{\"device_id\":\"ai_xiaozhi_001\",\"device_name\":\"AI小智\",\"device_type\":\"esp32\",\"capabilities\":\"voice,led\"}}";
        client.send(registerMsg);
        
    } else {
        Serial.println("WebSocket连接失败!");
        delay(5000);
        connectToWebSocket(); // 重试连接
    }
}

void onMessageCallback(WebsocketsMessage message) {
    Serial.print("收到消息: ");
    Serial.println(message.data());
}

void onEventsCallback(WebsocketsEvent event, String data) {
    if (event == WebsocketsEvent::ConnectionOpened) {
        Serial.println("WebSocket连接已建立");
    } else if (event == WebsocketsEvent::ConnectionClosed) {
        Serial.println("WebSocket连接已关闭");
        delay(5000);
        connectToWebSocket(); // 重新连接
    } else if (event == WebsocketsEvent::GotPing) {
        Serial.println("收到Ping");
    } else if (event == WebsocketsEvent::GotPong) {
        Serial.println("收到Pong");
    }
}
