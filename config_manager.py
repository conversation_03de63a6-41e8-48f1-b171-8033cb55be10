#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理工具
用于管理房间配置、设备配置和全局配置
"""

import json
import os
from typing import Dict, Any, Optional
from datetime import datetime

class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.global_config_path = os.path.join(config_dir, "global_config.json")
        self.rooms_dir = os.path.join(config_dir, "rooms")
        self.devices_dir = os.path.join(config_dir, "devices")
        
        # 确保目录存在
        os.makedirs(self.rooms_dir, exist_ok=True)
        os.makedirs(self.devices_dir, exist_ok=True)
    
    def load_global_config(self) -> Dict[str, Any]:
        """加载全局配置"""
        try:
            with open(self.global_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ 全局配置文件不存在: {self.global_config_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 全局配置文件格式错误: {e}")
            return {}
    
    def load_room_config(self, live_id: str) -> Optional[Dict[str, Any]]:
        """加载房间配置"""
        config_path = os.path.join(self.rooms_dir, f"{live_id}.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ 房间配置文件不存在: {config_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ 房间配置文件格式错误: {e}")
            return None
    
    def save_room_config(self, live_id: str, config: Dict[str, Any]) -> bool:
        """保存房间配置"""
        config_path = os.path.join(self.rooms_dir, f"{live_id}.json")
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 房间配置已保存: {config_path}")
            return True
        except Exception as e:
            print(f"❌ 保存房间配置失败: {e}")
            return False
    
    def load_devices_config(self) -> Dict[str, Any]:
        """加载设备配置"""
        devices_path = os.path.join(self.devices_dir, "devices.json")
        try:
            with open(devices_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ 设备配置文件不存在: {devices_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 设备配置文件格式错误: {e}")
            return {}
    
    def get_merged_config(self, live_id: str) -> Dict[str, Any]:
        """获取合并后的配置（房间配置 + 全局配置）"""
        global_config = self.load_global_config()
        room_config = self.load_room_config(live_id)
        
        if not room_config:
            print(f"❌ 无法加载房间 {live_id} 的配置")
            return global_config
        
        # 合并配置：房间配置优先
        merged_config = global_config.copy()
        merged_config.update(room_config)
        
        return merged_config
    
    def list_rooms(self) -> list:
        """列出所有房间配置"""
        rooms = []
        if not os.path.exists(self.rooms_dir):
            return rooms
        
        for filename in os.listdir(self.rooms_dir):
            if filename.endswith(".json"):
                live_id = filename[:-5]  # 移除 ".json" 后缀
                room_config = self.load_room_config(live_id)
                if room_config:
                    # 从不同的配置结构中提取信息
                    room_name = room_config.get('room_name',
                                              room_config.get('room_info', {}).get('room_name', '未知'))
                    enabled = room_config.get('enabled',
                                            room_config.get('room_info', {}).get('enabled', False))
                    target_device_id = room_config.get('target_device_id',
                                                     room_config.get('esp32_settings', {}).get('device_id', '未知'))

                    rooms.append({
                        'live_id': live_id,
                        'room_name': room_name,
                        'enabled': enabled,
                        'target_device_id': target_device_id
                    })
        
        return rooms
    
    def validate_room_config(self, config: Dict[str, Any]) -> list:
        """验证房间配置"""
        errors = []
        
        # 必需字段检查
        required_fields = ['live_id', 'target_device_id']
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        # 数据类型检查
        if 'enabled' in config and not isinstance(config['enabled'], bool):
            errors.append("enabled 字段必须是布尔值")
        
        # 设备ID格式检查
        if 'target_device_id' in config:
            device_id = config['target_device_id']
            if not isinstance(device_id, str) or len(device_id) != 17:
                errors.append("target_device_id 格式不正确（应为MAC地址格式）")
        
        return errors

def main():
    """主函数 - 配置管理工具"""
    manager = ConfigurationManager()
    
    print("🔧 配置管理工具")
    print("=" * 50)
    
    # 列出所有房间
    rooms = manager.list_rooms()
    print(f"\n📋 发现 {len(rooms)} 个房间配置:")
    
    for room in rooms:
        status = "✅ 启用" if room['enabled'] else "❌ 禁用"
        print(f"  - {room['live_id']}: {room['room_name']} ({status})")
        print(f"    设备: {room['target_device_id']}")
    
    # 加载设备配置
    devices = manager.load_devices_config()
    print(f"\n🔌 发现 {len(devices)} 个设备:")
    
    for device_id, device_info in devices.items():
        status = "✅ 启用" if device_info.get('enabled', False) else "❌ 禁用"
        print(f"  - {device_id}: {device_info.get('device_name', '未知')} ({status})")

if __name__ == "__main__":
    main()
