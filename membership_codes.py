#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员码管理模块（原邀请码系统重构）
"""

import os
import json
import threading
import string
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

class MembershipCodeManager:
    """会员码管理器"""
    
    def __init__(self, codes_file: str = "data/invitation_codes.json"):
        # 使用原有的邀请码文件来避免数据冲突
        self.codes_file = codes_file
        self.codes_data = {}
        self.lock = threading.Lock()
        self.load_codes()
    
    def load_codes(self):
        """加载会员码数据"""
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.codes_file), exist_ok=True)
            
            if os.path.exists(self.codes_file):
                with open(self.codes_file, 'r', encoding='utf-8') as f:
                    self.codes_data = json.load(f)
                
                # 为旧数据添加membership_days字段（迁移旧数据）
                need_save = False
                for code, code_info in self.codes_data.items():
                    if 'membership_days' not in code_info:
                        code_info['membership_days'] = code_info.get('valid_days', 7)
                        need_save = True
                
                if need_save:
                    self.save_codes()
                    print(f"已为 {len(self.codes_data)} 个邀请码添加会员天数字段")
                
                print(f"加载了 {len(self.codes_data)} 个会员码")
            else:
                self.codes_data = {}
                print("会员码文件不存在，创建新的会员码数据")
        except Exception as e:
            print(f"加载会员码数据失败: {e}")
            self.codes_data = {}
    
    def save_codes(self):
        """保存会员码数据"""
        try:
            with self.lock:
                # 确保数据目录存在
                os.makedirs(os.path.dirname(self.codes_file), exist_ok=True)

                # 写入临时文件，然后重命名（原子操作）
                temp_file = f"{self.codes_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self.codes_data, f, ensure_ascii=False, indent=2)

                # 原子替换
                if os.path.exists(self.codes_file):
                    os.remove(self.codes_file)
                os.rename(temp_file, self.codes_file)

                return True
        except Exception as e:
            print(f"保存会员码数据失败: {e}")
            return False

    def _save_codes_without_lock(self):
        """保存会员码数据（不获取锁）"""
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.codes_file), exist_ok=True)

            # 写入临时文件，然后重命名（原子操作）
            temp_file = f"{self.codes_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self.codes_data, f, ensure_ascii=False, indent=2)

            # 原子替换
            if os.path.exists(self.codes_file):
                os.remove(self.codes_file)
            os.rename(temp_file, self.codes_file)

            return True
        except Exception as e:
            print(f"保存会员码数据失败: {e}")
            return False
    
    def generate_code(self, length: int = 8) -> str:
        """生成会员码"""
        # 使用大写字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        
        attempts = 0
        while attempts < 1000:  # 防止无限循环
            code = ''.join(random.choice(chars) for _ in range(length))
            if code not in self.codes_data:
                return code
            attempts += 1

        # 如果1000次都没有生成唯一码，增加长度
        return self.generate_code(length + 1)
    
    def create_membership_code(self, created_by: str, valid_days: int = 7,
                             max_uses: int = 1, note: str = "") -> Dict:
        """创建会员码"""
        try:
            with self.lock:
                code = self.generate_code()

                # 计算过期时间
                expires_at = datetime.now() + timedelta(days=valid_days)
                code_info = {
                    'code': code,
                    'created_at': datetime.now().isoformat(),
                    'created_by': created_by,
                    'expires_at': expires_at.isoformat(),
                    'valid_days': valid_days,
                    'max_uses': max_uses,
                    'used_count': 0,
                    'used_by': [],
                    'status': 'active',  # active, expired, exhausted
                    'note': note,
                    'membership_days': valid_days  # 新增：会员天数
                }

                self.codes_data[code] = code_info

                # 直接保存，不再获取锁（因为已经在锁内）
                if self._save_codes_without_lock():
                    print(f"创建会员码成功: {code}")
                    return {'success': True, 'code': code, 'code_info': code_info}
                else:
                    return {'success': False, 'message': '保存会员码失败'}
                    
        except Exception as e:
            print(f"创建会员码异常: {e}")
            return {'success': False, 'message': str(e)}
    
    def validate_membership_code(self, code: str) -> Tuple[bool, str, Optional[Dict]]:
        """验证会员码"""
        try:
            code = code.strip().upper()
            
            if not code:
                return False, "会员码不能为空", None
            
            if code not in self.codes_data:
                return False, "会员码不存在", None
            
            code_info = self.codes_data[code]
            
            # 检查是否已过期
            expires_at = datetime.fromisoformat(code_info['expires_at'])
            if datetime.now() > expires_at:
                # 更新状态为过期
                code_info['status'] = 'expired'
                self.save_codes()
                return False, "会员码已过期", code_info
            
            # 检查使用次数
            if code_info['used_count'] >= code_info['max_uses']:
                # 更新状态为已用完
                code_info['status'] = 'exhausted'
                self.save_codes()
                return False, "会员码使用次数已达上限", code_info
            
            return True, "会员码有效", code_info
            
        except Exception as e:
            print(f"验证会员码异常: {e}")
            return False, f"验证失败: {str(e)}", None
    
    def use_membership_code(self, code: str, used_by: str) -> Tuple[bool, str, Optional[Dict]]:
        """使用会员码，返回(是否成功, 消息, 码信息)"""
        try:
            with self.lock:
                is_valid, message, code_info = self.validate_membership_code(code)
                
                if not is_valid:
                    return False, message, code_info
                
                # 记录使用
                code_info['used_count'] += 1
                code_info['used_by'].append({
                    'username': used_by,
                    'used_at': datetime.now().isoformat()
                })
                
                # 检查是否用完
                if code_info['used_count'] >= code_info['max_uses']:
                    code_info['status'] = 'exhausted'
                
                if self.save_codes():
                    print(f"会员码使用成功: {code} by {used_by}")
                    return True, "会员码使用成功", code_info
                else:
                    return False, "保存使用记录失败", None
                    
        except Exception as e:
            print(f"使用会员码异常: {e}")
            return False, f"使用失败: {str(e)}", None
    
    def list_codes(self, status: Optional[str] = None) -> List[Dict]:
        """获取会员码列表"""
        try:
            print(f"[DEBUG] list_codes called with status={status}")
            codes = []
            current_time = datetime.now()
            status_updated = False
            
            print(f"[DEBUG] Attempting to acquire lock...")
            with self.lock:  # 获取锁以确保线程安全
                print(f"[DEBUG] Lock acquired, processing {len(self.codes_data)} codes")
                for code, code_info in self.codes_data.items():
                    # 更新过期状态
                    expires_at = datetime.fromisoformat(code_info['expires_at'])
                    if current_time > expires_at and code_info['status'] == 'active':
                        code_info['status'] = 'expired'
                        status_updated = True
                    
                    # 计算剩余天数
                    remaining_days = max(0, (expires_at - current_time).days)
                    
                    code_data = code_info.copy()
                    code_data['remaining_days'] = remaining_days
                    code_data['remaining_uses'] = max(0, code_info['max_uses'] - code_info['used_count'])
                    
                    # 状态筛选
                    if status and code_info['status'] != status:
                        continue
                    
                    codes.append(code_data)
                
                print(f"[DEBUG] Processed {len(codes)} codes, status_updated={status_updated}")
                
                # 只有在状态真正更新时才保存，使用不获取锁的方法
                if status_updated:
                    print(f"[DEBUG] Saving codes...")
                    self._save_codes_without_lock()
                    print(f"[DEBUG] Codes saved")
            
            print(f"[DEBUG] Lock released, sorting codes")
            # 按创建时间倒序排列
            codes.sort(key=lambda x: x['created_at'], reverse=True)
            print(f"[DEBUG] list_codes returning {len(codes)} codes")
            return codes
            
        except Exception as e:
            print(f"获取会员码列表异常: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def delete_codes(self, codes_to_delete: List[str]) -> Tuple[int, List[str]]:
        """批量删除会员码"""
        try:
            print(f"[DEBUG] delete_codes 开始删除 {len(codes_to_delete)} 个邀请码")
            with self.lock:
                deleted_count = 0
                failed_codes = []

                print(f"[DEBUG] 当前数据库中有 {len(self.codes_data)} 个邀请码")
                print(f"[DEBUG] 数据库中的邀请码: {list(self.codes_data.keys())}")

                for code in codes_to_delete:
                    original_code = code
                    code = code.strip().upper()
                    print(f"[DEBUG] 尝试删除邀请码: {original_code} -> {code}")

                    if code in self.codes_data:
                        del self.codes_data[code]
                        deleted_count += 1
                        print(f"[DEBUG] 成功删除邀请码: {code}")
                    else:
                        failed_codes.append(original_code)
                        print(f"[DEBUG] 邀请码不存在: {code}")

                print(f"[DEBUG] 删除完成，成功: {deleted_count}，失败: {len(failed_codes)}")
                print(f"[DEBUG] 删除后数据库中还有 {len(self.codes_data)} 个邀请码")

                if self._save_codes_without_lock():
                    print(f"✅ 批量删除会员码成功: {deleted_count} 个")
                    return deleted_count, failed_codes
                else:
                    print(f"❌ 保存删除结果失败")
                    return 0, codes_to_delete

        except Exception as e:
            print(f"❌ 批量删除会员码异常: {e}")
            import traceback
            traceback.print_exc()
            return 0, codes_to_delete
    
    def cleanup_expired_codes(self) -> int:
        """清理过期的会员码"""
        try:
            with self.lock:
                current_time = datetime.now()
                expired_codes = []
                
                for code, code_info in self.codes_data.items():
                    expires_at = datetime.fromisoformat(code_info['expires_at'])
                    if current_time > expires_at:
                        expired_codes.append(code)
                
                for code in expired_codes:
                    del self.codes_data[code]
                
                if expired_codes and self.save_codes():
                    print(f"清理过期会员码成功: {len(expired_codes)} 个")
                    return len(expired_codes)
                else:
                    return 0
                    
        except Exception as e:
            print(f"清理过期会员码异常: {e}")
            return 0

    # 向后兼容方法
    def create_invitation_code(self, created_by: str, valid_days: int = 7,
                             max_uses: int = 1, note: str = "") -> Dict:
        """向后兼容的邀请码创建方法"""
        return self.create_membership_code(created_by, valid_days, max_uses, note)
    
    def validate_invitation_code(self, code: str) -> Tuple[bool, str, Optional[Dict]]:
        """向后兼容的邀请码验证方法"""
        return self.validate_membership_code(code)
    
    def use_invitation_code(self, code: str, used_by: str) -> Tuple[bool, str]:
        """向后兼容的邀请码使用方法"""
        success, message, _ = self.use_membership_code(code, used_by)
        return success, message

# 全局会员码管理器实例（单例模式）
_membership_manager = None

def get_membership_manager():
    """获取会员码管理器单例"""
    global _membership_manager
    if _membership_manager is None:
        _membership_manager = MembershipCodeManager()
    return _membership_manager

# 全局实例
membership_manager = get_membership_manager()

# 向后兼容的别名
invitation_manager = membership_manager