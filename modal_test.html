<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态窗口测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: var(--color-canvas-default);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: var(--color-canvas-subtle);
            border-radius: var(--borderRadius-medium);
            border: 1px solid var(--color-border-default);
        }

        /* 模态窗口样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: var(--color-canvas-default);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 0;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-large);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            padding: var(--base-size-16) var(--base-size-20);
            border-bottom: 1px solid var(--color-border-default);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--color-canvas-subtle);
            border-radius: var(--borderRadius-medium) var(--borderRadius-medium) 0 0;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--color-fg-default);
            font-size: 18px;
            font-weight: 600;
        }

        .modal-header h2 i {
            margin-right: var(--base-size-8);
            color: var(--color-accent-fg);
        }

        .close {
            color: var(--color-fg-muted);
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
        }

        .close:hover {
            color: var(--color-danger-fg);
            background-color: var(--color-danger-subtle);
        }

        .modal-body {
            padding: var(--base-size-20);
        }

        .form-group {
            margin-bottom: var(--base-size-16);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
            color: var(--color-fg-default);
        }

        .form-group label i {
            margin-right: var(--base-size-8);
            color: var(--color-accent-fg);
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: var(--base-size-8) var(--base-size-12);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            background-color: var(--color-canvas-default);
            color: var(--color-fg-default);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--color-accent-emphasis);
            box-shadow: 0 0 0 3px var(--color-accent-subtle);
        }

        .form-group small {
            display: block;
            margin-top: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 12px;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: var(--base-size-8);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: normal !important;
            margin-bottom: 0 !important;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-right: var(--base-size-8);
        }

        .modal-footer {
            padding: var(--base-size-16) var(--base-size-20);
            border-top: 1px solid var(--color-border-default);
            display: flex;
            justify-content: flex-end;
            gap: var(--base-size-12);
            background-color: var(--color-canvas-subtle);
            border-radius: 0 0 var(--borderRadius-medium) var(--borderRadius-medium);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="bi bi-window"></i> 模态窗口测试</h1>
        <p>这是新的创建房间模态窗口的测试页面。</p>
        
        <button class="btn-github btn-primary" onclick="showModal()">
            <i class="bi bi-plus-circle-fill"></i> 打开创建房间窗口
        </button>
        
        <div style="margin-top: 20px;">
            <h3>功能特点：</h3>
            <ul>
                <li>✅ 美观的GitHub风格设计</li>
                <li>✅ 表单验证</li>
                <li>✅ 设备和激活码下拉选择</li>
                <li>✅ 高级选项（自动启动、通知）</li>
                <li>✅ 点击外部关闭</li>
                <li>✅ 动画效果</li>
            </ul>
        </div>
    </div>

    <!-- 创建房间模态窗口 -->
    <div id="createRoomModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="bi bi-house-door-fill"></i> 创建新直播间</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="createRoomForm">
                    <div class="form-group">
                        <label for="liveId">
                            <i class="bi bi-broadcast"></i> 直播间ID *
                        </label>
                        <input type="text" id="liveId" name="liveId" required 
                               placeholder="例如: ROOM_TEST_001" 
                               pattern="[A-Za-z0-9_-]+" 
                               title="只能包含字母、数字、下划线和连字符">
                        <small>用于标识直播间的唯一ID</small>
                    </div>

                    <div class="form-group">
                        <label for="roomName">
                            <i class="bi bi-tag-fill"></i> 房间名称 *
                        </label>
                        <input type="text" id="roomName" name="roomName" required 
                               placeholder="例如: 我的测试直播间" 
                               maxlength="50">
                        <small>显示给观众的房间名称</small>
                    </div>

                    <div class="form-group">
                        <label for="targetDevice">
                            <i class="bi bi-cpu"></i> 目标设备 *
                        </label>
                        <select id="targetDevice" name="targetDevice" required>
                            <option value="">请选择设备...</option>
                            <option value="30:ed:a0:29:f0:a4">30:ed:a0:29:f0:a4 (默认设备) - 在线</option>
                            <option value="test-device-2">test-device-2 (测试设备2) - 离线</option>
                        </select>
                        <small>选择用于此直播间的ESP32设备</small>
                    </div>

                    <div class="form-group">
                        <label for="activationCode">
                            <i class="bi bi-key-fill"></i> 激活码 *
                        </label>
                        <select id="activationCode" name="activationCode" required>
                            <option value="">请选择激活码...</option>
                            <option value="PMR4NYNNWQX2">PMR4NYNNWQX2 (高级版 - 30天)</option>
                            <option value="TEST123456">TEST123456 (标准版 - 7天)</option>
                        </select>
                        <small>选择可用的激活码来激活此房间</small>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class="bi bi-info-circle"></i> 高级选项
                        </label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoStart" name="autoStart">
                                创建后自动启动
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableNotifications" name="enableNotifications" checked>
                                启用通知
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-github btn-secondary" onclick="closeModal()">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn-github btn-primary" onclick="submitForm()">
                    <i class="bi bi-plus-circle-fill"></i> 创建房间
                </button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            document.getElementById('createRoomModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('createRoomModal').style.display = 'none';
            document.getElementById('createRoomForm').reset();
        }

        function submitForm() {
            const form = document.getElementById('createRoomForm');
            const formData = new FormData(form);
            
            const liveId = formData.get('liveId');
            const roomName = formData.get('roomName');
            const targetDevice = formData.get('targetDevice');
            const activationCode = formData.get('activationCode');
            const autoStart = formData.get('autoStart') === 'on';
            const enableNotifications = formData.get('enableNotifications') === 'on';
            
            if (!liveId || !roomName || !targetDevice || !activationCode) {
                alert('请填写所有必填字段！');
                return;
            }
            
            alert(`模拟创建房间成功！\n\n直播间ID: ${liveId}\n房间名称: ${roomName}\n设备: ${targetDevice}\n激活码: ${activationCode}\n自动启动: ${autoStart ? '是' : '否'}\n启用通知: ${enableNotifications ? '是' : '否'}`);
            
            closeModal();
        }

        // 点击模态窗口外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('createRoomModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
