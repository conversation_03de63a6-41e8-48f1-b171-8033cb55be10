#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会员码创建脚本
"""

from membership_codes import membership_manager

def create_test_membership_codes():
    """创建测试会员码"""
    print("创建测试会员码...")
    
    test_codes = [
        {"days": 30, "note": "30天会员码测试"},
        {"days": 90, "note": "90天会员码测试"},
        {"days": 365, "note": "365天会员码测试"},
    ]
    
    created_codes = []
    for code_info in test_codes:
        result = membership_manager.create_membership_code(
            created_by="system",
            valid_days=code_info["days"],
            max_uses=1,
            note=code_info["note"]
        )
        
        if result['success']:
            created_codes.append(result['code'])
            print(f"创建成功: {result['code']} ({code_info['days']}天)")
        else:
            print(f"创建失败: {result['message']}")
    
    print(f"\n总共创建了 {len(created_codes)} 个测试会员码")
    for code in created_codes:
        print(f"   - {code}")

if __name__ == "__main__":
    create_test_membership_codes()