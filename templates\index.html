<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI机器人自动化直播控制台 - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 全局字体设置 */
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* GitHub风格自定义样式 */
        .time-display {
            font-family: var(--fontStack-monospace);
            font-size: 12px;
            color: var(--color-fg-muted);
            background-color: var(--color-canvas-subtle);
            padding: 4px 8px;
            border-radius: var(--borderRadius-small);
            border: 1px solid var(--color-border-default);
        }

        .sound-btn {
            background: none;
            border: 1px solid var(--color-border-default);
            color: var(--color-fg-muted);
            padding: 4px 8px;
            border-radius: var(--borderRadius-small);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sound-btn:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
            color: var(--color-accent-fg);
        }

        /* 抖音Logo样式 */
        .douyin-logo {
            margin-right: 8px;
            vertical-align: middle;
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        /* 悬停效果 */
        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 180px;
            background-color: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        /* 模态窗口样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: var(--color-canvas-default);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 0;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-large);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            padding: var(--base-size-16) var(--base-size-20);
            border-bottom: 1px solid var(--color-border-default);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--color-canvas-subtle);
            border-radius: var(--borderRadius-medium) var(--borderRadius-medium) 0 0;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--color-fg-default);
            font-size: 18px;
            font-weight: 600;
        }

        .modal-header h2 i {
            margin-right: var(--base-size-8);
            color: var(--color-accent-fg);
        }

        .close {
            color: var(--color-fg-muted);
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
        }

        .close:hover {
            color: var(--color-danger-fg);
            background-color: var(--color-danger-subtle);
        }

        .modal-body {
            padding: var(--base-size-20);
        }

        .form-group {
            margin-bottom: var(--base-size-16);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
            color: var(--color-fg-default);
        }

        .form-group label i {
            margin-right: var(--base-size-8);
            color: var(--color-accent-fg);
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: var(--base-size-8) var(--base-size-12);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            background-color: var(--color-canvas-default);
            color: var(--color-fg-default);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--color-accent-emphasis);
            box-shadow: 0 0 0 3px var(--color-accent-subtle);
        }

        .form-group small {
            display: block;
            margin-top: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 12px;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: var(--base-size-8);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: normal !important;
            margin-bottom: 0 !important;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-right: var(--base-size-8);
        }

        .modal-footer {
            padding: var(--base-size-16) var(--base-size-20);
            border-top: 1px solid var(--color-border-default);
            display: flex;
            justify-content: flex-end;
            gap: var(--base-size-12);
            background-color: var(--color-canvas-subtle);
            border-radius: 0 0 var(--borderRadius-medium) var(--borderRadius-medium);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    </style>
</head>
<body>
    <!-- GitHub风格导航栏 -->
    <nav class="github-navbar">
        <div class="github-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <a class="navbar-brand-github" href="/">
                    <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                    <span>AI机器人自动化直播控制台</span>
                </a>

                <!-- 导航链接 -->
                <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                    <a href="/" class="nav-link active">
                        <i class="bi bi-house-fill"></i>
                        <span>首页</span>
                    </a>
                    <a href="/leaderboard" class="nav-link">
                        <i class="bi bi-trophy-fill"></i>
                        <span>直播间排行榜</span>
                    </a>

                    <!-- 公告按钮将通过JavaScript动态添加 -->

                    <!-- 管理员专用链接 -->
                    <a href="/users" class="nav-link admin-only" style="display: none;">
                        <i class="bi bi-people-fill"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="/invitation-codes" class="nav-link admin-only" style="display: none;">
                        <i class="bi bi-key-fill"></i>
                        <span>会员码</span>
                    </a>
                    <a href="/announcements" class="nav-link admin-only" style="display: none;">
                        <i class="bi bi-megaphone-fill"></i>
                        <span>公告管理</span>
                    </a>
                </div>

                <div style="display: flex; align-items: center; gap: var(--base-size-16);">


                    <div class="time-display">
                        <i class="bi bi-clock"></i>
                        <span id="currentTime"></span>
                    </div>

                    <button class="sound-btn" onclick="toggleSound()" title="音效开关">
                        <i class="bi bi-volume-up" id="soundIcon"></i>
                    </button>

                    <!-- 个人中心下拉菜单 -->
                    <div class="dropdown" id="userDropdown">
                        <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                            <i class="bi bi-person-circle"></i>
                            <span id="currentUsername">用户</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdownMenu">
                            <div class="dropdown-item" style="background: none; cursor: default;">
                                <i class="bi bi-person-circle"></i>
                                <span id="userDisplayInfo">用户信息</span>
                            </div>
                            <div class="dropdown-item" id="membershipInfo" style="background: none; cursor: default; display: none; color: var(--color-accent-fg); font-weight: 500;">
                                <i class="bi bi-star-fill"></i>
                                <span id="membershipExpiry">会员信息</span>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a href="/profile" class="dropdown-item">
                                <i class="bi bi-person-gear"></i>
                                个人设置
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- GitHub风格统计面板 -->
    <div class="github-container" style="margin-top: var(--base-size-24);">
        <div class="github-grid github-grid-4">
            <div class="github-stat-card">
                <div class="github-stat-number" id="totalRooms">0</div>
                <div class="github-stat-label">
                    <i class="bi bi-house-door-fill"></i>
                    直播间总数
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number" id="activeRooms">0</div>
                <div class="github-stat-label">
                    <i class="bi bi-play-circle-fill"></i>
                    活跃直播间
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number" id="totalDevices">0</div>
                <div class="github-stat-label">
                    <i class="bi bi-cpu-fill"></i>
                    设备总数
                </div>
            </div>
            <div class="github-stat-card">
                <div class="github-stat-number" id="onlineDevices">0</div>
                <div class="github-stat-label">
                    <i class="bi bi-wifi"></i>
                    在线设备
                </div>
            </div>
        </div>
    </div>

    <!-- GitHub风格主布局 -->
    <div class="github-container">
        <div class="github-layout">
            <!-- 侧边栏 -->
            <div class="github-sidebar">
                <div class="github-sidebar-section">
                    <div class="github-sidebar-title">
                        <i class="bi bi-gear-fill"></i>
                        快速操作
                    </div>
                    <button class="btn-github btn-secondary" onclick="refreshAll()" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新状态
                    </button>
                    <button class="btn-github btn-secondary" onclick="showSystemLogs()" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-journal-text"></i>
                        系统日志
                    </button>
                    <button class="btn-github btn-secondary" onclick="exportConfig()" style="width: 100%; margin-bottom: var(--base-size-8);">
                        <i class="bi bi-download"></i>
                        导出配置
                    </button>
                    <button class="btn-github btn-secondary" onclick="emergencyStop()" style="width: 100%;">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        紧急停止
                    </button>
                </div>

                <div class="github-sidebar-section">
                    <div class="github-sidebar-title">
                        <i class="bi bi-activity"></i>
                        系统状态
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">WebSocket</span>
                        <span class="github-label label-success" id="websocketStatus">在线</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">数据库</span>
                        <span class="github-label label-success" id="databaseStatus">正常</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">AI服务</span>
                        <span class="github-label label-success" id="aiStatus">运行中</span>
                    </div>
                    <div class="github-status-item">
                        <span class="github-status-label">系统</span>
                        <span class="github-label label-success" id="systemStatus">正常</span>
                    </div>
                </div>


            </div>

            <!-- 主内容区域 -->
            <div style="display: flex; flex-direction: column; gap: var(--base-size-16);">
                <!-- 直播间管理 -->
                <div class="github-card">
                    <div class="github-card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="github-card-title">
                                <i class="bi bi-house-door-fill"></i>
                                直播间管理
                            </div>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <button class="btn-github btn-primary" onclick="console.log('按钮被点击'); showAddRoomModal();">
                                    <i class="bi bi-plus-circle-fill"></i>
                                    添加直播间
                                </button>
                                <button class="btn-github btn-secondary" onclick="refreshRooms()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    刷新
                                </button>
                                <button class="btn-github btn-secondary" onclick="testCreateRoom()">
                                    <i class="bi bi-bug-fill"></i>
                                    测试
                                </button>
                                <button class="btn-github btn-success" onclick="quickLogin()">
                                    <i class="bi bi-key-fill"></i>
                                    快速登录
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="github-card-body">
                        <div id="roomsList">
                            <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                                <i class="bi bi-hourglass-split" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                                <div>正在加载直播间数据...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备管理 -->
                <div class="github-card">
                    <div class="github-card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="github-card-title">
                                <i class="bi bi-cpu-fill"></i>
                                设备管理
                            </div>
                            <div style="display: flex; gap: var(--base-size-8);">
                                <button class="btn-github btn-primary" onclick="showAddDeviceModal()">
                                    <i class="bi bi-plus-circle-fill"></i>
                                    添加设备
                                </button>
                                <button class="btn-github btn-secondary" onclick="refreshDevices()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    刷新
                                </button>
                                <button class="btn-github btn-secondary" onclick="scanDevices()">
                                    <i class="bi bi-search"></i>
                                    扫描
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="github-card-body">
                        <div id="devicesList">
                            <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                                <i class="bi bi-hourglass-split" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                                <div>正在加载设备数据...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻控制台JavaScript -->
    <script>
        // 音效控制
        let soundEnabled = true;

        // 科幻音效函数
        function playSound(type) {
            if (!soundEnabled) return;

            // 使用Web Audio API创建科幻音效
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(type) {
                case 'beep':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    break;
                case 'success':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    break;
                case 'error':
                    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                    break;
            }

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }

        // 切换音效
        function toggleSound() {
            soundEnabled = !soundEnabled;
            const icon = document.getElementById('soundIcon');
            icon.className = soundEnabled ? 'bi bi-volume-up' : 'bi bi-volume-mute';
            playSound('beep');
        }

        // 更新时间（科幻格式）
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 数字动画效果
        function animateNumber(element, targetValue, duration = 1000) {
            const startValue = parseInt(element.textContent) || 0;
            const increment = (targetValue - startValue) / (duration / 16);
            let currentValue = startValue;

            const timer = setInterval(() => {
                currentValue += increment;
                if ((increment > 0 && currentValue >= targetValue) ||
                    (increment < 0 && currentValue <= targetValue)) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 16);
        }

        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                window.location.href = '/login';
                return false;
            }
            return true;
        }

        // 检查管理员权限并显示管理员链接
        async function checkAdminLinks() {
            try {
                const token = localStorage.getItem('auth_token');
                if (!token) return;

                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user.role === 'admin') {
                        document.querySelectorAll('.admin-only').forEach(el => {
                            el.style.display = 'flex';
                        });

                        // 更新用户名显示
                        const usernameElement = document.getElementById('currentUsername');
                        if (usernameElement) {
                            usernameElement.textContent = data.user.display_name || data.user.username;
                        }
                    }
                }
            } catch (error) {
                console.error('检查管理员权限失败:', error);
            }
        }

        // 初始化控制台
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);

            // 检查用户认证状态和权限
            checkUserAuth();

            // 检查登录状态
            if (!checkAuth()) return;

            // 检查会员状态
            checkMemberStatus();

            // 添加按钮点击音效
            document.querySelectorAll('.btn-spaceship').forEach(btn => {
                btn.addEventListener('click', () => playSound('beep'));
            });

            // 加载数据
            loadDashboardData();

            // 启动数据流动画
            startDataStreamAnimation();
        });

        // 数据流动画
        function startDataStreamAnimation() {
            setInterval(() => {
                document.querySelectorAll('.data-stream').forEach(stream => {
                    stream.style.animationDelay = Math.random() * 2 + 's';
                });
            }, 3000);
        }

        // 加载仪表板数据
        function loadDashboardData() {
            console.log('🔍 开始加载仪表板数据...');
            playSound('beep');

            // 显示加载状态
            showLoadingState();

            const token = localStorage.getItem('auth_token');
            if (!token) {
                console.log('🔍 未登录，跳过数据加载');
                showErrorState('请先登录');
                return;
            }

            // 并行获取房间和设备数据
            Promise.all([
                fetch('/api/rooms', {
                    headers: { 'Authorization': `Bearer ${token}` }
                }).then(response => response.json()),
                
                fetch('/api/status')
                    .then(response => response.json())
            ])
            .then(([roomsResponse, statusResponse]) => {
                console.log('🔍 房间API响应:', roomsResponse);
                console.log('🔍 状态API响应:', statusResponse);

                // 获取房间数据（包括未启动的）
                const allRooms = roomsResponse.success ? roomsResponse.rooms : {};
                
                // 获取运行状态和设备数据
                const runningRooms = statusResponse.rooms || {};
                const devices = statusResponse.deviceResponse?.devices || [];

                // 合并房间数据：配置数据 + 运行状态
                const mergedRooms = {};
                Object.entries(allRooms).forEach(([roomId, roomConfig]) => {
                    const runningStatus = runningRooms[roomId];
                    mergedRooms[roomId] = {
                        ...roomConfig,
                        status: runningStatus?.status || 'stopped',
                        ...runningStatus  // 如果有运行状态，覆盖配置
                    };
                });

                console.log('🔍 合并后的房间数据:', mergedRooms);
                console.log('🔍 设备数据:', devices);

                const totalRooms = Object.keys(mergedRooms).length;
                const activeRooms = Object.values(mergedRooms).filter(room => room.status === 'running').length;
                const totalDevices = devices.length;
                const onlineDevices = devices.filter(device => device.online === true).length;

                console.log('🔍 统计数据:', { totalRooms, activeRooms, totalDevices, onlineDevices });

                // 动画更新数字
                animateNumber(document.getElementById('totalRooms'), totalRooms);
                animateNumber(document.getElementById('activeRooms'), activeRooms);
                animateNumber(document.getElementById('totalDevices'), totalDevices);
                animateNumber(document.getElementById('onlineDevices'), onlineDevices);

                // 更新房间和设备列表
                updateRoomsList(mergedRooms);
                updateDevicesList(devices);

                // 更新系统状态
                updateSystemStatus(statusResponse);

                playSound('success');
                console.log('✅ 仪表板数据加载成功');
            })
            .catch(error => {
                console.error('❌ API请求失败:', error);
                showErrorState('网络请求失败: ' + error.message);
                playSound('error');
            });
        }

        // 显示加载状态
        function showLoadingState() {
            const roomsList = document.getElementById('roomsList');
            const devicesList = document.getElementById('devicesList');

            const loadingHTML = `
                <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                    <i class="bi bi-hourglass-split" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                    <div>正在加载数据...</div>
                </div>
            `;

            roomsList.innerHTML = loadingHTML;
            devicesList.innerHTML = loadingHTML;
        }

        // 显示错误状态
        function showErrorState(message) {
            const roomsList = document.getElementById('roomsList');
            const devicesList = document.getElementById('devicesList');

            const errorHTML = `
                <div style="text-align: center; padding: var(--base-size-32); color: var(--color-danger-fg);">
                    <i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                    <div>加载失败: ${message}</div>
                    <button class="btn-github btn-secondary" style="margin-top: var(--base-size-8);" onclick="loadDashboardData()">
                        <i class="bi bi-arrow-clockwise"></i>
                        重试
                    </button>
                </div>
            `;

            roomsList.innerHTML = errorHTML;
            devicesList.innerHTML = errorHTML;

            // 显示错误状态的统计数字
            document.getElementById('totalRooms').textContent = '--';
            document.getElementById('activeRooms').textContent = '--';
            document.getElementById('totalDevices').textContent = '--';
            document.getElementById('onlineDevices').textContent = '--';
        }

        // 更新房间列表
        function updateRoomsList(rooms) {
            const roomsList = document.getElementById('roomsList');

            if (Object.keys(rooms).length === 0) {
                roomsList.innerHTML = `
                    <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                        <i class="bi bi-house" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                        <p>暂无直播间</p>
                        <button class="btn-github btn-primary" onclick="showCreateRoomModal()">
                            <i class="bi bi-plus-circle-fill"></i>
                            创建直播间
                        </button>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="github-table">
                    <thead>
                        <tr>
                            <th>直播间ID</th>
                            <th>房间名称</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            for (const [roomId, roomData] of Object.entries(rooms)) {
                const status = roomData.status || 'stopped';
                
                // 获取房间名称，支持新旧格式
                let roomName = roomData.room_name || roomId;
                if (roomData.room_info?.room_name) {
                    roomName = roomData.room_info.room_name;
                }

                let statusLabel = '';
                switch (status) {
                    case 'running':
                        statusLabel = '<span class="github-label label-success">运行中</span>';
                        break;
                    case 'stopped':
                        statusLabel = '<span class="github-label label-danger">已停止</span>';
                        break;
                    default:
                        statusLabel = '<span class="github-label label-warning">未知</span>';
                }

                tableHTML += `
                    <tr>
                        <td><code>${roomId}</code></td>
                        <td>${roomName}</td>
                        <td>${statusLabel}</td>
                        <td>
                            ${status === 'running' ?
                                `<button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px; margin-right: 4px;" onclick="stopRoom('${roomId}')">
                                    <i class="bi bi-stop-fill"></i>
                                    停止
                                </button>` :
                                `<button class="btn-github btn-primary" style="padding: 2px 8px; font-size: 12px; margin-right: 4px;" onclick="startRoom('${roomId}')">
                                    <i class="bi bi-play-fill"></i>
                                    启动
                                </button>`
                            }
                            <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;" onclick="configureRoom('${roomId}')">
                                <i class="bi bi-gear-fill"></i>
                                配置
                            </button>
                        </td>
                    </tr>
                `;
            }

            tableHTML += `
                    </tbody>
                </table>
            `;

            roomsList.innerHTML = tableHTML;
        }

        // 更新设备列表
        function updateDevicesList(devices) {
            const devicesList = document.getElementById('devicesList');

            if (devices.length === 0) {
                devicesList.innerHTML = `
                    <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                        <i class="bi bi-cpu" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                        <p>暂无设备</p>
                        <button class="btn-github btn-primary" onclick="showAddDeviceModal()">
                            <i class="bi bi-plus-circle-fill"></i>
                            添加设备
                        </button>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="github-table">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>连接时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            devices.forEach(device => {
                // 修复设备状态检查 - 使用 online 字段
                const isOnline = device.online === true;
                const deviceName = device.device_name || device.device_id;
                const ipAddress = device.ip_address || '--';
                const deviceType = device.device_type || 'unknown';
                const capabilities = device.capabilities || '';

                let statusLabel = '';
                if (isOnline) {
                    statusLabel = '<span class="github-label label-success">在线</span>';
                } else {
                    statusLabel = '<span class="github-label label-danger">离线</span>';
                }

                console.log('🔍 设备状态:', device.device_id, 'online:', device.online, 'isOnline:', isOnline);

                // 格式化连接时间
                const connectTime = device.connect_time ?
                    new Date(device.connect_time).toLocaleTimeString() : '--';

                tableHTML += `
                    <tr>
                        <td><code>${device.device_id}</code></td>
                        <td>${deviceName}</td>
                        <td>
                            <span class="github-label" style="background-color: var(--color-neutral-emphasis); color: var(--color-fg-on-emphasis);">
                                ${deviceType}
                            </span>
                        </td>
                        <td>${statusLabel}</td>
                        <td>${connectTime}</td>
                        <td>
                            <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px; margin-right: 4px;" onclick="configureDevice('${device.device_id}')">
                                <i class="bi bi-gear-fill"></i>
                                配置
                            </button>
                            <button class="btn-github btn-secondary" style="padding: 2px 8px; font-size: 12px;" onclick="testDevice('${device.device_id}')">
                                <i class="bi bi-lightning-fill"></i>
                                测试
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            devicesList.innerHTML = tableHTML;
        }

        // 更新系统状态显示
        function updateSystemStatus(statusResponse) {
            try {
                // WebSocket状态
                const wsStatus = statusResponse.status?.status === 'online' ? 'online' : 'offline';
                const wsElement = document.getElementById('websocketStatus');
                if (wsStatus === 'online') {
                    wsElement.textContent = '在线';
                    wsElement.className = 'github-label label-success';
                } else {
                    wsElement.textContent = '离线';
                    wsElement.className = 'github-label label-danger';
                }

                // 数据库状态（基于API响应成功率）
                const dbElement = document.getElementById('databaseStatus');
                dbElement.textContent = '正常';
                dbElement.className = 'github-label label-success';

                // AI服务状态（基于进程运行情况）
                const aiElement = document.getElementById('aiStatus');
                const runningRooms = Object.values(statusResponse.rooms || {}).filter(room => room.status === 'running');
                if (runningRooms.length > 0) {
                    aiElement.textContent = '运行中';
                    aiElement.className = 'github-label label-success';
                } else {
                    aiElement.textContent = '空闲';
                    aiElement.className = 'github-label label-warning';
                }

                // 系统状态（综合状态）
                const systemElement = document.getElementById('systemStatus');
                const onlineDevices = statusResponse.deviceResponse?.devices?.filter(d => d.online) || [];
                if (wsStatus === 'online' && onlineDevices.length > 0) {
                    systemElement.textContent = '正常';
                    systemElement.className = 'github-label label-success';
                } else if (wsStatus === 'online') {
                    systemElement.textContent = '部分在线';
                    systemElement.className = 'github-label label-warning';
                } else {
                    systemElement.textContent = '异常';
                    systemElement.className = 'github-label label-danger';
                }
            } catch (error) {
                console.error('更新系统状态失败:', error);
                // 错误状态
                ['websocketStatus', 'databaseStatus', 'aiStatus', 'systemStatus'].forEach(id => {
                    const element = document.getElementById(id);
                    element.textContent = '未知';
                    element.className = 'github-label label-secondary';
                });
            }
        }

        // 刷新所有数据
        function refreshAll() {
            playSound('beep');
            loadDashboardData();
        }

        // 专业功能函数
        function showSystemLogs() {
            playSound('beep');
            alert('系统日志模块启动中...');
        }

        function exportConfig() {
            playSound('beep');
            alert('配置导出程序启动中...');
        }

        // 显示创建房间模态框
        function showAddRoomModal() {
            console.log('🔍 showAddRoomModal 被调用');
            playSound('beep');

            // 检查是否已登录
            const token = localStorage.getItem('auth_token');
            if (!token) {
                alert('请先登录！将跳转到登录页面。');
                window.location.href = '/login';
                return;
            }

            // 显示模态窗口
            document.getElementById('createRoomModal').style.display = 'block';

            // 加载设备选项
            loadDeviceOptions();
        }

        // 关闭创建房间模态框
        function closeCreateRoomModal() {
            document.getElementById('createRoomModal').style.display = 'none';
            // 清空表单
            document.getElementById('createRoomForm').reset();
        }

        // 加载设备选项
        async function loadDeviceOptions() {
            const token = localStorage.getItem('auth_token');
            const deviceSelect = document.getElementById('targetDevice');

            console.log('开始加载设备选项...');

            try {
                const response = await fetch('/api/devices', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('设备API响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('设备API响应数据:', data);

                // 清空现有选项
                deviceSelect.innerHTML = '<option value="">请选择设备...</option>';

                if (data.success && data.devices && data.devices.length > 0) {
                    console.log(`加载到 ${data.devices.length} 个设备`);
                    data.devices.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.device_id;
                        option.textContent = `${device.device_id} (${device.device_name || '未命名'}) - ${device.online ? '在线' : '离线'}`;
                        // 允许选择离线设备，因为它们可能在启动时连接
                        deviceSelect.appendChild(option);
                    });
                    console.log('设备选项加载成功');
                } else if (data.success && data.devices && data.devices.length === 0) {
                    deviceSelect.innerHTML = '<option value="">暂无可用设备 - 请先在设备管理中添加设备</option>';
                    console.log('没有配置任何设备');
                } else {
                    throw new Error(data.message || '获取设备列表失败');
                }
            } catch (error) {
                console.error('加载设备选项失败:', error);
                deviceSelect.innerHTML = '<option value="">加载设备失败 - ' + error.message + '</option>';
                alert('加载设备列表失败: ' + error.message + '\\n请检查网络连接或联系管理员');
            }
        }



        // 显示创建房间模态框
        function showCreateRoomModal() {
            console.log('🔍 showCreateRoomModal 开始执行');

            try {
                const liveId = prompt('请输入直播间ID:');
                console.log('🔍 用户输入的直播间ID:', liveId);
                if (!liveId) {
                    console.log('🔍 用户取消输入直播间ID');
                    return;
                }

                const roomName = prompt('请输入房间名称:');
                console.log('🔍 用户输入的房间名称:', roomName);
                if (!roomName) {
                    console.log('🔍 用户取消输入房间名称');
                    return;
                }



                console.log('🔍 开始获取设备列表...');
                // 获取设备列表供选择
                const token = localStorage.getItem('auth_token');
                console.log('🔍 认证令牌:', token ? '存在' : '不存在');

                fetch('/api/devices', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    console.log('🔍 设备列表响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('🔍 设备列表响应数据:', data);
                    if (data.success && data.devices && data.devices.length > 0) {
                        const deviceOptions = data.devices.map(device =>
                            `${device.device_id} (${device.device_name || device.device_id})`
                        ).join('\n');

                        const selectedDevice = prompt(`请选择设备:\n${deviceOptions}\n\n请输入设备ID:`);
                        console.log('🔍 用户选择的设备:', selectedDevice);
                        if (!selectedDevice) {
                            console.log('🔍 用户取消选择设备');
                            return;
                        }

                        createRoom(liveId, roomName, selectedDevice);
                    } else {
                        console.log('🔍 没有可用设备');
                        alert('没有可用设备，请先添加设备');
                    }
                })
                .catch(error => {
                    console.error('🔍 获取设备列表失败:', error);
                    alert('获取设备列表失败: ' + error.message);
                });
            } catch (error) {
                console.error('🔍 showCreateRoomModal 执行异常:', error);
                alert('创建房间过程中发生错误: ' + error.message);
            }
        }

        // 创建直播间
        function createRoom(liveId, roomName, targetDeviceId) {
            console.log('🔍 createRoom 开始执行');
            console.log('🔍 参数:', { liveId, roomName, targetDeviceId });

            const token = localStorage.getItem('auth_token');
            console.log('🔍 认证令牌:', token ? '存在' : '不存在');

            const requestData = {
                live_id: liveId,
                room_name: roomName,
                target_device_id: targetDeviceId
            };
            console.log('🔍 请求数据:', requestData);

            fetch('/api/rooms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('🔍 创建房间响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('🔍 创建房间响应数据:', data);
                if (data.success) {
                    playSound('success');
                    alert('直播间创建成功！');
                    loadDashboardData(); // 刷新数据
                } else {
                    playSound('error');
                    alert(`创建失败: ${data.message || '未知错误'}`);
                }
            })
            .catch(error => {
                console.error('🔍 创建房间请求失败:', error);
                playSound('error');
                alert(`创建失败: ${error.message || '网络错误'}`);
            });
        }

        function refreshRooms() {
            playSound('beep');
            loadDashboardData();
        }

        function showAddDeviceModal() {
            console.log('🔍 showAddDeviceModal 被调用');
            playSound('beep');

            try {
                const deviceId = prompt('请输入设备ID:');
                console.log('🔍 用户输入的设备ID:', deviceId);
                if (!deviceId) {
                    console.log('🔍 用户取消输入设备ID');
                    return;
                }

                const deviceName = prompt('请输入设备名称:');
                console.log('🔍 用户输入的设备名称:', deviceName);
                if (!deviceName) {
                    console.log('🔍 用户取消输入设备名称');
                    return;
                }

                const ipAddress = prompt('请输入设备IP地址 (可选):') || '';
                console.log('🔍 用户输入的IP地址:', ipAddress);

                addDevice(deviceId, deviceName, ipAddress);
            } catch (error) {
                console.error('🔍 showAddDeviceModal 执行异常:', error);
                alert('添加设备过程中发生错误: ' + error.message);
            }
        }

        // 添加设备
        function addDevice(deviceId, deviceName, ipAddress) {
            console.log('🔍 addDevice 开始执行');
            console.log('🔍 参数:', { deviceId, deviceName, ipAddress });

            const token = localStorage.getItem('auth_token');
            console.log('🔍 认证令牌:', token ? '存在' : '不存在');

            const requestData = {
                device_id: deviceId,
                device_name: deviceName,
                device_type: 'esp32',
                capabilities: 'voice',
                description: `用户添加的设备: ${deviceName}`,
                enabled: true
            };

            if (ipAddress) {
                requestData.ip_address = ipAddress;
            }

            console.log('🔍 请求数据:', requestData);

            fetch('/api/devices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('🔍 添加设备响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('🔍 添加设备响应数据:', data);
                if (data.success) {
                    playSound('success');
                    alert('设备添加成功！');
                    loadDashboardData(); // 刷新数据
                } else {
                    playSound('error');
                    alert(`添加失败: ${data.message || '未知错误'}`);
                }
            })
            .catch(error => {
                console.error('🔍 添加设备请求失败:', error);
                playSound('error');
                alert(`添加失败: ${error.message || '网络错误'}`);
            });
        }

        function refreshDevices() {
            playSound('beep');
            loadDashboardData();
        }

        function scanDevices() {
            playSound('beep');
            fetch('/api/devices/scan-network', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    playSound('success');
                    if (data.devices.length > 0) {
                        alert(`扫描完成，发现 ${data.devices.length} 个设备`);
                        loadDashboardData(); // 刷新数据
                    } else {
                        alert('扫描完成，未发现新设备');
                    }
                } else {
                    playSound('error');
                    alert(`扫描失败: ${data.message}`);
                }
            })
            .catch(error => {
                playSound('error');
                alert(`扫描失败: ${error.message}`);
            });
        }

        // 启动房间
        function startRoom(roomId) {
            playSound('beep');
            fetch(`/api/rooms/${roomId}/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    playSound('success');
                    alert(`房间 ${roomId} 启动成功`);
                    loadDashboardData(); // 刷新数据
                } else {
                    playSound('error');
                    alert(`启动失败: ${data.message}`);
                }
            })
            .catch(error => {
                playSound('error');
                alert(`启动失败: ${error.message}`);
            });
        }

        // 停止房间
        function stopRoom(roomId) {
            playSound('beep');
            if (!confirm(`确认停止房间 ${roomId}？`)) return;

            fetch(`/api/rooms/${roomId}/stop`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    playSound('success');
                    alert(`房间 ${roomId} 停止成功`);
                    loadDashboardData(); // 刷新数据
                } else {
                    playSound('error');
                    alert(`停止失败: ${data.message}`);
                }
            })
            .catch(error => {
                playSound('error');
                alert(`停止失败: ${error.message}`);
            });
        }

        // 配置房间
        function configureRoom(roomId) {
            playSound('beep');
            // 跳转到房间配置页面
            window.location.href = `/room/${roomId}/config`;
        }

        // 配置设备
        function configureDevice(deviceId) {
            playSound('beep');
            // 调用设备测试API
            fetch(`/api/devices/${deviceId}/test`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    playSound('success');
                    alert(`设备 ${deviceId} 配置成功`);
                } else {
                    playSound('error');
                    alert(`配置失败: ${data.message}`);
                }
            })
            .catch(error => {
                playSound('error');
                alert(`配置失败: ${error.message}`);
            });
        }

        // 测试设备
        function testDevice(deviceId) {
            playSound('beep');
            // 调用设备唤醒API
            fetch(`/api/devices/${deviceId}/wake`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    playSound('success');
                    alert(`设备 ${deviceId} 测试成功`);
                } else {
                    playSound('error');
                    alert(`测试失败: ${data.message}`);
                }
            })
            .catch(error => {
                playSound('error');
                alert(`测试失败: ${error.message}`);
            });
        }

        function emergencyStop() {
            playSound('error');
            if (confirm('确认启动紧急停止程序？')) {
                // 停止所有房间
                fetch('/api/rooms/stop-all', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        playSound('success');
                        alert('紧急停止程序已激活！所有房间已停止');
                        loadDashboardData();
                    } else {
                        alert('紧急停止失败');
                    }
                })
                .catch(error => {
                    alert('紧急停止失败');
                });
            }
        }

        // 测试创建房间功能
        function testCreateRoom() {
            console.log('🔍 测试创建房间功能');
            alert('测试按钮工作正常！现在尝试创建房间...');
            showAddRoomModal();
        }

        // 提交创建房间表单
        async function submitCreateRoom() {
            const form = document.getElementById('createRoomForm');
            const formData = new FormData(form);

            // 获取表单数据
            const liveId = formData.get('liveId').trim();
            const roomName = formData.get('roomName').trim();
            const targetDevice = formData.get('targetDevice');
            const autoStart = formData.get('autoStart') === 'on';

            // 验证必填字段
            if (!liveId || !roomName || !targetDevice) {
                alert('请填写所有必填字段！');
                return;
            }

            // 验证直播间ID格式
            if (!/^[A-Za-z0-9_-]+$/.test(liveId)) {
                alert('直播间ID只能包含字母、数字、下划线和连字符！');
                return;
            }

            console.log('🔍 提交创建房间:', { liveId, roomName, targetDevice, autoStart });

            // 禁用提交按钮
            const submitBtn = document.querySelector('.modal-footer .btn-primary');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

            try {
                // 创建房间
                const success = await createRoomWithData(liveId, roomName, targetDevice);

                if (success) {
                    // 关闭模态窗口
                    closeCreateRoomModal();

                    // 刷新数据
                    loadDashboardData();

                    // 如果选择了自动启动，则启动房间
                    if (autoStart) {
                        setTimeout(() => {
                            startRoom(liveId);
                        }, 1000);
                    }
                }
            } catch (error) {
                console.error('创建房间异常:', error);
                alert('创建房间失败: ' + error.message);
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // 创建房间的具体实现
        async function createRoomWithData(liveId, roomName, targetDeviceId) {
            const token = localStorage.getItem('auth_token');

            const requestData = {
                live_id: liveId,
                room_name: roomName,
                target_device_id: targetDeviceId
            };

            console.log('🔍 发送创建房间请求:', requestData);

            const response = await fetch('/api/rooms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(requestData)
            });

            const data = await response.json();
            console.log('🔍 创建房间响应:', data);

            if (data.success) {
                playSound('success');
                alert('✅ 房间创建成功！');
                return true;
            } else {
                playSound('error');
                alert('❌ 创建失败: ' + (data.message || '未知错误'));
                return false;
            }
        }

        // 快速登录功能
        async function quickLogin() {
            try {
                console.log('🔍 开始快速登录...');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                console.log('🔍 登录响应:', data);

                if (data.success) {
                    localStorage.setItem('auth_token', data.token);
                    console.log('✅ 快速登录成功');
                    alert('快速登录成功！现在可以创建房间了。');
                    loadDashboardData(); // 刷新页面数据
                } else {
                    console.log('❌ 快速登录失败:', data.message);
                    alert('快速登录失败: ' + data.message);
                }
            } catch (error) {
                console.error('❌ 快速登录异常:', error);
                alert('快速登录异常: ' + error.message);
            }
        }

        // 点击模态窗口外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('createRoomModal');
            if (event.target === modal) {
                closeCreateRoomModal();
            }

            // 点击外部关闭下拉菜单
            const dropdown = document.getElementById('userDropdownMenu');
            const dropdownToggle = document.querySelector('.dropdown-toggle');
            if (!dropdownToggle.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        }

        // 切换用户下拉菜单
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdownMenu');
            dropdown.classList.toggle('show');
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('auth_token');
                window.location.href = '/login';
            }
        }

        // 更新会员信息显示
        function updateMembershipDisplay(memberStatus) {
            const membershipInfo = document.getElementById('membershipInfo');
            const membershipExpiry = document.getElementById('membershipExpiry');
            
            if (!memberStatus || !memberStatus.is_member) {
                // 非会员用户
                membershipInfo.style.display = 'none';
                return;
            }
            
            // 是会员用户
            membershipInfo.style.display = 'flex';
            
            if (memberStatus.expired) {
                // 会员已过期
                membershipExpiry.textContent = '会员已过期';
                membershipInfo.style.color = 'var(--color-danger-fg)';
            } else if (memberStatus.expires_at) {
                // 有到期时间的会员
                const expiryDate = new Date(memberStatus.expires_at);
                const remainingDays = memberStatus.remaining_days || 0;
                
                if (remainingDays <= 7) {
                    // 即将到期
                    membershipExpiry.textContent = `会员剩余 ${remainingDays} 天`;
                    membershipInfo.style.color = 'var(--color-attention-fg)';
                } else {
                    // 正常会员
                    membershipExpiry.textContent = `会员剩余 ${remainingDays} 天`;
                    membershipInfo.style.color = 'var(--color-success-fg)';
                }
            } else {
                // 永久会员
                membershipExpiry.textContent = '永久会员';
                membershipInfo.style.color = 'var(--color-success-fg)';
            }
        }

        // 检查用户认证状态和权限
        async function checkUserAuth() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                // 未登录，隐藏用户相关元素
                document.getElementById('userDropdown').style.display = 'none';
                return;
            }

            try {
                // 获取用户信息（包含会员状态）
                const response = await fetch('/api/user/info', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success && data.user) {
                    // 显示用户信息
                    document.getElementById('currentUsername').textContent = data.user.display_name || data.user.username;
                    document.getElementById('userDisplayInfo').textContent = data.user.display_name || data.user.username;
                    document.getElementById('userDropdown').style.display = 'block';

                    // 更新会员信息
                    updateMembershipDisplay(data.user.member_status);

                    // 如果是管理员，显示管理员专用链接
                    if (data.user.role === 'admin') {
                        // 显示管理员专用导航链接
                        document.querySelectorAll('.admin-only').forEach(link => {
                            link.style.display = 'flex';
                        });
                    }
                } else {
                    // Token无效，清除并隐藏
                    localStorage.removeItem('auth_token');
                    document.getElementById('userDropdown').style.display = 'none';
                }
            } catch (error) {
                console.error('验证用户认证失败:', error);
                document.getElementById('userDropdown').style.display = 'none';
            }
        }

        // 检查会员状态
        async function checkMemberStatus() {
            try {
                const token = localStorage.getItem('auth_token');
                if (!token) return;

                const response = await fetch('/api/member/check', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const memberStatus = data.member_status;

                        // 如果会员已过期，显示提示并限制功能
                        if (memberStatus.expired) {
                            showMemberExpiredModal();
                            disableRoomFeatures();
                        }
                    }
                }
            } catch (error) {
                console.error('检查会员状态失败:', error);
            }
        }

        // 显示会员过期提示模态框
        function showMemberExpiredModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: #ff6b6b;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 20px;
                        color: white;
                        font-size: 24px;
                    ">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <h3 style="margin-bottom: 15px; color: #333;">会员权限已过期</h3>
                    <p style="color: #666; margin-bottom: 25px; line-height: 1.5;">
                        您的会员权限已经到期，无法使用直播服务功能。<br>
                        请联系管理员续费会员以继续使用。
                    </p>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">
                        我知道了
                    </button>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // 禁用房间相关功能
        function disableRoomFeatures() {
            // 禁用创建房间按钮
            const createBtn = document.getElementById('createRoomBtn');
            if (createBtn) {
                createBtn.disabled = true;
                createBtn.title = '会员已过期，无法创建房间';
                createBtn.style.opacity = '0.5';
                createBtn.style.cursor = 'not-allowed';
            }

            // 禁用所有房间操作按钮
            document.querySelectorAll('.room-actions button').forEach(btn => {
                if (!btn.classList.contains('btn-info')) { // 保留查看详情按钮
                    btn.disabled = true;
                    btn.title = '会员已过期，无法操作房间';
                    btn.style.opacity = '0.5';
                    btn.style.cursor = 'not-allowed';
                }
            });

            // 在房间列表顶部添加提示
            const roomsContainer = document.querySelector('.rooms-grid');
            if (roomsContainer && !document.getElementById('memberExpiredNotice')) {
                const notice = document.createElement('div');
                notice.id = 'memberExpiredNotice';
                notice.style.cssText = `
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 6px;
                    padding: 15px;
                    margin-bottom: 20px;
                    color: #856404;
                    text-align: center;
                `;
                notice.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>会员已过期</strong> - 您无法创建或管理直播间，请联系管理员续费
                `;
                roomsContainer.parentNode.insertBefore(notice, roomsContainer);
            }
        }
    </script>

    <!-- 创建房间模态窗口 -->
    <div id="createRoomModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="bi bi-house-door-fill"></i> 创建新直播间</h2>
                <span class="close" onclick="closeCreateRoomModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="createRoomForm">
                    <div class="form-group">
                        <label for="liveId">
                            <i class="bi bi-broadcast"></i> 直播间ID *
                        </label>
                        <input type="text" id="liveId" name="liveId" required
                               placeholder="例如: ROOM_TEST_001"
                               pattern="[A-Za-z0-9_-]+"
                               title="只能包含字母、数字、下划线和连字符">
                        <small>用于标识直播间的唯一ID</small>
                    </div>

                    <div class="form-group">
                        <label for="roomName">
                            <i class="bi bi-tag-fill"></i> 房间名称 *
                        </label>
                        <input type="text" id="roomName" name="roomName" required
                               placeholder="例如: 我的测试直播间"
                               maxlength="50">
                        <small>显示给观众的房间名称</small>
                    </div>

                    <div class="form-group">
                        <label for="targetDevice">
                            <i class="bi bi-cpu"></i> 目标设备 *
                        </label>
                        <select id="targetDevice" name="targetDevice" required>
                            <option value="">请选择设备...</option>
                        </select>
                        <small>选择用于此直播间的ESP32设备</small>
                    </div>



                    <div class="form-group">
                        <label>
                            <i class="bi bi-info-circle"></i> 高级选项
                        </label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoStart" name="autoStart">
                                创建后自动启动
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableNotifications" name="enableNotifications" checked>
                                启用通知
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-github btn-secondary" onclick="closeCreateRoomModal()">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn-github btn-primary" onclick="submitCreateRoom()">
                    <i class="bi bi-plus-circle-fill"></i> 创建房间
                </button>
            </div>
        </div>
    </div>

    <!-- 公告系统 -->
    <script src="/static/js/announcement-modal.js"></script>
    <script src="/static/js/announcement-button.js"></script>
</body>
</html>
