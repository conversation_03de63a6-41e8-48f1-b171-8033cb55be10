# 自定义回复功能设计方案

## 1. 配置结构设计

### 1.1 房间配置文件新增配置项
```json
{
  "custom_reply": {
    "enabled": true,
    "priority_weight": 8,  // 优先级权重，介于系统回复和普通用户消息之间
    "match_mode": "contains",  // 匹配模式: "exact"(完全匹配) 或 "contains"(包含匹配)
    "case_sensitive": false,   // 是否区分大小写
    "cooldown_seconds": 5,     // 同一关键词回复冷却时间
    "rules": [
      {
        "id": "rule_001",
        "keywords": ["你好", "hello", "hi"],  // 触发关键词列表
        "responses": [  // 回复内容列表，随机选择
          "你好{user_name}！欢迎来到直播间！",
          "嗨{user_name}，很高兴见到你！",
          "{user_name}你好呀，今天过得怎么样？"
        ],
        "enabled": true,
        "match_mode": "contains",  // 可覆盖全局匹配模式
        "weight": 8  // 可覆盖全局权重
      },
      {
        "id": "rule_002", 
        "keywords": ["谢谢", "感谢", "thanks"],
        "responses": [
          "不客气{user_name}！",
          "{user_name}太客气了！",
          "应该的{user_name}，记得点赞关注哦！"
        ],
        "enabled": true
      }
    ]
  }
}
```

### 1.2 优先级设计
- 自定义回复权重: 8 (介于FOLLOW_MESSAGE=6和NORMAL_USER=10之间)
- 确保自定义回复优先于系统回复，但低于用户消息
- 支持每个规则独立设置权重

## 2. 实现方案

### 2.1 核心类设计
```python
class CustomReplyManager:
    """自定义回复管理器"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.enabled = False
        self.rules = []
        self.cooldowns = {}  # 冷却时间记录
        self.load_config()
    
    def load_config(self):
        """加载自定义回复配置"""
        
    def check_and_reply(self, user_message, user_name, user_id):
        """检查消息并返回自定义回复"""
        
    def find_matching_rule(self, message):
        """查找匹配的规则"""
        
    def is_in_cooldown(self, rule_id):
        """检查规则是否在冷却期"""
```

### 2.2 集成点设计
- 在`_parseChatMsg`方法中，在反垃圾检查之后、普通回复处理之前插入
- 如果匹配到自定义回复，跳过普通回复处理
- 使用现有的优先队列系统发送回复

### 2.3 匹配逻辑
1. 遍历启用的自定义回复规则
2. 根据匹配模式检查关键词
3. 检查冷却时间
4. 随机选择回复模板
5. 替换变量占位符
6. 加入优先队列

## 3. 与现有功能的兼容性

### 3.1 优先级协调
- 自定义回复 > 系统回复(关注/点赞) > 欢迎消息 > 防冷场
- 不影响礼物回复和定时插播的高优先级

### 3.2 冷却机制
- 独立的冷却系统，不与现有回复冷却冲突
- 支持按规则ID分别冷却

### 3.3 消息处理流程
```
用户消息 -> 时间过滤 -> 反垃圾检查 -> 自定义回复检查 -> 普通回复处理
```

---

# 防冷场增强功能说明

## 实现方案
主动沟通功能已整合到现有的防冷场系统中，通过增强防冷场功能实现：

### 核心特性
1. **最后发言用户跟踪**: 记录最后发言的用户信息
2. **个性化回复**: 优先向最后发言用户发送个性化防冷场消息
3. **主动沟通次数限制**: 对同一用户的主动沟通次数进行限制
4. **备用通用模板**: 当无法个性化回复时，使用通用防冷场模板

### 配置结构
```json
{
  "anti_silence": {
    "enabled": true,
    "delay_seconds": 15,
    "cooldown_seconds": 30,
    "max_proactive_attempts": 2,
    "templates": [
      "现在直播间有点冷场，请主动和大家打个招呼",
      "请和观众们互动一下，营造温暖的氛围"
    ],
    "personalized_templates": [
      "{user_name}，刚才聊得挺开心的，还有什么想聊的吗？",
      "嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！",
      "{user_name}，直播间有点安静了，来活跃一下气氛吧！"
    ]
  }
}
```

### 工作流程
1. 用户发言时更新最后发言用户信息
2. 防冷场触发时，优先尝试个性化回复最后发言用户
3. 如果用户已达到最大主动沟通次数，或没有最后发言用户，使用通用模板
4. 所有消息通过现有的优先队列系统发送
