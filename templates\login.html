<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI机器人自动化直播控制台 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 全局字体设置 */
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--base-size-16);
            background-color: var(--color-canvas-subtle);
        }

        /* GitHub风格登录容器 */
        .login-container {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: 0 8px 24px rgba(31,35,40,0.12);
            padding: var(--base-size-32);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--base-size-24);
        }

        .login-logo {
            width: 64px;
            height: 64px;
            background: var(--color-accent-emphasis);
            border-radius: var(--borderRadius-medium);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--base-size-16);
            font-size: 24px;
            color: var(--color-fg-on-emphasis);
        }

        .login-title {
            font-family: var(--fontStack-system);
            font-size: 24px;
            font-weight: 600;
            color: var(--color-fg-default);
            margin-bottom: var(--base-size-8);
        }

        .login-subtitle {
            color: var(--color-fg-muted);
            font-weight: 400;
            font-size: 16px;
        }

        /* GitHub风格输入框 */
        .form-group-github {
            margin-bottom: var(--base-size-16);
        }

        .form-label-github {
            display: block;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
            font-size: 14px;
            color: var(--color-fg-default);
        }

        .form-input-github {
            width: 100%;
            padding: 5px 12px;
            font-size: 14px;
            line-height: 20px;
            color: var(--color-fg-default);
            background-color: var(--color-canvas-default);
            background-repeat: no-repeat;
            background-position: right 8px center;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            outline: none;
            box-shadow: inset 0 1px 0 rgba(208,215,222,0.2);
            transition: border-color 0.2s ease-in-out;
        }

        .form-input-github:focus {
            border-color: var(--color-accent-emphasis);
            outline: 2px solid var(--color-accent-emphasis);
            outline-offset: -2px;
            box-shadow: none;
        }
        }

        .form-floating-modern input:focus + label,
        .form-floating-modern input:not(:placeholder-shown) + label {
            top: 0;
            font-size: 10px;
            color: #2563eb;
            background: #f8fafc;
            border-radius: 2px;
            padding: 0.125rem 0.25rem;
        }

        .btn-login {
            width: 100%;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
            border-radius: 4px;
            color: white;
            font-weight: 700;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.2s ease;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .btn-login:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
            background: linear-gradient(90deg, #1d4ed8 0%, #1e40af 100%);
        }

        .btn-login:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .alert-modern {
            background: rgba(254, 226, 226, 0.95);
            border: 1px solid #dc2626;
            border-radius: 4px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            color: #dc2626;
            font-weight: 500;
            font-size: 12px;
        }

        .alert-success {
            background: rgba(209, 250, 229, 0.95);
            border: 1px solid #059669;
            border-radius: 4px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            color: #059669;
            font-weight: 500;
            font-size: 12px;
        }

        .footer-links {
            text-align: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #cbd5e1;
        }

        .footer-links span {
            color: #64748b;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer-links i {
            color: #2563eb;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                </svg>
            </div>
            <h1 class="login-title">AI机器人自动化直播控制台</h1>
            <p class="login-subtitle">登录到您的账户</p>
        </div>

        <!-- 登录表单 -->
        <form id="loginForm" style="display: block;">
            <div id="alertContainer"></div>

            <div class="form-group-github">
                <label class="form-label-github" for="loginUsername">用户名</label>
                <input type="text" id="loginUsername" class="form-input-github" placeholder="输入用户名" required>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="loginPassword">密码</label>
                <input type="password" id="loginPassword" class="form-input-github" placeholder="输入密码" required>
            </div>

            <button type="submit" class="btn-github btn-primary" style="width: 100%;">
                登录
            </button>
        </form>

        <!-- 注册表单 -->
        <form id="registerForm" style="display: none;">
            <div id="registerAlertContainer"></div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerDisplayName">昵称 *</label>
                <input type="text" id="registerDisplayName" class="form-input-github" placeholder="输入您的昵称" required>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerUsername">用户名 *</label>
                <input type="text" id="registerUsername" class="form-input-github"
                       placeholder="4位以上英文或数字"
                       pattern="[A-Za-z0-9]{4,}"
                       title="用户名必须是4位以上的英文字母或数字" required>
                <small style="color: var(--color-fg-muted); font-size: 12px; margin-top: 4px; display: block;">
                    用户名必须是4位以上的英文字母或数字
                </small>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerPassword">密码 *</label>
                <input type="password" id="registerPassword" class="form-input-github"
                       placeholder="输入密码（至少6位）"
                       minlength="6" required>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerConfirmPassword">确认密码 *</label>
                <input type="password" id="registerConfirmPassword" class="form-input-github"
                       placeholder="再次输入密码" required>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerEmail">电子邮箱</label>
                <input type="email" id="registerEmail" class="form-input-github"
                       placeholder="输入邮箱地址（可选）">
                <small style="color: var(--color-fg-muted); font-size: 12px; margin-top: 4px; display: block;">
                    邮箱地址为可选项，用于找回密码
                </small>
            </div>

            <div class="form-group-github">
                <label class="form-label-github" for="registerInvitationCode">会员码 *</label>
                <input type="text" id="registerInvitationCode" class="form-input-github"
                       placeholder="输入会员码"
                       style="text-transform: uppercase;" required>
                <small style="color: var(--color-fg-muted); font-size: 12px; margin-top: 4px; display: block;">
                    请输入有效的会员码才能注册
                </small>
            </div>

            <button type="submit" class="btn-github btn-primary" style="width: 100%;">
                注册
            </button>
        </form>

        <!-- 切换按钮 -->
        <div style="text-align: center; margin-top: var(--base-size-20);">
            <button type="button" id="toggleFormBtn" class="btn-github btn-secondary"
                    style="background: none; border: none; color: var(--color-accent-fg); text-decoration: underline; cursor: pointer; font-size: 14px;">
                还没有账户？点击注册
            </button>
        </div>

        <div class="footer-links">
            <div style="display: flex; justify-content: center; align-items: center; gap: var(--base-size-16); margin-top: var(--base-size-24); font-size: 12px; color: var(--color-fg-muted);">
                <span>
                    <i class="bi bi-shield-check-fill" style="margin-right: var(--base-size-4); color: var(--color-success-fg);"></i>
                    安全登录
                </span>
                <span>•</span>
                <span>
                    <i class="bi bi-lock-fill" style="margin-right: var(--base-size-4); color: var(--color-success-fg);"></i>
                    数据加密
                </span>
            </div>
        </div>
    </div>

    <script>
        // 表单切换功能
        let isLoginMode = true;

        function toggleForm() {
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const toggleBtn = document.getElementById('toggleFormBtn');
            const subtitle = document.querySelector('.login-subtitle');

            if (isLoginMode) {
                // 切换到注册模式
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
                toggleBtn.textContent = '已有账户？点击登录';
                subtitle.textContent = '创建新账户';
                isLoginMode = false;
            } else {
                // 切换到登录模式
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
                toggleBtn.textContent = '还没有账户？点击注册';
                subtitle.textContent = '登录到您的账户';
                isLoginMode = true;
            }
        }

        document.getElementById('toggleFormBtn').addEventListener('click', toggleForm);

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value;
            const alertContainer = document.getElementById('alertContainer');

            console.log('登录表单提交:', { username, password: '***' });

            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2" style="animation: spin 1s linear infinite;"></i>登录中...';
            submitBtn.disabled = true;

            // 添加旋转动画
            const style = document.createElement('style');
            style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
            document.head.appendChild(style);

            try {
                console.log('发送登录请求到:', '/api/auth/login');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('登录响应状态:', response.status);
                const result = await response.json();
                console.log('登录响应结果:', result);

                if (result.success) {
                    // 保存令牌到localStorage
                    localStorage.setItem('auth_token', result.token);
                    localStorage.setItem('user_info', JSON.stringify(result.user));

                    showAlert('登录成功，正在跳转...', 'success');

                    // 跳转到主页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showAlert(result.message || '登录失败', 'danger');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showAlert('网络错误，请稍后重试: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // 注册表单处理
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const displayName = document.getElementById('registerDisplayName').value.trim();
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;
            const email = document.getElementById('registerEmail').value.trim();
            const invitationCode = document.getElementById('registerInvitationCode').value.trim().toUpperCase();
            const alertContainer = document.getElementById('registerAlertContainer');

            // 表单验证
            if (!displayName || !username || !password || !confirmPassword || !invitationCode) {
                showAlert('请填写所有必填字段', 'danger', alertContainer);
                return;
            }

            if (username.length < 4 || !/^[A-Za-z0-9]+$/.test(username)) {
                showAlert('用户名必须是4位以上的英文字母或数字', 'danger', alertContainer);
                return;
            }

            if (password.length < 6) {
                showAlert('密码长度至少6位', 'danger', alertContainer);
                return;
            }

            if (password !== confirmPassword) {
                showAlert('两次输入的密码不一致', 'danger', alertContainer);
                return;
            }

            if (email && !email.includes('@')) {
                showAlert('邮箱格式不正确', 'danger', alertContainer);
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('#registerForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2" style="animation: spin 1s linear infinite;"></i>注册中...';
            submitBtn.disabled = true;

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        display_name: displayName,
                        email: email || '',
                        invitation_code: invitationCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('注册成功！请使用新账户登录', 'success', alertContainer);

                    // 3秒后切换到登录表单
                    setTimeout(() => {
                        toggleForm();
                        // 清空注册表单
                        document.getElementById('registerForm').reset();
                        // 预填用户名
                        document.getElementById('loginUsername').value = username;
                    }, 2000);
                } else {
                    showAlert(result.message || '注册失败', 'danger', alertContainer);
                }
            } catch (error) {
                console.error('注册错误:', error);
                showAlert('网络错误，请稍后重试: ' + error.message, 'danger', alertContainer);
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        function showAlert(message, type, container = null) {
            const alertContainer = container || document.getElementById('alertContainer');
            const iconMap = {
                'danger': 'bi-exclamation-triangle-fill',
                'success': 'bi-check-circle-fill',
                'warning': 'bi-exclamation-triangle-fill',
                'info': 'bi-info-circle-fill'
            };

            const alertClass = type === 'success' ? 'alert-success' : 'alert-modern';

            const alertHtml = `
                <div class="${alertClass}" style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;">
                        <i class="bi ${iconMap[type] || iconMap.danger} me-2"></i>
                        <span>${message}</span>
                    </div>
                    <button type="button" onclick="this.parentElement.style.display='none'"
                            style="background: none; border: none; color: inherit; cursor: pointer; padding: 0; font-size: 1.2rem; opacity: 0.7; transition: opacity 0.2s;">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = alertContainer.querySelector(`.${alertClass}`);
                if (alert) {
                    alert.style.display = 'none';
                }
            }, type === 'success' ? 3000 : 5000);
        }
        
        // 检查是否已经登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('auth_token');
            if (token) {
                // 验证令牌是否有效
                fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // 已登录，跳转到主页
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    console.log('令牌验证失败:', error);
                    // 清除无效令牌
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_info');
                });
            }
        });
        
        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
