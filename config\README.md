# 配置文件说明

## 目录结构
```
config/
├── README.md              # 配置说明文档
├── global_config.json     # 全局默认配置
├── rooms/                 # 房间配置目录
│   ├── 337356796001.json  # 东教兽号
│   ├── 211953907443.json  # 硅灵造物直播间
│   └── ...               # 其他房间配置
└── devices/               # 设备配置目录
    └── devices.json       # 设备注册信息
```

## 配置优先级
1. 房间特定配置 (rooms/*.json)
2. 全局默认配置 (global_config.json)
3. 代码内置默认值

## 配置字段说明

### 全局配置 (global_config.json)
- `websocket_settings`: WebSocket连接配置
- `message_queue`: 消息队列配置
- `logging`: 日志配置
- `status_monitor`: 状态监控配置

### 房间配置 (rooms/*.json)
- `live_id`: 直播间ID
- `room_name`: 房间名称
- `target_device_id`: 目标设备ID
- `enabled`: 是否启用
- `welcome_message`: 欢迎消息配置
- `anti_spam`: 防刷屏配置
- `gift_priority`: 礼物优先级配置
- `interactive_games`: 互动游戏配置
- `scheduled_broadcast`: 定时插播配置
- `anti_silence`: 防冷场配置
- `follow_interaction`: 关注互动配置
- `like_interaction`: 点赞互动配置
- `message_preprocess`: 消息预处理配置

### 设备配置 (devices/devices.json)
- `device_id`: 设备MAC地址
- `device_name`: 设备名称
- `device_type`: 设备类型
- `capabilities`: 设备能力
- `enabled`: 是否启用
