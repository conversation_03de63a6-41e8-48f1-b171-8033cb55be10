#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试房间加载问题
"""

import os
import json

def debug_room_loading():
    """调试房间加载"""
    print("🔍 调试房间加载问题")
    
    rooms_dir = 'config/rooms'
    if not os.path.exists(rooms_dir):
        print(f"❌ 房间配置目录不存在: {rooms_dir}")
        return
    
    print(f"📁 房间配置目录: {rooms_dir}")
    
    all_files = os.listdir(rooms_dir)
    json_files = [f for f in all_files if f.endswith('.json')]
    
    print(f"📋 发现 {len(json_files)} 个JSON文件:")
    for f in json_files:
        print(f"  - {f}")
    
    print("\n🔍 逐个检查房间配置:")
    
    valid_rooms = 0
    invalid_rooms = 0
    
    for filename in json_files:
        live_id = filename[:-5]  # 移除.json后缀
        config_path = os.path.join(rooms_dir, filename)
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                room_config = json.load(f)
            
            # 检查是否有有效的live_id
            has_live_id = False
            actual_live_id = None
            
            if 'live_id' in room_config:
                actual_live_id = room_config['live_id']
                has_live_id = True
            elif (isinstance(room_config.get('live_settings'), dict) and
                  'live_id' in room_config['live_settings']):
                actual_live_id = room_config['live_settings']['live_id']
                has_live_id = True
            
            # 检查room_name
            room_name = room_config.get('room_name')
            if not room_name:
                room_name = room_config.get('room_info', {}).get('room_name', '未知')
            
            if has_live_id and actual_live_id != 'YOUR_LIVE_ID':
                print(f"✅ {live_id}: {room_name} (live_id: {actual_live_id})")
                valid_rooms += 1
            else:
                print(f"❌ {live_id}: {room_name} (live_id: {actual_live_id}) - 无效或模板")
                invalid_rooms += 1
                
        except Exception as e:
            print(f"❌ {live_id}: 加载失败 - {e}")
            invalid_rooms += 1
    
    print(f"\n📊 统计结果:")
    print(f"  有效房间: {valid_rooms}")
    print(f"  无效房间: {invalid_rooms}")
    print(f"  总计: {valid_rooms + invalid_rooms}")

if __name__ == "__main__":
    debug_room_loading()
