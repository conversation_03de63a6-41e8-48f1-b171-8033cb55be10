<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员码管理 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 导航栏样式 */
        .navbar-brand-github {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            text-decoration: none;
            color: var(--color-fg-default);
            font-weight: 600;
            font-size: 16px;
        }

        .douyin-logo {
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--color-canvas-overlay);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        .time-display {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--base-size-16);
            margin-bottom: var(--base-size-24);
        }

        .stat-card {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-16);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: var(--base-size-8);
        }

        .stat-label {
            color: var(--color-fg-muted);
            font-size: 0.875rem;
        }

        .code-item {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-16);
            margin-bottom: var(--base-size-12);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--base-size-12);
        }

        .code-text {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-accent-fg);
        }

        .code-status {
            padding: 2px 8px;
            border-radius: var(--border-radius-small);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: var(--color-success-subtle);
            color: var(--color-success-fg);
        }

        .status-expired {
            background: var(--color-danger-subtle);
            color: var(--color-danger-fg);
        }

        .status-exhausted {
            background: var(--color-neutral-subtle);
            color: var(--color-fg-muted);
        }

        .code-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--base-size-12);
            font-size: 0.875rem;
            color: var(--color-fg-muted);
        }

        .code-actions {
            display: flex;
            gap: var(--base-size-8);
            margin-top: var(--base-size-12);
        }

        .filter-tabs {
            display: flex;
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-16);
            border-bottom: 1px solid var(--color-border-default);
        }

        .filter-tab {
            padding: var(--base-size-8) var(--base-size-16);
            border: none;
            background: none;
            color: var(--color-fg-muted);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .filter-tab.active {
            color: var(--color-accent-fg);
            border-bottom-color: var(--color-accent-fg);
        }

        .filter-tab:hover {
            color: var(--color-fg-default);
        }

        .create-form {
            background: var(--color-canvas-subtle);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-20);
            margin-bottom: var(--base-size-24);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--base-size-16);
            margin-bottom: var(--base-size-16);
        }

        .batch-actions {
            display: flex;
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-16);
            align-items: center;
        }

        .selected-count {
            color: var(--color-fg-muted);
            font-size: 0.875rem;
        }

        .code-checkbox {
            margin-right: var(--base-size-8);
        }

        .empty-state {
            text-align: center;
            padding: var(--base-size-40);
            color: var(--color-fg-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: var(--base-size-16);
            opacity: 0.5;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: var(--base-size-16);
            border-radius: var(--border-radius-medium);
            box-shadow: var(--shadow-large);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--color-success-subtle);
            border: 1px solid var(--color-success-emphasis);
            color: var(--color-success-fg);
        }

        .notification-error {
            background: var(--color-danger-subtle);
            border: 1px solid var(--color-danger-emphasis);
            color: var(--color-danger-fg);
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
        }

        .notification-close:hover {
            opacity: 1;
        }

        .notification-body {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .code-highlight {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            margin: 0 2px;
        }

        .newly-created {
            animation: highlight-new 3s ease-out;
            border: 2px solid var(--color-success-emphasis) !important;
        }

        @keyframes highlight-new {
            0% {
                background: var(--color-success-subtle);
                transform: scale(1.02);
            }
            100% {
                background: var(--color-canvas-default);
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="github-container">
        <!-- GitHub风格导航栏 -->
        <nav class="github-navbar">
            <div class="github-container">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <a class="navbar-brand-github" href="/">
                        <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                        <span>AI机器人自动化直播控制台</span>
                    </a>

                    <!-- 导航链接 -->
                    <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                        <a href="/" class="nav-link">
                            <i class="bi bi-house-fill"></i>
                            <span>首页</span>
                        </a>
                        <a href="/leaderboard" class="nav-link">
                            <i class="bi bi-trophy-fill"></i>
                            <span>直播间排行榜</span>
                        </a>

                        <!-- 公告按钮将通过JavaScript动态添加 -->

                        <!-- 管理员专用链接 -->
                        <a href="/users" class="nav-link admin-only">
                            <i class="bi bi-people-fill"></i>
                            <span>用户管理</span>
                        </a>
                        <a href="/invitation-codes" class="nav-link active admin-only">
                            <i class="bi bi-key-fill"></i>
                            <span>会员码</span>
                        </a>
                        <a href="/announcements" class="nav-link admin-only">
                            <i class="bi bi-megaphone-fill"></i>
                            <span>公告管理</span>
                        </a>
                    </div>

                    <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                        <div class="time-display">
                            <i class="bi bi-clock"></i>
                            <span id="currentTime"></span>
                        </div>

                        <!-- 个人中心下拉菜单 -->
                        <div class="dropdown" id="userDropdown">
                            <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                                <i class="bi bi-person-circle"></i>
                                <span id="currentUsername">用户</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="userDropdownMenu">
                                <a href="/profile" class="dropdown-item">
                                    <i class="bi bi-person-gear"></i>
                                    个人设置
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="github-main">
            <div class="github-header">
                <h1>
                    <i class="bi bi-key-fill"></i>
                    会员码管理
                </h1>
                <p>管理系统会员码，控制用户注册权限</p>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCodes">0</div>
                    <div class="stat-label">总会员码</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeCodes">0</div>
                    <div class="stat-label">可用会员码</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="expiredCodes">0</div>
                    <div class="stat-label">已过期</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="exhaustedCodes">0</div>
                    <div class="stat-label">已用完</div>
                </div>
            </div>

            <!-- 创建会员码表单 -->
            <div class="create-form">
                <h3>
                    <i class="bi bi-plus-circle"></i>
                    创建会员码
                </h3>
                <form id="createCodeForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="validDays">会员天数</label>
                            <input type="number" id="validDays" name="validDays" min="1" max="3650" value="30" required>
                            <small class="form-text text-muted">用户使用此码后获得的会员天数（最多10年）</small>
                        </div>
                        <div class="form-group">
                            <label for="maxUses">使用次数</label>
                            <input type="number" id="maxUses" name="maxUses" min="1" max="1000" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="note">备注</label>
                            <input type="text" id="note" name="note" placeholder="可选备注信息">
                        </div>
                    </div>
                    <button type="submit" class="btn-github btn-primary">
                        <i class="bi bi-plus"></i>
                        创建会员码
                    </button>
                </form>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="active">可用</button>
                <button class="filter-tab" data-status="expired">已过期</button>
                <button class="filter-tab" data-status="exhausted">已用完</button>
            </div>

            <!-- 批量操作 -->
            <div class="batch-actions">
                <button id="selectAllBtn" class="btn-github btn-sm">全选</button>
                <button id="deleteSelectedBtn" class="btn-github btn-sm btn-danger" disabled>
                    <i class="bi bi-trash"></i>
                    删除选中
                </button>
                <button id="cleanupBtn" class="btn-github btn-sm">
                    <i class="bi bi-trash3"></i>
                    清理过期
                </button>
                <span class="selected-count" id="selectedCount">已选择 0 个会员码</span>
            </div>

            <!-- 会员码列表 -->
            <div id="codesList">
                <div class="empty-state">
                    <i class="bi bi-key"></i>
                    <p>正在加载会员码...</p>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/js/auth.js"></script>
    <script>
        let allCodes = [];
        let currentFilter = 'all';
        let selectedCodes = new Set();

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查认证和管理员权限
            if (!checkAuth()) {
                window.location.href = '/login';
                return;
            }

            await checkAdminPermission();
            await loadUserInfo();
            await loadInvitationCodes();
            
            // 绑定事件
            document.getElementById('createCodeForm').addEventListener('submit', handleCreateCode);
            document.getElementById('selectAllBtn').addEventListener('click', toggleSelectAll);
            document.getElementById('deleteSelectedBtn').addEventListener('click', deleteSelectedCodes);
            document.getElementById('cleanupBtn').addEventListener('click', cleanupExpiredCodes);
            
            // 筛选标签事件
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.status;
                    renderCodes();
                });
            });
        });

        // 检查管理员权限
        async function checkAdminPermission() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (!data.success || data.user.role !== 'admin') {
                        alert('您没有访问此页面的权限，需要管理员权限');
                        window.location.href = '/';
                        return;
                    }
                } else {
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('权限检查失败:', error);
                window.location.href = '/login';
            }
        }

        // 加载用户信息
        async function loadUserInfo() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/user-info', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('userDisplayName').textContent = data.display_name || data.username;
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }

        // 加载会员码列表
        async function loadInvitationCodes() {
            try {
                console.log('开始加载会员码...');
                const token = localStorage.getItem('auth_token');
                console.log('Token:', token ? '存在' : '不存在');

                const response = await fetch('/api/invitation-codes', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                console.log('响应状态:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('会员码数据:', data);
                    if (data.success) {
                        allCodes = data.codes;
                        updateStats();
                        renderCodes();
                    } else {
                        showError('加载会员码失败: ' + data.message);
                    }
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('API错误:', errorData);
                    showError('加载会员码失败: ' + (errorData.message || '未知错误'));
                }
            } catch (error) {
                console.error('加载会员码失败:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 更新统计信息
        function updateStats() {
            const total = allCodes.length;
            const active = allCodes.filter(code => code.status === 'active').length;
            const expired = allCodes.filter(code => code.status === 'expired').length;
            const exhausted = allCodes.filter(code => code.status === 'exhausted').length;

            document.getElementById('totalCodes').textContent = total;
            document.getElementById('activeCodes').textContent = active;
            document.getElementById('expiredCodes').textContent = expired;
            document.getElementById('exhaustedCodes').textContent = exhausted;
        }

        // 渲染会员码列表
        function renderCodes() {
            const container = document.getElementById('codesList');
            let filteredCodes = allCodes;

            if (currentFilter !== 'all') {
                filteredCodes = allCodes.filter(code => code.status === currentFilter);
            }

            if (filteredCodes.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-key"></i>
                        <p>没有找到会员码</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredCodes.map(code => `
                <div class="code-item">
                    <div class="code-header">
                        <div style="display: flex; align-items: center;">
                            <input type="checkbox" class="code-checkbox" 
                                   data-code="${code.code}" 
                                   ${selectedCodes.has(code.code) ? 'checked' : ''}
                                   onchange="toggleCodeSelection('${code.code}')">
                            <span class="code-text">${code.code}</span>
                        </div>
                        <span class="code-status status-${code.status}">
                            ${getStatusText(code.status)}
                        </span>
                    </div>
                    <div class="code-info">
                        <div><strong>创建者:</strong> ${code.created_by}</div>
                        <div><strong>创建时间:</strong> ${formatDate(code.created_at)}</div>
                        <div><strong>有效期:</strong> ${code.valid_days} 天</div>
                        <div><strong>剩余天数:</strong> ${code.remaining_days}</div>
                        <div><strong>使用次数:</strong> ${code.used_count}/${code.max_uses}</div>
                        <div><strong>剩余次数:</strong> ${code.remaining_uses}</div>
                        ${code.note ? `<div><strong>备注:</strong> ${code.note}</div>` : ''}
                    </div>
                    <div class="code-actions">
                        <button class="btn-github btn-sm" onclick="copyCode('${code.code}')">
                            <i class="bi bi-clipboard"></i>
                            复制
                        </button>
                        <button class="btn-github btn-sm btn-danger" onclick="deleteCode('${code.code}')">
                            <i class="bi bi-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '可用',
                'expired': '已过期',
                'exhausted': '已用完'
            };
            return statusMap[status] || status;
        }

        // 格式化日期
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // 创建会员码
        async function handleCreateCode(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                valid_days: parseInt(formData.get('validDays')),
                max_uses: parseInt(formData.get('maxUses')),
                note: formData.get('note') || ''
            };

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/invitation-codes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    const codeHtml = `<span class="code-highlight">${result.code}</span>`;
                    showSuccess(`🎉 会员码创建成功！<br><br>新会员码：${codeHtml}<br><br>已自动复制到剪贴板，请保存好这个会员码。`, { duration: 8000 });
                    
                    // 复制到剪贴板
                    try {
                        await navigator.clipboard.writeText(result.code);
                    } catch (err) {
                        console.log('复制到剪贴板失败:', err);
                    }
                    
                    e.target.reset();
                    
                    // 重新加载列表并高亮新创建的会员码
                    await loadInvitationCodes();
                    highlightNewCode(result.code);
                } else {
                    showError('创建失败: ' + result.message);
                }
            } catch (error) {
                console.error('创建会员码失败:', error);
                showError('网络错误');
            }
        }

        // 高亮新创建的会员码
        function highlightNewCode(code) {
            setTimeout(() => {
                const codeElements = document.querySelectorAll('.code-text');
                codeElements.forEach(element => {
                    if (element.textContent === code) {
                        const codeItem = element.closest('.code-item');
                        if (codeItem) {
                            codeItem.classList.add('newly-created');
                            codeItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            }, 500); // 等待列表渲染完成
        }

        // 复制会员码
        function copyCode(code) {
            navigator.clipboard.writeText(code).then(() => {
                showSuccess('会员码已复制到剪贴板');
            }).catch(() => {
                showError('复制失败');
            });
        }

        // 删除单个会员码
        async function deleteCode(code) {
            if (!confirm('确定要删除这个会员码吗？')) return;
            
            await deleteCodesRequest([code]);
        }

        // 切换会员码选择
        function toggleCodeSelection(code) {
            if (selectedCodes.has(code)) {
                selectedCodes.delete(code);
            } else {
                selectedCodes.add(code);
            }
            updateSelectedCount();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            const allSelected = selectedCodes.size === checkboxes.length;
            
            if (allSelected) {
                selectedCodes.clear();
                checkboxes.forEach(cb => cb.checked = false);
            } else {
                checkboxes.forEach(cb => {
                    cb.checked = true;
                    selectedCodes.add(cb.dataset.code);
                });
            }
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const count = selectedCodes.size;
            document.getElementById('selectedCount').textContent = `已选择 ${count} 个会员码`;
            document.getElementById('deleteSelectedBtn').disabled = count === 0;
            document.getElementById('selectAllBtn').textContent = 
                count === document.querySelectorAll('.code-checkbox').length ? '取消全选' : '全选';
        }

        // 删除选中的会员码
        async function deleteSelectedCodes() {
            if (selectedCodes.size === 0) return;
            
            if (!confirm(`确定要删除选中的 ${selectedCodes.size} 个会员码吗？`)) return;
            
            await deleteCodesRequest(Array.from(selectedCodes));
        }

        // 删除会员码请求
        async function deleteCodesRequest(codes) {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/invitation-codes/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ codes })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message);
                    selectedCodes.clear();
                    loadInvitationCodes();
                } else {
                    showError('删除失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除会员码失败:', error);
                showError('网络错误');
            }
        }

        // 清理过期会员码
        async function cleanupExpiredCodes() {
            if (!confirm('确定要清理所有过期的会员码吗？')) return;
            
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/invitation-codes/cleanup', {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message);
                    loadInvitationCodes();
                } else {
                    showError('清理失败: ' + result.message);
                }
            } catch (error) {
                console.error('清理过期会员码失败:', error);
                showError('网络错误');
            }
        }

        // 显示成功消息
        function showSuccess(message, options = {}) {
            showNotification(message, 'success', options);
        }

        // 显示错误消息
        function showError(message, options = {}) {
            showNotification(message, 'error', options);
        }

        // 通用通知函数
        function showNotification(message, type = 'success', options = {}) {
            // 移除现有通知
            const existing = document.querySelector('.notification');
            if (existing) {
                existing.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            
            const title = type === 'success' ? '成功' : '错误';
            const icon = type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill';
            
            notification.innerHTML = `
                <div class="notification-header">
                    <span><i class="bi ${icon}"></i> ${title}</span>
                    <button class="notification-close" onclick="closeNotification(this)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="notification-body">${message}</div>
            `;

            // 添加到页面
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // 自动关闭
            const duration = options.duration || (type === 'success' ? 5000 : 8000);
            setTimeout(() => {
                closeNotification(notification.querySelector('.notification-close'));
            }, duration);
        }

        // 关闭通知
        function closeNotification(button) {
            const notification = button.closest('.notification');
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }

        // 导航栏相关函数
        function toggleUserDropdown() {
            const menu = document.getElementById('userDropdownMenu');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const menu = document.getElementById('userDropdownMenu');

            if (!dropdown.contains(event.target)) {
                menu.classList.remove('show');
            }
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即更新一次
    </script>

    <!-- 公告系统 -->
    <script src="/static/js/announcement-modal.js"></script>
    <script src="/static/js/announcement-button.js"></script>
</body>
</html>
