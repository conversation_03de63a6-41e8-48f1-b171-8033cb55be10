# 🚀 生产环境部署指南

## 📋 部署前检查清单

### 环境要求
- [ ] Python 3.8+ 已安装
- [ ] 所有依赖包已安装 (`pip install -r requirements.txt`)
- [ ] ESP32设备已配置并连接到网络
- [ ] 防火墙已配置允许端口15000和15008

### 安全配置
- [ ] 设置环境变量 `FLASK_SECRET_KEY` 为强密码
- [ ] 修改默认管理员密码
- [ ] 配置HTTPS（生产环境推荐）
- [ ] 设置适当的文件权限

### 配置文件检查
- [ ] 验证所有房间配置文件格式正确
- [ ] 确认设备ID配置正确
- [ ] 检查日志级别设置为INFO或WARNING

## 🔧 部署步骤

### 1. 环境变量设置

**Windows:**
```cmd
set FLASK_SECRET_KEY=your-very-strong-secret-key-here
set FLASK_ENV=production
```

**Linux/macOS:**
```bash
export FLASK_SECRET_KEY="your-very-strong-secret-key-here"
export FLASK_ENV=production
```

### 2. 创建系统服务（Linux）

创建服务文件 `/etc/systemd/system/live-chat-bot.service`:

```ini
[Unit]
Description=Live Chat Bot System
After=network.target

[Service]
Type=simple
User=chatbot
WorkingDirectory=/opt/live_chat_bot
Environment=FLASK_SECRET_KEY=your-secret-key
Environment=FLASK_ENV=production
ExecStart=/usr/bin/python3 start_system.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
sudo systemctl enable live-chat-bot
sudo systemctl start live-chat-bot
```

### 3. 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:15008;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /ws {
        proxy_pass http://localhost:15000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

### 4. 日志管理

配置日志轮转 `/etc/logrotate.d/live-chat-bot`:

```
/opt/live_chat_bot/data/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 chatbot chatbot
    postrotate
        systemctl reload live-chat-bot
    endscript
}
```

## 🔒 安全最佳实践

### 1. 用户权限管理
- 创建专用用户运行服务
- 限制文件访问权限
- 定期更新密码

### 2. 网络安全
- 使用防火墙限制访问
- 配置HTTPS证书
- 定期更新系统和依赖

### 3. 数据备份
- 定期备份配置文件
- 备份用户数据和统计数据
- 测试恢复流程

## 📊 监控和维护

### 1. 系统监控
```bash
# 检查服务状态
sudo systemctl status live-chat-bot

# 查看日志
sudo journalctl -u live-chat-bot -f

# 检查端口占用
netstat -tlnp | grep -E ':(15000|15008)'
```

### 2. 性能监控
- 监控CPU和内存使用
- 检查WebSocket连接数
- 监控消息队列长度

### 3. 定期维护
- 清理旧日志文件
- 更新依赖包
- 检查配置文件完整性

## 🚨 故障排除

### 常见问题

**服务无法启动:**
```bash
# 检查配置文件
python config_manager.py

# 检查端口占用
sudo lsof -i :15000
sudo lsof -i :15008

# 检查权限
ls -la /opt/live_chat_bot
```

**设备连接失败:**
```bash
# 测试WebSocket连接
python websocket_simple.py --test

# 检查设备网络
ping device-ip-address
```

**Web界面无法访问:**
```bash
# 检查Flask服务
curl http://localhost:15008/api/health

# 检查Nginx配置
sudo nginx -t
```

## 📈 性能优化

### 1. 系统优化
- 调整消息队列大小
- 优化日志级别
- 配置合适的超时时间

### 2. 数据库优化（如适用）
- 定期清理过期数据
- 优化查询性能
- 配置索引

### 3. 网络优化
- 使用CDN加速静态资源
- 配置Gzip压缩
- 优化WebSocket连接

## 📞 技术支持

如遇到部署问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误日志
- 配置文件内容（去除敏感信息）

---

**部署完成后，请访问 Web 管理界面验证所有功能正常工作。**
