#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量创建用户脚本
"""

import json
import random
import string
from datetime import datetime, timedelta
from user_management import user_manager
from invitation_codes import invitation_manager

def generate_username(prefix="user", length=6):
    """生成用户名"""
    suffix = ''.join(random.choices(string.digits, k=length))
    return f"{prefix}{suffix}"

def generate_display_name():
    """生成随机昵称"""
    adjectives = [
        "快乐的", "聪明的", "勇敢的", "温柔的", "活泼的", "可爱的", "优雅的", "幽默的",
        "阳光的", "开朗的", "善良的", "机智的", "热情的", "友好的", "乐观的", "积极的"
    ]
    
    nouns = [
        "小猫", "小狗", "小鸟", "小熊", "小兔", "小鱼", "小象", "小狮",
        "星星", "月亮", "太阳", "彩虹", "花朵", "蝴蝶", "蜜蜂", "小树"
    ]
    
    return random.choice(adjectives) + random.choice(nouns)

def create_invitation_codes(count=50):
    """创建邀请码"""
    print(f"🎫 创建 {count} 个邀请码...")
    
    codes = []
    for i in range(count):
        result = invitation_manager.create_invitation_code(
            created_by="admin",
            valid_days=365,  # 1年有效期
            max_uses=1,      # 每个邀请码只能使用一次
            note=f"批量创建邀请码 #{i+1}"
        )
        
        if result['success']:
            codes.append(result['code'])
            if (i + 1) % 10 == 0:
                print(f"   已创建 {i+1} 个邀请码...")
        else:
            print(f"❌ 创建邀请码失败: {result['message']}")
    
    print(f"✅ 成功创建 {len(codes)} 个邀请码")
    return codes

def create_users_batch(count=20, member_ratio=0.7):
    """批量创建用户"""
    print(f"👥 批量创建 {count} 个用户...")
    
    # 先创建足够的邀请码
    invitation_codes = create_invitation_codes(count + 10)  # 多创建一些备用
    
    if len(invitation_codes) < count:
        print(f"❌ 邀请码不足，只有 {len(invitation_codes)} 个")
        return []
    
    created_users = []
    member_count = int(count * member_ratio)  # 会员用户数量
    
    for i in range(count):
        try:
            # 生成用户信息
            username = generate_username()
            password = "123456"  # 统一密码，方便测试
            display_name = generate_display_name()
            email = f"{username}@example.com"
            invitation_code = invitation_codes[i]
            
            # 创建用户
            result = user_manager.create_user(
                username=username,
                password=password,
                role="user",
                display_name=display_name,
                email=email
            )
            
            if result['success']:
                # 设置邀请码
                user_manager.set_invitation_code(username, invitation_code)
                
                # 使用邀请码
                invitation_manager.use_invitation_code(invitation_code, username)
                
                # 为部分用户开通会员
                if i < member_count:
                    # 随机会员时长：30-365天
                    member_days = random.randint(30, 365)
                    member_result = user_manager.activate_member(username, member_days)
                    if member_result['success']:
                        print(f"✅ 创建会员用户: {username} ({display_name}) - {member_days}天")
                    else:
                        print(f"✅ 创建普通用户: {username} ({display_name}) - 会员开通失败")
                else:
                    print(f"✅ 创建普通用户: {username} ({display_name})")
                
                created_users.append({
                    'username': username,
                    'password': password,
                    'display_name': display_name,
                    'email': email,
                    'is_member': i < member_count,
                    'invitation_code': invitation_code
                })
                
            else:
                print(f"❌ 创建用户失败: {result['message']}")
                
        except Exception as e:
            print(f"❌ 创建用户异常: {e}")
    
    print(f"✅ 成功创建 {len(created_users)} 个用户")
    print(f"   其中会员用户: {sum(1 for u in created_users if u['is_member'])} 个")
    print(f"   普通用户: {sum(1 for u in created_users if not u['is_member'])} 个")
    
    return created_users

def save_user_list(users, filename="created_users.json"):
    """保存用户列表到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        print(f"📁 用户列表已保存到: {filename}")
    except Exception as e:
        print(f"❌ 保存用户列表失败: {e}")

def create_demo_users():
    """创建演示用户"""
    demo_users = [
        {
            'username': 'demo001',
            'password': '123456',
            'display_name': '演示用户001',
            'email': '<EMAIL>',
            'is_member': True,
            'member_days': 90
        },
        {
            'username': 'demo002', 
            'password': '123456',
            'display_name': '演示用户002',
            'email': '<EMAIL>',
            'is_member': True,
            'member_days': 180
        },
        {
            'username': 'demo003',
            'password': '123456', 
            'display_name': '演示用户003',
            'email': '<EMAIL>',
            'is_member': False,
            'member_days': 0
        }
    ]
    
    print("🎭 创建演示用户...")
    
    # 创建邀请码
    invitation_codes = create_invitation_codes(len(demo_users))
    
    created_demo_users = []
    
    for i, user_info in enumerate(demo_users):
        try:
            invitation_code = invitation_codes[i] if i < len(invitation_codes) else None
            
            # 创建用户
            result = user_manager.create_user(
                username=user_info['username'],
                password=user_info['password'],
                role="user",
                display_name=user_info['display_name'],
                email=user_info['email']
            )
            
            if result['success']:
                # 设置邀请码
                if invitation_code:
                    user_manager.set_invitation_code(user_info['username'], invitation_code)
                    invitation_manager.use_invitation_code(invitation_code, user_info['username'])
                
                # 开通会员
                if user_info['is_member'] and user_info['member_days'] > 0:
                    member_result = user_manager.activate_member(user_info['username'], user_info['member_days'])
                    if member_result['success']:
                        print(f"✅ 创建演示会员: {user_info['username']} ({user_info['display_name']}) - {user_info['member_days']}天")
                    else:
                        print(f"✅ 创建演示用户: {user_info['username']} ({user_info['display_name']}) - 会员开通失败")
                else:
                    print(f"✅ 创建演示用户: {user_info['username']} ({user_info['display_name']})")
                
                created_demo_users.append(user_info)
                
            else:
                print(f"❌ 创建演示用户失败: {result['message']}")
                
        except Exception as e:
            print(f"❌ 创建演示用户异常: {e}")
    
    return created_demo_users

def main():
    """主函数"""
    print("👥 用户批量创建工具")
    print("=" * 50)
    
    print("请选择创建方式:")
    print("1. 创建演示用户 (3个固定用户)")
    print("2. 批量创建随机用户")
    print("3. 创建演示用户 + 批量随机用户")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    all_users = []
    
    if choice in ['1', '3']:
        # 创建演示用户
        demo_users = create_demo_users()
        all_users.extend(demo_users)
    
    if choice in ['2', '3']:
        # 批量创建用户
        try:
            count = int(input("请输入要创建的用户数量 (默认20): ") or "20")
            member_ratio = float(input("请输入会员比例 0-1 (默认0.7): ") or "0.7")
            
            if count <= 0 or count > 1000:
                print("❌ 用户数量必须在1-1000之间")
                return
            
            if member_ratio < 0 or member_ratio > 1:
                print("❌ 会员比例必须在0-1之间")
                return
            
            batch_users = create_users_batch(count, member_ratio)
            all_users.extend(batch_users)
            
        except ValueError:
            print("❌ 输入格式错误")
            return
    
    if all_users:
        # 保存用户列表
        save_user_list(all_users)
        
        print("\n" + "=" * 50)
        print("🎉 用户创建完成！")
        print(f"📊 总计创建用户: {len(all_users)} 个")
        print(f"📁 用户信息已保存到: created_users.json")
        print("\n💡 提示:")
        print("   - 所有用户的默认密码都是: 123456")
        print("   - 可以在用户管理页面查看和管理这些用户")
        print("   - 演示用户账号: demo001, demo002, demo003")
    else:
        print("❌ 没有创建任何用户")

if __name__ == "__main__":
    main()
