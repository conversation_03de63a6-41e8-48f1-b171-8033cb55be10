/* 飞船控制台科幻风格设计系统 */

:root {
  /* 专业暗蓝色系统 */
  --primary-blue: #1e3a8a;
  --secondary-blue: #1e40af;
  --accent-blue: #2563eb;
  --light-blue: #3b82f6;
  --dark-blue: #1e293b;
  --steel-blue: #334155;
  --slate-blue: #475569;

  /* 深色背景系统 */
  --space-black: #0f172a;
  --space-dark: #1e293b;
  --space-medium: #334155;
  --space-light: #475569;
  --panel-dark: #1e293b;
  --panel-medium: #334155;

  /* 专业文字颜色 */
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-accent: #60a5fa;

  /* 状态颜色 */
  --status-success: #059669;
  --status-warning: #d97706;
  --status-error: #dc2626;
  --status-info: #0284c7;
  
  /* 阴影效果 - 更加微妙 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);

  /* 边框效果 - 去除发光 */
  --border-primary: 1px solid var(--accent-blue);
  --border-secondary: 1px solid var(--steel-blue);
  --border-subtle: 1px solid var(--space-medium);

  /* 字体 */
  --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  --font-tech: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-tech);
  background: linear-gradient(135deg, var(--space-black) 0%, var(--space-dark) 50%, var(--space-medium) 100%);
  color: var(--text-primary);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* 微妙的纹理背景 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(1px 1px at 40px 60px, rgba(96, 165, 250, 0.1), transparent),
    radial-gradient(1px 1px at 120px 30px, rgba(96, 165, 250, 0.08), transparent),
    radial-gradient(1px 1px at 200px 90px, rgba(96, 165, 250, 0.06), transparent);
  background-repeat: repeat;
  background-size: 300px 200px;
  z-index: -1;
  opacity: 0.4;
}

/* 专业导航栏 */
.spaceship-navbar {
  background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
  border-bottom: var(--border-primary);
  box-shadow: var(--shadow-md);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
}

.navbar-brand-spaceship {
  font-family: var(--font-tech);
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

.navbar-brand-spaceship:hover {
  color: var(--text-accent);
}

.navbar-brand-spaceship i {
  font-size: 1.8rem;
  color: var(--accent-blue);
  transition: transform 0.3s ease;
}

.navbar-brand-spaceship:hover i {
  transform: scale(1.05);
}

/* 专业统计面板 */
.stats-console {
  padding: 2rem 0;
  max-width: 1400px;
  margin: 0 auto;
}

.stats-grid-spaceship {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-panel {
  background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
  border: var(--border-secondary);
  border-radius: 12px;
  padding: 2rem 1.5rem;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.stat-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue), var(--light-blue));
  border-radius: 12px 12px 0 0;
}

.stat-panel:hover {
  transform: translateY(-3px);
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-lg);
}

.stat-number-spaceship {
  font-family: var(--font-mono);
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  line-height: 1;
}

.stat-label-spaceship {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-label-spaceship i {
  color: var(--accent-blue);
  font-size: 1.1rem;
}

/* 主控制台布局 */
.main-console {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* 侧边控制面板 */
.control-panel {
  background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
  border: var(--border-secondary);
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 6rem;
  box-shadow: var(--shadow-md);
}

.control-section-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: var(--border-subtle);
}

.control-section-title i {
  color: var(--accent-blue);
}

/* 专业按钮 */
.btn-spaceship {
  background: linear-gradient(135deg, var(--space-dark) 0%, var(--space-medium) 100%);
  color: var(--text-primary);
  border: var(--border-secondary);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
  position: relative;
}

.btn-spaceship:hover {
  transform: translateY(-1px);
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-md);
  color: var(--text-accent);
  background: linear-gradient(135deg, var(--space-medium) 0%, var(--space-light) 100%);
}

.btn-spaceship i {
  font-size: 1rem;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 6px;
  border: var(--border-subtle);
}

.status-label {
  color: var(--text-secondary);
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.status-badge-spaceship {
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-online {
  background: var(--status-success);
  color: white;
}

.status-warning {
  background: var(--status-warning);
  color: white;
}

.status-error {
  background: var(--status-error);
  color: white;
}

/* 导航链接 */
.nav-link-spaceship {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  border: 1px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-link-spaceship:hover,
.nav-link-spaceship.active {
  background: rgba(59, 130, 246, 0.1);
  color: var(--text-accent);
  border-color: var(--accent-blue);
  transform: translateX(3px);
}

.nav-link-spaceship i {
  font-size: 1.1rem;
  color: var(--accent-blue);
}

/* 主内容卡片 */
.console-card {
  background: linear-gradient(135deg, var(--panel-dark) 0%, var(--panel-medium) 100%);
  border: var(--border-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.console-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue), var(--light-blue));
  border-radius: 12px 12px 0 0;
}

.console-card:hover {
  transform: translateY(-2px);
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-lg);
}

.console-card-header {
  background: linear-gradient(135deg, var(--space-dark) 0%, var(--space-medium) 100%);
  padding: 1.5rem;
  border-bottom: var(--border-subtle);
}

.console-card-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.console-card-title i {
  color: var(--accent-blue);
  font-size: 1.3rem;
}

.console-card-body {
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
}

/* 数据表格专业风格 */
.data-table-spaceship {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  font-family: var(--font-mono);
  font-size: 0.9rem;
}

.data-table-spaceship th {
  background: linear-gradient(135deg, var(--space-dark) 0%, var(--space-medium) 100%);
  color: var(--text-primary);
  padding: 1rem;
  text-align: left;
  border-bottom: var(--border-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.data-table-spaceship td {
  padding: 1rem;
  border-bottom: var(--border-subtle);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.data-table-spaceship tr:hover td {
  background: rgba(59, 130, 246, 0.05);
  color: var(--text-primary);
}

/* 加载动画 */
.loading-spaceship {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.loading-spinner-spaceship {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text-spaceship {
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

/* 时间显示 */
.time-display {
  font-family: var(--font-mono);
  color: var(--text-accent);
  font-size: 0.9rem;
  background: rgba(59, 130, 246, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: var(--border-secondary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-console {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .control-panel {
    position: static;
    margin-bottom: 2rem;
  }

  .stats-console {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .stats-grid-spaceship {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-panel {
    padding: 1rem;
  }

  .stat-number-spaceship {
    font-size: 2rem;
  }

  .navbar-brand-spaceship {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .stats-grid-spaceship {
    grid-template-columns: 1fr;
  }

  .main-console {
    padding: 0.5rem;
  }

  .console-card-header,
  .console-card-body {
    padding: 1rem;
  }
}
