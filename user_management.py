#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理系统 - 处理用户认证、权限管理和资源分配
"""

import json
import os
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import threading

class UserManager:
    """用户管理器"""
    
    def __init__(self, users_file: str = "data/runtime/users.json"):
        self.users_file = users_file
        self.users_data = {}
        self.sessions = {}  # 存储用户会话
        self.lock = threading.Lock()
        self.load_users()
        
        # 确保有默认管理员账户
        self.ensure_default_admin()
    
    def load_users(self):
        """加载用户数据"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    self.users_data = json.load(f)
                print(f"加载了 {len(self.users_data)} 个用户")
            else:
                self.users_data = {}
                print("用户文件不存在，创建新的用户数据")
        except Exception as e:
            print(f"加载用户数据失败: {e}")
            self.users_data = {}
    
    def save_users(self):
        """保存用户数据"""
        try:
            print(f"开始保存用户数据到 {self.users_file}")
            # 不要在这里再次获取锁，因为调用者已经获取了锁
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
            print(f"用户数据保存成功")
            return True
        except Exception as e:
            print(f"保存用户数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def ensure_default_admin(self):
        """确保存在默认管理员账户"""
        admin_username = "admin"
        if admin_username not in self.users_data:
            # 创建默认管理员账户，密码为 admin123
            admin_user = {
                "username": admin_username,
                "password_hash": self.hash_password("admin123"),
                "role": "admin",
                "display_name": "系统管理员",
                "email": "admin@localhost",
                "created_at": datetime.now().isoformat(),
                "last_login": None,
                "enabled": True,
                "assigned_rooms": [],  # 管理员可以访问所有房间
                "assigned_devices": []  # 管理员可以访问所有设备
            }
            self.users_data[admin_username] = admin_user
            self.save_users()
            print(f"创建默认管理员账户: {admin_username} / admin123")
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash_check.hex() == hash_value
        except:
            return False
    
    def create_user(self, username: str, password: str, role: str = "user", 
                   display_name: str = "", email: str = "") -> Dict[str, Any]:
        """创建用户"""
        if username in self.users_data:
            return {"success": False, "message": "用户名已存在"}
        
        if len(password) < 6:
            return {"success": False, "message": "密码长度至少6位"}
        
        if role not in ["admin", "user"]:
            return {"success": False, "message": "无效的用户角色"}
        
        user_data = {
            "username": username,
            "password_hash": self.hash_password(password),
            "role": role,
            "display_name": display_name or username,
            "email": email,
            "created_at": datetime.now().isoformat(),
            "last_login": None,
            "enabled": True,
            "assigned_rooms": [],
            "assigned_devices": [],
            # 会员相关字段
            "is_member": False,
            "member_expires_at": None,
            "member_activated_at": None,
            "invitation_code": None  # 记录使用的邀请码
        }
        
        self.users_data[username] = user_data
        if self.save_users():
            print(f"创建用户成功: {username} ({role})")
            return {"success": True, "message": "用户创建成功"}
        else:
            return {"success": False, "message": "保存用户数据失败"}

    def set_invitation_code(self, username: str, invitation_code: str) -> bool:
        """设置用户使用的邀请码"""
        if username in self.users_data:
            self.users_data[username]["invitation_code"] = invitation_code
            return self.save_users()
        return False
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """用户认证"""
        print(f"认证用户: {username}")
        print(f"用户数据库中的用户: {list(self.users_data.keys())}")

        if username not in self.users_data:
            print(f"用户不存在: {username}")
            return {"success": False, "message": "用户名或密码错误"}

        user = self.users_data[username]
        print(f"找到用户: {user}")

        if not user.get("enabled", True):
            print(f"用户已被禁用: {username}")
            return {"success": False, "message": "账户已被禁用"}

        print(f"验证密码...")
        password_valid = self.verify_password(password, user["password_hash"])
        print(f"密码验证结果: {password_valid}")

        if not password_valid:
            print(f"密码错误: {username}")
            return {"success": False, "message": "用户名或密码错误"}

        # 创建会话
        print(f"创建会话...")
        try:
            session_token = secrets.token_urlsafe(32)
            print(f"生成会话令牌: {session_token[:10]}...")

            session_data = {
                "username": username,
                "role": user["role"],
                "created_at": time.time(),
                "expires_at": time.time() + 24 * 3600  # 24小时过期
            }
            print(f"会话数据: {session_data}")

            print(f"获取锁...")
            with self.lock:
                print(f"已获取锁，保存会话...")
                self.sessions[session_token] = session_data
                print(f"更新最后登录时间...")
                # 更新最后登录时间
                self.users_data[username]["last_login"] = datetime.now().isoformat()
                print(f"保存用户数据...")
                save_result = self.save_users()
                print(f"保存结果: {save_result}")
        except Exception as e:
            print(f"创建会话异常: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"创建会话失败: {str(e)}"}

        print(f"用户登录成功: {username} ({user['role']})")
        result = {
            "success": True,
            "message": "登录成功",
            "token": session_token,
            "user": {
                "username": username,
                "role": user["role"],
                "display_name": user["display_name"]
            }
        }
        print(f"返回认证结果: {result}")
        return result

    def reset_password(self, username: str, new_password: str) -> Dict[str, Any]:
        """重置用户密码"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}

        if len(new_password) < 6:
            return {"success": False, "message": "密码长度至少6位"}

        with self.lock:
            self.users_data[username]["password_hash"] = self.hash_password(new_password)
            if self.save_users():
                print(f"✅ 重置用户密码成功: {username}")
                return {"success": True, "message": "密码重置成功"}
            else:
                return {"success": False, "message": "保存用户数据失败"}

    def set_user_status(self, username: str, enabled: bool) -> Dict[str, Any]:
        """设置用户状态"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}

        if username == "admin":
            return {"success": False, "message": "不能禁用管理员账户"}

        with self.lock:
            self.users_data[username]["enabled"] = enabled
            if self.save_users():
                action = "启用" if enabled else "禁用"
                print(f"✅ {action}用户成功: {username}")
                return {"success": True, "message": f"用户{action}成功"}
            else:
                return {"success": False, "message": "保存用户数据失败"}
    
    def verify_session(self, token: str) -> Optional[Dict[str, Any]]:
        """验证会话"""
        if not token or token not in self.sessions:
            return None
        
        session = self.sessions[token]
        if time.time() > session["expires_at"]:
            # 会话过期，删除
            with self.lock:
                del self.sessions[token]
            return None
        
        return session
    
    def logout(self, token: str) -> bool:
        """用户登出"""
        if token in self.sessions:
            with self.lock:
                del self.sessions[token]
            return True
        return False
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        print(f"DEBUG: get_user_info called with username={username}, type={type(username)}")
        if username not in self.users_data:
            print(f"DEBUG: username {username} not found in users_data")
            return None
        
        user = self.users_data[username].copy()
        # 移除敏感信息
        user.pop("password_hash", None)
        return user
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有用户列表（管理员功能）"""
        users = []
        for username, user_data in self.users_data.items():
            user_info = user_data.copy()
            user_info.pop("password_hash", None)  # 移除密码哈希
            users.append(user_info)
        return users
    
    def assign_room_to_user(self, username: str, room_id: str) -> Dict[str, Any]:
        """分配房间给用户"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}
        
        user = self.users_data[username]
        if room_id not in user["assigned_rooms"]:
            user["assigned_rooms"].append(room_id)
            if self.save_users():
                print(f"✅ 分配房间 {room_id} 给用户 {username}")
                return {"success": True, "message": "房间分配成功"}
            else:
                return {"success": False, "message": "保存数据失败"}
        else:
            return {"success": False, "message": "房间已分配给该用户"}
    
    def remove_room_from_user(self, username: str, room_id: str) -> Dict[str, Any]:
        """从用户移除房间"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}
        
        user = self.users_data[username]
        if room_id in user["assigned_rooms"]:
            user["assigned_rooms"].remove(room_id)
            if self.save_users():
                print(f"✅ 从用户 {username} 移除房间 {room_id}")
                return {"success": True, "message": "房间移除成功"}
            else:
                return {"success": False, "message": "保存数据失败"}
        else:
            return {"success": False, "message": "用户未分配该房间"}
    
    def assign_device_to_user(self, username: str, device_id: str) -> Dict[str, Any]:
        """分配设备给用户"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}
        
        user = self.users_data[username]
        if device_id not in user["assigned_devices"]:
            user["assigned_devices"].append(device_id)
            if self.save_users():
                print(f"✅ 分配设备 {device_id} 给用户 {username}")
                return {"success": True, "message": "设备分配成功"}
            else:
                return {"success": False, "message": "保存数据失败"}
        else:
            return {"success": False, "message": "设备已分配给该用户"}
    
    def remove_device_from_user(self, username: str, device_id: str) -> Dict[str, Any]:
        """从用户移除设备"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}
        
        user = self.users_data[username]
        if device_id in user["assigned_devices"]:
            user["assigned_devices"].remove(device_id)
            if self.save_users():
                print(f"✅ 从用户 {username} 移除设备 {device_id}")
                return {"success": True, "message": "设备移除成功"}
            else:
                return {"success": False, "message": "保存数据失败"}
        else:
            return {"success": False, "message": "用户未分配该设备"}
    
    def can_access_room(self, username: str, room_id: str) -> bool:
        """检查用户是否可以访问房间"""
        if username not in self.users_data:
            return False
        
        user = self.users_data[username]
        # 管理员可以访问所有房间
        if user["role"] == "admin":
            return True
        
        # 普通用户只能访问分配的房间
        return room_id in user["assigned_rooms"]
    
    def can_access_device(self, username: str, device_id: str) -> bool:
        """检查用户是否可以访问设备"""
        if username not in self.users_data:
            return False
        
        user = self.users_data[username]
        # 管理员可以访问所有设备
        if user["role"] == "admin":
            return True
        
        # 普通用户只能访问分配的设备
        return device_id in user["assigned_devices"]
    
    def get_user_rooms(self, username: str) -> List[str]:
        """获取用户可访问的房间列表"""
        if username not in self.users_data:
            return []

        user = self.users_data[username]
        if user["role"] == "admin":
            # 管理员返回所有房间（支持新旧配置格式）
            try:
                # 优先使用新的配置结构
                if os.path.exists('config/rooms'):
                    rooms = []
                    for filename in os.listdir('config/rooms'):
                        if filename.endswith('.json'):
                            live_id = filename[:-5]  # 移除.json后缀
                            rooms.append(live_id)
                    return rooms
                # 兼容旧的配置结构
                elif os.path.exists('rooms_config.json'):
                    with open('rooms_config.json', 'r', encoding='utf-8') as f:
                        rooms_config = json.load(f)
                    return list(rooms_config.keys())
            except Exception as e:
                print(f"❌ 获取管理员房间列表失败: {e}")
            return []
        else:
            return user["assigned_rooms"]
    
    def get_user_devices(self, username: str) -> List[str]:
        """获取用户可访问的设备列表"""
        if username not in self.users_data:
            return []
        
        user = self.users_data[username]
        if user["role"] == "admin":
            # 管理员返回所有设备（优先从新配置路径读取）
            try:
                if os.path.exists('config/devices/devices.json'):
                    with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                        devices_config = json.load(f)
                    return list(devices_config.keys())
                elif os.path.exists('devices_config.json'):
                    with open('devices_config.json', 'r', encoding='utf-8') as f:
                        devices_config = json.load(f)
                    return list(devices_config.keys())
            except:
                pass
            return []
        else:
            return user["assigned_devices"]

    # ==================== 会员管理方法 ====================

    def check_member_status(self, username: str) -> Dict[str, Any]:
        """检查用户会员状态"""
        if username not in self.users_data:
            return {"is_member": False, "expired": True, "message": "用户不存在"}

        user = self.users_data[username]

        # 如果不是会员
        if not user.get("is_member", False):
            return {"is_member": False, "expired": False, "message": "非会员用户"}

        # 检查会员是否过期
        member_expires_at = user.get("member_expires_at")
        if not member_expires_at:
            return {"is_member": True, "expired": False, "message": "永久会员"}

        try:
            expires_at = datetime.fromisoformat(member_expires_at)
            current_time = datetime.now()

            if current_time > expires_at:
                # 会员已过期，更新状态
                user["is_member"] = False
                self.save_users()
                return {"is_member": False, "expired": True, "expires_at": member_expires_at, "message": "会员已过期"}
            else:
                remaining_days = (expires_at - current_time).days
                return {
                    "is_member": True,
                    "expired": False,
                    "expires_at": member_expires_at,
                    "remaining_days": remaining_days,
                    "message": f"会员有效，剩余{remaining_days}天"
                }
        except Exception as e:
            print(f"❌ 检查会员状态异常: {e}")
            return {"is_member": False, "expired": True, "message": "会员状态检查失败"}

    def activate_member(self, username: str, days: int) -> Dict[str, Any]:
        """开通会员"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}

        try:
            user = self.users_data[username]
            current_time = datetime.now()

            # 如果用户已经是会员且未过期，从当前到期时间开始续期
            if user.get("is_member", False) and user.get("member_expires_at"):
                try:
                    current_expires_at = datetime.fromisoformat(user["member_expires_at"])
                    if current_time < current_expires_at:
                        # 从当前到期时间开始续期
                        new_expires_at = current_expires_at + timedelta(days=days)
                    else:
                        # 已过期，从当前时间开始
                        new_expires_at = current_time + timedelta(days=days)
                except:
                    # 解析失败，从当前时间开始
                    new_expires_at = current_time + timedelta(days=days)
            else:
                # 新开通会员，从当前时间开始
                new_expires_at = current_time + timedelta(days=days)
                user["member_activated_at"] = current_time.isoformat()

            user["is_member"] = True
            user["member_expires_at"] = new_expires_at.isoformat()

            if self.save_users():
                print(f"✅ 用户 {username} 会员开通成功，到期时间: {new_expires_at}")
                return {
                    "success": True,
                    "message": f"会员开通成功，到期时间: {new_expires_at.strftime('%Y-%m-%d %H:%M:%S')}",
                    "expires_at": new_expires_at.isoformat()
                }
            else:
                return {"success": False, "message": "保存用户数据失败"}

        except Exception as e:
            print(f"❌ 开通会员异常: {e}")
            return {"success": False, "message": f"开通会员失败: {str(e)}"}

    def deactivate_member(self, username: str) -> Dict[str, Any]:
        """取消会员"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}

        try:
            user = self.users_data[username]
            user["is_member"] = False
            user["member_expires_at"] = None

            if self.save_users():
                print(f"✅ 用户 {username} 会员已取消")
                return {"success": True, "message": "会员已取消"}
            else:
                return {"success": False, "message": "保存用户数据失败"}

        except Exception as e:
            print(f"❌ 取消会员异常: {e}")
            return {"success": False, "message": f"取消会员失败: {str(e)}"}

    def get_all_users_with_member_info(self) -> List[Dict[str, Any]]:
        """获取所有用户信息（包含会员信息）"""
        users = []
        current_time = datetime.now()

        for username, user_data in self.users_data.items():
            user_info = {
                "username": username,
                "display_name": user_data.get("display_name", username),
                "email": user_data.get("email", ""),
                "role": user_data.get("role", "user"),
                "created_at": user_data.get("created_at", ""),
                "last_login": user_data.get("last_login"),
                "enabled": user_data.get("enabled", True),
                "is_member": user_data.get("is_member", False),
                "member_expires_at": user_data.get("member_expires_at"),
                "member_activated_at": user_data.get("member_activated_at"),
                "invitation_code": user_data.get("invitation_code")
            }

            # 计算会员状态
            if user_info["is_member"] and user_info["member_expires_at"]:
                try:
                    expires_at = datetime.fromisoformat(user_info["member_expires_at"])
                    if current_time > expires_at:
                        user_info["member_status"] = "expired"
                        user_info["remaining_days"] = 0
                        # 更新数据库中的状态
                        user_data["is_member"] = False
                    else:
                        user_info["member_status"] = "active"
                        user_info["remaining_days"] = (expires_at - current_time).days
                except:
                    user_info["member_status"] = "error"
                    user_info["remaining_days"] = 0
            elif user_info["is_member"]:
                user_info["member_status"] = "permanent"
                user_info["remaining_days"] = -1
            else:
                user_info["member_status"] = "none"
                user_info["remaining_days"] = 0

            users.append(user_info)

        # 保存状态更新
        self.save_users()

        # 按创建时间倒序排列
        users.sort(key=lambda x: x["created_at"], reverse=True)
        return users

    def search_users(self, query: str) -> List[Dict[str, Any]]:
        """搜索用户"""
        if not query:
            return self.get_all_users_with_member_info()

        query = query.lower()
        all_users = self.get_all_users_with_member_info()

        filtered_users = []
        for user in all_users:
            if (query in user["username"].lower() or
                query in user["display_name"].lower() or
                query in user.get("email", "").lower()):
                filtered_users.append(user)

        return filtered_users

    def delete_user(self, username: str) -> Dict[str, Any]:
        """删除用户"""
        if username not in self.users_data:
            return {"success": False, "message": "用户不存在"}
        
        # 防止删除管理员账户
        user = self.users_data[username]
        if user.get("role") == "admin":
            return {"success": False, "message": "不能删除管理员账户"}
        
        # 防止删除最后一个管理员
        admin_count = sum(1 for u in self.users_data.values() if u.get("role") == "admin")
        if admin_count <= 1 and user.get("role") == "admin":
            return {"success": False, "message": "不能删除最后一个管理员账户"}
        
        try:
            with self.lock:
                # 清理用户相关的会话
                sessions_to_remove = []
                for token, session in self.sessions.items():
                    if session.get("username") == username:
                        sessions_to_remove.append(token)
                
                for token in sessions_to_remove:
                    del self.sessions[token]
                
                # 删除用户数据
                display_name = user.get("display_name", username)
                del self.users_data[username]
                
                if self.save_users():
                    print(f"✅ 删除用户成功: {username} ({display_name})")
                    return {
                        "success": True, 
                        "message": f"用户 {display_name} ({username}) 删除成功",
                        "deleted_user": {
                            "username": username,
                            "display_name": display_name
                        }
                    }
                else:
                    return {"success": False, "message": "保存用户数据失败"}
                    
        except Exception as e:
            print(f"❌ 删除用户异常: {e}")
            return {"success": False, "message": f"删除用户失败: {str(e)}"}

# 全局用户管理器实例
# 优先使用data/runtime/users.json，如果不存在则使用根目录的users.json
import os
runtime_users_file = "data/runtime/users.json"
default_users_file = "users.json"

if os.path.exists(runtime_users_file):
    print(f"使用运行时用户数据文件: {runtime_users_file}")
    user_manager = UserManager(runtime_users_file)
else:
    print(f"使用默认用户数据文件: {default_users_file}")
    user_manager = UserManager(default_users_file)
