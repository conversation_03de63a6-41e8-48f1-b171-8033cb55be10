nohup: ignoring input
🔍 使用运行时用户数据文件: data/runtime/users.json
✅ 加载了 29 个用户
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
📊 激活码数据已加载，共 120 个激活码
🌐 启动Flask Web服务器
📡 地址: http://0.0.0.0:15008
🔗 连接到ESP32 WebSocket服务器: ws://0.0.0.0:15000
 * Serving Flask app 'flask_web_server'
 * Debug mode: off
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:15008
 * Running on http://**************:15008
[33mPress CTRL+C to quit[0m
*************** - - [30/Jul/2025 12:44:07] "GET / HTTP/1.1" 200 -
*************** - - [30/Jul/2025 12:44:08] "[31m[1mGET /api/devices HTTP/1.1[0m" 401 -
*************** - - [30/Jul/2025 12:44:08] "GET /login HTTP/1.1" 200 -
*************** - - [30/Jul/2025 12:44:11] "POST /api/auth/login HTTP/1.1" 200 -
*************** - - [30/Jul/2025 12:44:12] "GET / HTTP/1.1" 200 -
*************** - - [30/Jul/2025 12:44:13] "GET /api/devices HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:44:28] "GET /login HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:44:32] "GET / HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:44:33] "[31m[1mGET /api/devices HTTP/1.1[0m" 401 -
************** - - [30/Jul/2025 12:44:33] "GET /login HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:44:34] "POST /api/auth/login HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:44:36] "GET / HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:44:36] "GET /api/devices HTTP/1.1" 200 -
🔍 收到登录请求
🔍 请求数据: {'username': 'admin', 'password': 'admin123'}
🔍 用户名: admin, 密码长度: 8
🔍 调用用户管理器认证
🔍 认证用户: admin
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'admin', 'password_hash': 'c0a92f77289bcc65be261d741c9f83ba:17542ee8b6429565152298302f4e559d7076564dc208612df6391018becc139e', 'role': 'admin', 'display_name': '系统管理员', 'email': 'admin@localhost', 'created_at': '2025-07-25T00:11:16.418449', 'last_login': '2025-07-30T02:30:51.471666', 'enabled': True, 'assigned_rooms': ['211953907443'], 'assigned_devices': ['30:ed:a0:29:f0:a4', '10:20:ba:cd:44:ac', '10:20:ba:cd:07:18', '10:20:ba:cf:5f:6c', '10:20:ba:cd:5d:84', '10:20:ba:cf:5f:c0', '10:20:ba:cd:0a:58', '10:20:ba:cd:2b:6c', '10:20:ba:cf:5f:58']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: CLiNuHt1KG...
🔍 会话数据: {'username': 'admin', 'role': 'admin', 'created_at': 1753850651.7301388, 'expires_at': 1753937051.7301397}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: admin (admin)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'CLiNuHt1KGJZCu7F3xFCUmyxaHs4tV4GEp3ru8Cv9f4', 'user': {'username': 'admin', 'role': 'admin', 'display_name': '系统管理员'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'CLiNuHt1KGJZCu7F3xFCUmyxaHs4tV4GEp3ru8Cv9f4', 'user': {'username': 'admin', 'role': 'admin', 'display_name': '系统管理员'}}
🔍 从配置文件加载到 26 个设备
🔍 用户 admin (admin) 请求设备列表，共 26 个设备
🔍 管理员用户，返回所有 26 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
🔍 管理员用户，返回 19 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
🔍 管理员用户，返回 19 个房间
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
📊 全局互动排行榜: 107 位用户有互动记录
🔍 收到登录请求
🔍 请求数据: {'username': 'kanshijie', 'password': '123456'}
🔍 用户名: kanshijie, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: kanshijie
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'kanshijie', 'password_hash': '3eb241a9e6d5c6be73d2c4b2d0f66fca:b53dffcb52a8e20c76a80531b980017313bbc4d130ac912c812dff60ff7085a4', 'role': 'user', 'display_name': 'kanshijie', 'email': '', 'created_at': '2025-07-30T00:06:04.218034', 'last_login': '2025-07-30T08:39:57.048510', 'enabled': True, 'assigned_rooms': ['7212598169799085351&type=general&ug_source=hw_dy_57dh'], 'assigned_devices': ['10:20:ba:cf:62:74']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: 5Dw0x5tPwH...
🔍 会话数据: {'username': 'kanshijie', 'role': 'user', 'created_at': 1753850674.924889, 'expires_at': 1753937074.9248898}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: kanshijie (user)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': '5Dw0x5tPwHNyz_JUkADFnKay3-jiJm_tTPCnwFOtjs4', 'user': {'username': 'kanshijie', 'role': 'user', 'display_name': 'kanshijie'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': '5Dw0x5tPwHNyz_JUkADFnKay3-jiJm_tTPCnwFOtjs4', 'user': {'username': 'kanshijie', 'role': 'user', 'display_name': 'kanshijie'}}
🔍 从配置文件加载到 26 个设备
🔍 用户 kanshijie (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cf:62:74']，过滤后: 1 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 kanshijie (user) 请求房间列表
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 kanshijie (user) 请求房间列表
🔍 管理员用户，返回 1 个房间
🔍 调试信息 - 请求房间ID: 200382305200
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
*************** - - [30/Jul/2025 12:44:36] "POST /api/rooms/200382305200/start HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:44:38] "POST /api/auth/login HTTP/1.1" 200 -
🔍 调试信息 - 找到的配置: {'live_id': '200382305200', 'room_name': '苦瓜测试', 'target_device_id': '30:ed:a0:29:f0:a4', 'enabled': True, 'created_at': '2025-07-29T23:14:51.324173', 'created_by': 'admin', 'activation_code': 'KNWHALQ7YEBY', 'welcome_message': {'enabled': True, 'cooldown_seconds': 5, 'templates': ['欢迎{user_name}进入直播间！', '{user_name}来了，大家欢迎！']}, 'like_interaction': {'enabled': True, 'cooldown_seconds': 60, 'first_like_threshold': 1, 'subsequent_like_threshold': 100, 'templates': ['{user_name}说给你点赞了 夸夸她', '{user_name}说给你点个赞 你是最棒的', '{user_name}给你点赞了 给他推销一下']}, 'follow_interaction': {'enabled': True, 'response_templates': ['哟 {user_name}关注了我，是不是看上我了？', '{user_name}终于关注了，眼光还不错嘛', '欢迎{user_name}，不过关注了可别后悔哦', '{user_name}关注了我，算你有品味', '哟{user_name}，关注我是你做过最明智的决定']}, 'message_preprocess': {'enabled': True, 'filter_emoji': True, 'filter_special_chars': True, 'max_length': 200, 'min_length': 1, 'blocked_words': [], 'replacement_rules': {}}, 'anti_spam': {'enabled': True, 'duplicate_detection': {}, 'frequency_limit': {}, 'warning_messages': ['别刷了别刷了 再刷屏就坏掉了'], 'block_duration_seconds': 30, 'warning_cooldown_seconds': 30}, 'gift_priority': {'enabled': True, 'priority_message_count': 2, 'gift_response_templates': ['哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧'], 'gift_weights': {'玫瑰': 3, '小心心': 3, '跑车': 10, '火箭': 50, '嘉年华': 100, '默认': 3}}, 'scheduled_broadcast': {'enabled': True, 'interval_minutes': 10, 'templates': ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！']}, 'anti_silence': {'enabled': True, 'delay_seconds': 5, 'cooldown_seconds': 5, 'max_proactive_attempts': 2, 'templates': ['介绍一下自己'], 'personalized_templates': ['{user_name} 跑哪儿去了']}, 'custom_reply': {'enabled': True, 'priority_weight': 8, 'match_mode': 'contains', 'case_sensitive': False, 'cooldown_seconds': 5, 'rules': [{'id': 'question', 'keywords': ['怎么卖'], 'responses': ['用户现在想知道怎么下单 直接告诉用户看主页艾特的硅灵造物店铺'], 'enabled': True}]}, 'interactive_games': {'enabled': True, 'cooldown_seconds': 30, 'auto_trigger': True, 'auto_interval_minutes': 5, 'games': {'guess_number': {'enabled': True, 'range': [1, 100]}, 'word_chain': {'enabled': True, 'difficulty': 'medium'}, 'riddle': {'enabled': True, 'category': 'all'}}}}
✅ 房间 200382305200 激活码验证通过: 激活码有效，剩余 29 天
🚀 准备启动房间: 200382305200 (苦瓜测试) -> 30:ed:a0:29:f0:a4
🚀 启动直播间: 200382305200 (苦瓜测试) -> 30:ed:a0:29:f0:a4
⚠️ 未找到图形终端，使用后台进程模式
✅ 直播间 200382305200 (苦瓜测试) 已启动
🔧 进程ID: 105150
🎬 启动房间监控: 200382305200
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
🔍 管理员用户，返回 19 个房间
🔍 收到登录请求
🔍 请求数据: {'username': 'admin', 'password': 'admin123'}
🔍 用户名: admin, 密码长度: 8
🔍 调用用户管理器认证
🔍 认证用户: admin
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'admin', 'password_hash': 'c0a92f77289bcc65be261d741c9f83ba:17542ee8b6429565152298302f4e559d7076564dc208612df6391018becc139e', 'role': 'admin', 'display_name': '系统管理员', 'email': 'admin@localhost', 'created_at': '2025-07-25T00:11:16.418449', 'last_login': '2025-07-30T12:44:11.730164', 'enabled': True, 'assigned_rooms': ['211953907443'], 'assigned_devices': ['30:ed:a0:29:f0:a4', '10:20:ba:cd:44:ac', '10:20:ba:cd:07:18', '10:20:ba:cf:5f:6c', '10:20:ba:cd:5d:84', '10:20:ba:cf:5f:c0', '10:20:ba:cd:0a:58', '10:20:ba:cd:2b:6c', '10:20:ba:cf:5f:58']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: wepQG81XS4...
🔍 会话数据: {'username': 'admin', 'role': 'admin', 'created_at': 1753850678.4008832, 'expires_at': 1753937078.400884}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: admin (admin)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'wepQG81XS4zT95KE3RFBPKMfouG-z2m-Zl1ednaOLbc', 'user': {'username': 'admin', 'role': 'admin', 'display_name': '系统管理员'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'wepQG81XS4zT95KE3RFBPKMfouG-z2m-Zl1ednaOLbc', 'user': {'username': 'admin', 'role': 'admin', 'display_name': '系统管理员'}}
🔍 调试信息 - 请求房间ID: 7212598169799085351&type=general&ug_source=hw_dy_57dh
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
************* - - [30/Jul/2025 12:44:38] "POST /api/rooms/7212598169799085351&type=general&ug_source=hw_dy_57dh/start HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:44:39] "GET / HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:44:39] "GET /api/devices HTTP/1.1" 200 -
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
🚀 正在启动抖音直播聊天机器人...
📋 使用配置文件: config_7212598169799085351&type=general&ug_source=hw_dy_57dh.json
🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动
   - 只有在 'listening' 或 'idle' 状态时才发送消息
   - 在 'speaking' 状态时消息会自动加入队列
   - 说话结束后自动处理队列中的消息
   - 队列空闲超过1秒自动发送防冷场消息
   - 检测点赞事件并主动与点赞用户互动
   - 避免设备冲突，确保消息发送时机合适
   - 支持消息队列状态查看和手动处理

✅ 配置文件加载成功: config_7212598169799085351&type=general&ug_source=hw_dy_57dh.json
🤖 抖音直播聊天机器人 - 增强版
============================================================
⚠️ 配置项 'logging.show_room_id' 不存在，使用默认值: True
⚠️ 配置项 'custom_reply' 不存在，使用默认值: {}
📝 自定义回复配置加载完成: 禁用, 0条规则
🏠 房间ID: 7212598169799085351&type=general&ug_source=hw_dy_57dh
⚠️ 配置项 'scheduled_broadcast.enabled' 不存在，使用默认值: True
⚠️ 配置项 'scheduled_broadcast.interval_minutes' 不存在，使用默认值: 10
⚠️ 配置项 'scheduled_broadcast.templates' 不存在，使用默认值: ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！', '感谢大家的支持，让我们一起聊天互动吧！', '别忘了给主播点个小心心，你的支持是我最大的动力！', '新来的朋友记得关注一下，不迷路哦！']
⚠️ 配置项 'welcome_message.enabled' 不存在，使用默认值: True
⚠️ 配置项 'welcome_message.cooldown_seconds' 不存在，使用默认值: 0
⚠️ 配置项 'welcome_message.templates' 不存在，使用默认值: []
📋 配置已加载:
   📺 直播间ID: 7212598169799085351&type=general&ug_source=hw_dy_57dh
   📡 WebSocket地址: ws://localhost:15000
   🎯 目标设备ID: 10:20:ba:cf:62:74
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📦 消息队列: 启用
   🔄 状态监控: 启用
🔄 状态监控线程已启动，检查间隔: 1秒
🔄 配置重载监控已启动
⚠️ 配置项 'follow_interaction.enabled' 不存在，使用默认值: True
⚠️ 配置项 'follow_interaction.response_templates' 不存在，使用默认值: []

🔍 检查ESP32设备状态...
❌ ESP32设备离线: 10:20:ba:cf:62:74
⚠️ ESP32设备离线，但仍会继续监控直播间
💡 请确保ESP32设备已连接并运行mcp_wake服务

📋 当前设置:
   🔄 自动回复: 启用
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📊 消息长度限制: 1-100字符
【X】No match found for roomId
❌ 启动失败: 'NoneType' object has no attribute 'group'
************ - - [30/Jul/2025 12:44:47] "GET / HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:44:48] "[31m[1mGET /api/devices HTTP/1.1[0m" 401 -
************ - - [30/Jul/2025 12:44:48] "GET /login HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:44:48] "GET /login HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:44:50] "POST /api/auth/login HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:44:51] "GET / HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:44:51] "GET /api/devices HTTP/1.1" 200 -
🔍 调试信息 - 找到的配置: {'live_id': '7212598169799085351&type=general&ug_source=hw_dy_57dh', 'room_name': '墙头看世界', 'target_device_id': '10:20:ba:cf:62:74', 'enabled': True, 'created_at': '2025-07-30T08:44:35.865246', 'created_by': 'kanshijie', 'activation_code': '5KP6NBA749R6', 'welcome_message': {'enabled': True, 'cooldown_seconds': 1, 'templates': []}, 'custom_reply': {'enabled': True, 'priority_weight': 8, 'match_mode': 'contains', 'case_sensitive': False, 'cooldown_seconds': 5, 'rules': []}, 'anti_spam': {'enabled': True, 'duplicate_detection': {'enabled': True, 'time_window_seconds': 60, 'max_duplicates': 3, 'similarity_threshold': 0.8}, 'frequency_limit': {'enabled': True, 'time_window_seconds': 30, 'max_messages': 5}, 'warning_messages': ['{user_name}你个刷屏怪，再刷屏把你关小黑屋了！', '{user_name}别刷屏了，给其他人一点发言机会！', '{user_name}刷屏可不是好习惯，消停点！', '{user_name}你这样刷屏很影响直播间秩序哦！'], 'block_duration_seconds': 300, 'warning_cooldown_seconds': 60}, 'message_preprocess': {'enabled': True, 'filter_emoji': True, 'filter_special_chars': True, 'max_length': 200, 'min_length': 1, 'blocked_words': [], 'replacement_rules': {}}, 'like_interaction': {'enabled': True, 'cooldown_seconds': 60, 'first_like_threshold': 1, 'subsequent_like_threshold': 100, 'templates': []}, 'gift_priority': {'enabled': True, 'priority_message_count': 2, 'gift_response_templates': ['哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧'], 'gift_weights': {'玫瑰': 3, '小心心': 3, '跑车': 10, '火箭': 50, '嘉年华': 100, '默认': 3}}, 'scheduled_broadcast': {'enabled': True, 'interval_minutes': 10, 'templates': ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！']}, 'interactive_games': {'enabled': True, 'cooldown_seconds': 30, 'auto_trigger': True, 'auto_interval_minutes': 5, 'games': {'guess_number': {'enabled': True, 'range': [1, 100]}, 'word_chain': {'enabled': True, 'difficulty': 'medium'}, 'riddle': {'enabled': True, 'category': 'all'}}}, 'anti_silence': {'enabled': True, 'delay_seconds': 30, 'cooldown_seconds': 50, 'max_proactive_attempts': 2, 'templates': ['现在直播间有点冷场，请主动和大家打个招呼', '请和观众们互动一下，营造温暖的氛围', '可以主动找个有趣的话题和大家聊聊'], 'personalized_templates': ['{user_name}，刚才聊得挺开心的，还有什么想聊的吗？', '嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！', '{user_name}，直播间有点安静了，来活跃一下气氛吧！', '{user_name}，有什么问题或想法都可以说出来哦！']}, 'follow_interaction': {'enabled': True, 'response_templates': ['哟 {user_name}关注了我，是不是看上我了？', '{user_name}终于关注了，眼光还不错嘛', '欢迎{user_name}，不过关注了可别后悔哦', '{user_name}关注了我，算你有品味', '哟{user_name}，关注我是你做过最明智的决定']}}
✅ 房间 7212598169799085351&type=general&ug_source=hw_dy_57dh 激活码验证通过: 激活码有效，剩余 299 天
🚀 准备启动房间: 7212598169799085351&type=general&ug_source=hw_dy_57dh (墙头看世界) -> 10:20:ba:cf:62:74
🚀 启动直播间: 7212598169799085351&type=general&ug_source=hw_dy_57dh (墙头看世界) -> 10:20:ba:cf:62:74
⚠️ 未找到图形终端，使用后台进程模式
✅ 直播间 7212598169799085351&type=general&ug_source=hw_dy_57dh (墙头看世界) 已启动
🔧 进程ID: 105161
🎬 启动房间监控: 7212598169799085351&type=general&ug_source=hw_dy_57dh
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 kanshijie (user) 请求房间列表
🔍 管理员用户，返回 1 个房间
🔍 从配置文件加载到 26 个设备
🔍 用户 admin (admin) 请求设备列表，共 26 个设备
🔍 管理员用户，返回所有 26 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
🔍 管理员用户，返回 19 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
🔍 管理员用户，返回 19 个房间
🔍 收到登录请求
🔍 请求数据: {'username': 'xiechaojun', 'password': '123456'}
🔍 用户名: xiechaojun, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: xiechaojun
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'xiechaojun', 'password_hash': '6b0e53a5f503299130981da1625ef8ab:33f422adb6bc5b2664f521d73e35af487a56f53cdec2f2f7122d7e5e7eec2085', 'role': 'user', 'display_name': 'xiechaojun', 'email': '', 'created_at': '2025-07-26T19:06:07.255019', 'last_login': '2025-07-30T05:47:58.908479', 'enabled': True, 'assigned_rooms': ['475343222698'], 'assigned_devices': ['10:20:ba:cd:72:5c']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: wGqU7tIIpy...
🔍 会话数据: {'username': 'xiechaojun', 'role': 'user', 'created_at': 1753850690.5266557, 'expires_at': 1753937090.5266566}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: xiechaojun (user)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'wGqU7tIIpyr0n1lQkiiIyYdWX1PxWeRSElqUIiuEyqg', 'user': {'username': 'xiechaojun', 'role': 'user', 'display_name': 'xiechaojun'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'wGqU7tIIpyr0n1lQkiiIyYdWX1PxWeRSElqUIiuEyqg', 'user': {'username': 'xiechaojun', 'role': 'user', 'display_name': 'xiechaojun'}}
🔍 从配置文件加载到 26 个设备
🔍 用户 xiechaojun (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cd:72:5c']，过滤后: 1 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
💾 开始保存数据: 113 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
🔍 调试信息 - 请求房间ID: 739580848508
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
🔍 调试信息 - 找到的配置: {'live_id': '739580848508', 'room_name': '东哥哥号', 'target_device_id': '34:cd:b0:b3:df:28', 'enabled': True, 'created_at': '2025-07-30T04:50:39.205453', 'created_by': 'admin', 'activation_code': 'MJZRKAU5WWY7'}
✅ 房间 739580848508 激活码验证通过: 激活码有效，剩余 299 天
🚀 准备启动房间: 739580848508 (东哥哥号) -> 34:cd:b0:b3:df:28
🚀 启动直播间: 739580848508 (东哥哥号) -> 34:cd:b0:b3:df:28
⚠️ 未找到图形终端，使用后台进程模式
************** - - [30/Jul/2025 12:45:03] "POST /api/rooms/739580848508/start HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:06] "[31m[1mGET /api/devices HTTP/1.1[0m" 401 -
182.245.189.204 - - [30/Jul/2025 12:45:06] "GET /login HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:45:14] "[31m[1mPOST /api/rooms/875206794749/start HTTP/1.1[0m" 401 -
************* - - [30/Jul/2025 12:45:16] "GET /login HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:17] "POST /api/auth/login HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:45:18] "GET / HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:45:18] "GET /api/devices HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:18] "GET / HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:19] "GET /api/devices HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:25] "GET /api/devices HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:45:32] "GET / HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:45:32] "GET /api/devices HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:45:35] "POST /api/auth/login HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:45:37] "GET / HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:45:37] "POST /api/devices/10:20:ba:cf:5f:58/wake HTTP/1.1" 200 -
************* - - [30/Jul/2025 12:45:38] "GET /api/devices HTTP/1.1" 200 -
✅ 直播间 739580848508 (东哥哥号) 已启动
🔧 进程ID: 105211
🎬 启动房间监控: 739580848508
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
📊 更新房间 739580848508 的官方排行榜，共 3 位用户
📊 通过API更新房间 739580848508 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
🔍 收到登录请求
🔍 请求数据: {'username': 'heyuan', 'password': '123456'}
🔍 用户名: heyuan, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: heyuan
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'heyuan', 'password_hash': '15112057ebe1613134503a5150dca225:116b48439bdc1a60f96e8ab5bef0c1ac2b421e52b9c7dd54ea8709db4bef9736', 'role': 'user', 'display_name': 'heyuan', 'email': '', 'created_at': '2025-07-26T19:00:45.249362', 'last_login': '2025-07-30T09:16:53.301876', 'enabled': True, 'assigned_rooms': ['431879348173'], 'assigned_devices': ['10:20:ba:cf:5f:58']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: REguI0bDKK...
🔍 会话数据: {'username': 'heyuan', 'role': 'user', 'created_at': 1753850717.6709104, 'expires_at': 1753937117.6709113}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: heyuan (user)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'REguI0bDKKR7DJw0fH4LdL-gcvRh3RPc5-gl_Fe6Tlw', 'user': {'username': 'heyuan', 'role': 'user', 'display_name': 'heyuan'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'REguI0bDKKR7DJw0fH4LdL-gcvRh3RPc5-gl_Fe6Tlw', 'user': {'username': 'heyuan', 'role': 'user', 'display_name': 'heyuan'}}
🔍 收到房间列表请求
🔍 从配置文件加载到 26 个设备
🔍 用户 admin (admin) 请求设备列表，共 26 个设备
🔍 管理员用户，返回所有 26 个设备
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
🔍 从配置文件加载到 26 个设备
🔍 用户 heyuan (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cf:5f:58']，过滤后: 1 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 heyuan (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 heyuan (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 从配置文件加载到 26 个设备
🔍 用户 heyuan (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cf:5f:58']，过滤后: 1 个设备
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
🔍 从配置文件加载到 26 个设备
🔍 用户 admin (admin) 请求设备列表，共 26 个设备
🔍 管理员用户，返回所有 26 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
🔍 收到登录请求
🔍 请求数据: {'username': 'fanjie', 'password': '123456'}
🔍 用户名: fanjie, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: fanjie
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'fanjie', 'password_hash': '7a8a1e66de1cda623781b61e7f3997bc:987aa091b3d22c7f2223915c618738302e0ef440a129bf1d3d17b319afe2f4d7', 'role': 'user', 'display_name': 'fanjie', 'email': '', 'created_at': '2025-07-29T23:29:30.223601', 'last_login': '2025-07-30T11:52:40.490419', 'enabled': True, 'assigned_rooms': ['875206794749'], 'assigned_devices': ['10:20:ba:cf:63:ec']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: QANSsV0fcL...
🔍 会话数据: {'username': 'fanjie', 'role': 'user', 'created_at': 1753850735.2344408, 'expires_at': 1753937135.2344418}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: fanjie (user)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'QANSsV0fcLsQztloSnqjwflLalf-F_HRGrjsWQDOtjI', 'user': {'username': 'fanjie', 'role': 'user', 'display_name': 'fanjie'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'QANSsV0fcLsQztloSnqjwflLalf-F_HRGrjsWQDOtjI', 'user': {'username': 'fanjie', 'role': 'user', 'display_name': 'fanjie'}}
🔍 用户 heyuan 唤醒设备 10:20:ba:cf:5f:58，消息: 设备唤醒测试
🔧 强制模式：等待设备 10:20:ba:cf:5f:58 锁...
🔒 已获取设备 10:20:ba:cf:5f:58 发送锁
🔍 发送唤醒消息到WebSocket: {'type': 'wake_device', 'data': {'device_id': '10:20:ba:cf:5f:58', 'message': '设备唤醒测试', 'force': True, 'user': 'heyuan'}}
🔍 收到WebSocket响应: {'type': 'wake_response', 'data': {'success': True, 'message': 'Wake command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🔓 已释放设备 10:20:ba:cf:5f:58 发送锁
🔍 收到房间列表请求
🔍 从配置文件加载到 26 个设备
🔍 用户 fanjie (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cf:63:ec']，过滤后: 1 个设备
🔍 从配置文件加载到 19 个房间
🔍 用户 fanjie (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
************* - - [30/Jul/2025 12:45:39] "POST /api/rooms/875206794749/start HTTP/1.1" 200 -
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
🚀 正在启动抖音直播聊天机器人...
📋 使用配置文件: config_200382305200.json
🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动
   - 只有在 'listening' 或 'idle' 状态时才发送消息
   - 在 'speaking' 状态时消息会自动加入队列
   - 说话结束后自动处理队列中的消息
   - 队列空闲超过1秒自动发送防冷场消息
   - 检测点赞事件并主动与点赞用户互动
   - 避免设备冲突，确保消息发送时机合适
   - 支持消息队列状态查看和手动处理

✅ 配置文件加载成功: config_200382305200.json
🤖 抖音直播聊天机器人 - 增强版
============================================================
⚠️ 配置项 'logging.show_room_id' 不存在，使用默认值: True
⚠️ 配置项 'custom_reply' 不存在，使用默认值: {}
📝 自定义回复配置加载完成: 禁用, 0条规则
🏠 房间ID: 200382305200
⚠️ 配置项 'scheduled_broadcast.enabled' 不存在，使用默认值: True
⚠️ 配置项 'scheduled_broadcast.interval_minutes' 不存在，使用默认值: 10
⚠️ 配置项 'scheduled_broadcast.templates' 不存在，使用默认值: ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！', '感谢大家的支持，让我们一起聊天互动吧！', '别忘了给主播点个小心心，你的支持是我最大的动力！', '新来的朋友记得关注一下，不迷路哦！']
⚠️ 配置项 'welcome_message.enabled' 不存在，使用默认值: True
⚠️ 配置项 'welcome_message.cooldown_seconds' 不存在，使用默认值: 0
⚠️ 配置项 'welcome_message.templates' 不存在，使用默认值: []
📋 配置已加载:
   📺 直播间ID: 200382305200
   📡 WebSocket地址: ws://localhost:15000
   🎯 目标设备ID: 30:ed:a0:29:f0:a4
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📦 消息队列: 启用
   🔄 状态监控: 启用
🔄 状态监控线程已启动，检查间隔: 1秒
🔄 配置重载监控已启动
⚠️ 配置项 'follow_interaction.enabled' 不存在，使用默认值: True
⚠️ 配置项 'follow_interaction.response_templates' 不存在，使用默认值: []

🔍 检查ESP32设备状态...
✅ ESP32设备在线: 30:ed:a0:29:f0:a4
📊 设备状态: idle
💡 状态说明: 空闲 - 可以发送消息
💡 当前状态: 空闲 - 可以发送消息
✅ 设备状态正常，可以发送消息

📋 当前设置:
   🔄 自动回复: 启用
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📊 消息长度限制: 1-100字符
【苦瓜】[101847692837]直播间：已结束.

🎯 开始监控直播间聊天消息...
💬 符合条件的聊天消息将自动通过ESP32回复
🔍 系统会自动检查设备状态，确保在合适时机发送消息
⏹️ 按Ctrl+C停止监控
============================================================
【√】WebSocket连接成功.
【直播间统计msg】0在线观众
🔍 收到排行榜消息，共 0 位用户
【直播间排行榜msg】排行榜为空
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 5秒
🎭 冷场回复冷却: 5秒
🎭 房间配置路径: config/rooms/200382305200.json['anti_silence']
🔄 已重新加载房间 200382305200 的配置（新格式）
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【直播间统计msg】2在线观众
【进场msg】[100397619549][女]用户723542324 进入了直播间
👋 检测到用户进入: 用户723542324 (女)
👋 准备发送欢迎消息: 用户723542324来了，大家欢迎！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[305200] [12:45:16] 🔥 优先消息已加入队列[位置0]: 用户723542324: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
[305200] [12:45:16] 📦 处理队列: 1条消息
[305200] [12:45:16] 🔥 发现 1 条优先消息（礼物回复等）
[305200] [12:45:16] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:45:16] 🔥 队列[1/1] 用户723542: 进入直播间...[优先] (0.0s)
[305200] [12:45:16] 👋 正在处理欢迎消息: 用户723542
✅ 设备就绪，状态: idle
🔍 发送调用[1]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[1]: 用户723542324来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "用户723542324来了，大家欢迎！...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 用户723542324来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
🎧 设备进入listening状态，重置防冷场计时器
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 1
[305200] [12:45:27] ✅ 队列消息发送成功: 用户723542
✅ 队列处理完成，共处理 1 条消息
【进场msg】[2827549571559292][女]用户4678755385837HH 进入了直播间
👋 检测到用户进入: 用户4678755385837HH (女)
👋 准备发送欢迎消息: 用户4678755385837来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 用户4678755385837HH
   设备状态: speaking
   欢迎内容: 用户4678755385837来了，大家欢迎！
[305200] [12:45:27] 🔥 优先消息已加入队列[位置0]: 用户4678755385837HH: 进入直播间 (权重:1)
[305200] [12:45:27] 📦 欢迎消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 用户4678755385837HH, score_str=''
【排行榜】第3名: 用户4678755385837HH - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【直播间统计msg】3在线观众
【进场msg】[3280541550386296][女]用户7122621336150 进入了直播间
👋 检测到用户进入: 用户7122621336150 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 用户7122621336150
【进场msg】[2586518077325208][男]十里故清欢 进入了直播间
👋 检测到用户进入: 十里故清欢 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 十里故清欢
【直播间统计msg】5在线观众
【进场msg】[1631284001519934][男]让往事随风 进入了直播间
👋 检测到用户进入: 让往事随风 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 让往事随风
【直播间统计msg】4在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 用户7122621336150, score_str=''
【排行榜】第3名: 用户7122621336150 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
💾 开始保存数据: 113 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
【直播间统计msg】3在线观众
************** - - [30/Jul/2025 12:46:08] "GET / HTTP/1.1" 200 -
************** - - [30/Jul/2025 12:46:09] "GET /api/devices HTTP/1.1" 200 -
182.245.189.204 - - [30/Jul/2025 12:46:19] "POST /api/rooms/431879348173/start HTTP/1.1" 200 -
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
🚀 正在启动抖音直播聊天机器人...
📋 使用配置文件: config_431879348173.json
🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动
   - 只有在 'listening' 或 'idle' 状态时才发送消息
   - 在 'speaking' 状态时消息会自动加入队列
   - 说话结束后自动处理队列中的消息
   - 队列空闲超过1秒自动发送防冷场消息
   - 检测点赞事件并主动与点赞用户互动
   - 避免设备冲突，确保消息发送时机合适
   - 支持消息队列状态查看和手动处理

✅ 配置文件加载成功: config_431879348173.json
🤖 抖音直播聊天机器人 - 增强版
============================================================
⚠️ 配置项 'logging.show_room_id' 不存在，使用默认值: True
⚠️ 配置项 'custom_reply' 不存在，使用默认值: {}
📝 自定义回复配置加载完成: 禁用, 0条规则
🏠 房间ID: 431879348173
⚠️ 配置项 'message_preprocess' 不存在，使用默认值: {}
⚠️ 配置项 'scheduled_broadcast' 不存在，使用默认值: {}
⚠️ 配置项 'scheduled_broadcast.enabled' 不存在，使用默认值: True
⚠️ 配置项 'scheduled_broadcast.interval_minutes' 不存在，使用默认值: 10
⚠️ 配置项 'scheduled_broadcast.templates' 不存在，使用默认值: ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！', '感谢大家的支持，让我们一起聊天互动吧！', '别忘了给主播点个小心心，你的支持是我最大的动力！', '新来的朋友记得关注一下，不迷路哦！']
⚠️ 配置项 'welcome_message.enabled' 不存在，使用默认值: True
⚠️ 配置项 'welcome_message.cooldown_seconds' 不存在，使用默认值: 0
⚠️ 配置项 'welcome_message.templates' 不存在，使用默认值: []
📋 配置已加载:
   📺 直播间ID: 431879348173
   📡 WebSocket地址: ws://localhost:15000
   🎯 目标设备ID: 10:20:ba:cf:5f:58
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📦 消息队列: 启用
   🔄 状态监控: 启用
🔄 状态监控线程已启动，检查间隔: 1秒
🔄 配置重载监控已启动
⚠️ 配置项 'follow_interaction' 不存在，使用默认值: {}
⚠️ 配置项 'follow_interaction.enabled' 不存在，使用默认值: True
⚠️ 配置项 'follow_interaction.response_templates' 不存在，使用默认值: []

🔍 检查ESP32设备状态...
🎧 设备进入listening状态，重置防冷场计时器
✅ ESP32设备在线: 10:20:ba:cf:5f:58
📊 设备状态: listening
💡 状态说明: 监听中 - 可以发送消息
💡 当前状态: 监听中 - 可以发送消息
✅ 设备状态正常，可以发送消息

📋 当前设置:
   🔄 自动回复: 启用
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📊 消息长度限制: 1-100字符
🎭 冷场检查: 无活动时间=2.0s, 需要=3s, 上次冷场=1753850782.9s, 冷却=20s
🎭 冷场检查: 无活动时间=3.0s, 需要=3s, 上次冷场=1753850783.9s, 冷却=20s
🎭 检测到冷场：listening状态 3.0秒无活动，触发防冷场消息
💬 生成通用防冷场消息
🎭 发送防冷场消息: 请和观众们互动一下，营造温暖的氛围
🎭 最终格式: 请和观众们互动一下，营造温暖的氛围 (长度:17)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
[348173] [12:46:23] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
[348173] [12:46:23] 📦 处理队列: 1条消息
[348173] [12:46:23] 🔥 发现 1 条优先消息（礼物回复等）
[348173] [12:46:23] 📊 队列状态: 1条消息，本次处理上限: 1条
[348173] [12:46:23] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
[348173] [12:46:23] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[1]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[1]: 请和观众们互动一下，营造温暖的氛围
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "请和观众们互动一下，营造温暖的氛围",...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 请和观众们互动一下，营造温暖的氛围
✅ 发送成功
⏳ 等待设备完成说话...
【和元Ai心疗】[4332506120721343]直播间：正在直播.

🎯 开始监控直播间聊天消息...
💬 符合条件的聊天消息将自动通过ESP32回复
🔍 系统会自动检查设备状态，确保在合适时机发送消息
⏹️ 按Ctrl+C停止监控
============================================================
【√】WebSocket连接成功.
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 柒柒, score_str=''
【排行榜】第1名: 柒柒 - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【直播间统计msg】8在线观众
【进场msg】[104953920160][男]自信洋洋 进入了直播间
👋 检测到用户进入: 自信洋洋 (男)
👋 准备发送欢迎消息: 自信洋洋来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 自信洋洋
   设备状态: speaking
   欢迎内容: 自信洋洋来了，大家欢迎！
[348173] [12:46:26] 🔥 优先消息已加入队列[位置0]: 自信洋洋: 进入直播间 (权重:1)
[348173] [12:46:26] 📦 欢迎消息已加入队列等待处理
[348173] [12:46:26] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): 666
[348173] [12:46:26] 🕐 消息时间检查: 启动后 6.3s, 过滤启用: True
[348173] [12:46:26] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 1)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> 66...
[348173] [12:46:26] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:46:26]    用户消息: 久之艾美容店(官渡区新亚洲体育城店): 66
[348173] [12:46:26]    设备状态: speaking
🆕 新用户首次发言: 久之艾美容店(官渡区新亚洲体育城店) (权重:15)
[348173] [12:46:26] 🔥 优先消息已加入队列[位置0]: 久之艾美容店(官渡区新亚洲体育城店): 66 (权重:15)
🆕 新用户消息已加入高优先级队列
【统计msg】当前观看人数: 5, 累计观看人数: 1007
【进场msg】[100684168822][男]晓MING 进入了直播间
👋 检测到用户进入: 晓MING (男)
⏳ 欢迎消息冷却中，跳过欢迎: 晓MING
【直播间统计msg】6在线观众
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 1020
【进场msg】[3597216434356908][女](๑òᆺó๑) 进入了直播间
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
🚀 正在启动抖音直播聊天机器人...
📋 使用配置文件: config_739580848508.json
🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动
   - 只有在 'listening' 或 'idle' 状态时才发送消息
   - 在 'speaking' 状态时消息会自动加入队列
   - 说话结束后自动处理队列中的消息
   - 队列空闲超过1秒自动发送防冷场消息
   - 检测点赞事件并主动与点赞用户互动
   - 避免设备冲突，确保消息发送时机合适
   - 支持消息队列状态查看和手动处理

✅ 配置文件加载成功: config_739580848508.json
🤖 抖音直播聊天机器人 - 增强版
============================================================
⚠️ 配置项 'logging.show_room_id' 不存在，使用默认值: True
⚠️ 配置项 'custom_reply' 不存在，使用默认值: {}
📝 自定义回复配置加载完成: 禁用, 0条规则
🏠 房间ID: 739580848508
⚠️ 配置项 'anti_silence' 不存在，使用默认值: {}
⚠️ 配置项 'like_interaction' 不存在，使用默认值: {}
⚠️ 配置项 'anti_silence' 不存在，使用默认值: {}
⚠️ 配置项 'like_interaction' 不存在，使用默认值: {}
⚠️ 配置项 'message_preprocess' 不存在，使用默认值: {}
⚠️ 配置项 'scheduled_broadcast' 不存在，使用默认值: {}
⚠️ 配置项 'scheduled_broadcast.enabled' 不存在，使用默认值: True
⚠️ 配置项 'scheduled_broadcast.interval_minutes' 不存在，使用默认值: 10
⚠️ 配置项 'scheduled_broadcast.templates' 不存在，使用默认值: ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！', '感谢大家的支持，让我们一起聊天互动吧！', '别忘了给主播点个小心心，你的支持是我最大的动力！', '新来的朋友记得关注一下，不迷路哦！']
⚠️ 配置项 'welcome_message' 不存在，使用默认值: {}
⚠️ 配置项 'welcome_message.enabled' 不存在，使用默认值: True
⚠️ 配置项 'welcome_message.cooldown_seconds' 不存在，使用默认值: 0
⚠️ 配置项 'welcome_message.templates' 不存在，使用默认值: []
📋 配置已加载:
   📺 直播间ID: 739580848508
   📡 WebSocket地址: ws://localhost:15000
   🎯 目标设备ID: 34:cd:b0:b3:df:28
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📦 消息队列: 启用
   🔄 状态监控: 启用
🔄 状态监控线程已启动，检查间隔: 1秒
🔄 配置重载监控已启动
⚠️ 配置项 'gift_priority' 不存在，使用默认值: {}
⚠️ 配置项 'follow_interaction' 不存在，使用默认值: {}
⚠️ 配置项 'follow_interaction.enabled' 不存在，使用默认值: True
⚠️ 配置项 'follow_interaction.response_templates' 不存在，使用默认值: []
⚠️ 配置项 'interactive_games' 不存在，使用默认值: {}

🔍 检查ESP32设备状态...
❌ ESP32设备离线: 34:cd:b0:b3:df:28
⚠️ ESP32设备离线，但仍会继续监控直播间
💡 请确保ESP32设备已连接并运行mcp_wake服务

📋 当前设置:
   🔄 自动回复: 启用
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📊 消息长度限制: 1-100字符
【机器人东哥哥】[65033183496]直播间：正在直播.

🎯 开始监控直播间聊天消息...
💬 符合条件的聊天消息将自动通过ESP32回复
🔍 系统会自动检查设备状态，确保在合适时机发送消息
⏹️ 按Ctrl+C停止监控
============================================================
【√】WebSocket连接成功.
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 青宁🥭, score_str=''
【排行榜】第1名: 青宁🥭 - 贡献值: 第1名
🔍 排行榜第2名: 二月红爱旅游, score_str=''
【排行榜】第2名: 二月红爱旅游 - 贡献值: 第2名
🔍 排行榜第3名: 叫我清哥, score_str=''
【排行榜】第3名: 叫我清哥 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7053
【直播间统计msg】6在线观众
【进场msg】[2990313091375854][女]嗯. 进入了直播间
👋 检测到用户进入: 嗯. (女)
👋 准备发送欢迎消息: 嗯.欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备 34:cd:b0:b3:df:28 未连接
   用户: 嗯.
   设备状态: offline
   欢迎内容: 嗯.欢迎你！有什么想聊的吗？
【统计msg】当前观看人数: 7, 累计观看人数: 7055
【进场msg】[2626079180478839][女]用户7553096337482 进入了直播间
👋 检测到用户进入: 用户7553096337482 (女)
👋 准备发送欢迎消息: 欢迎用户7553096337482进入直播间！
🚫 欢迎消息被阻止发送: 设备 34:cd:b0:b3:df:28 未连接
   用户: 用户7553096337482
   设备状态: offline
   欢迎内容: 欢迎用户7553096337482进入直播间！
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 3秒
🎭 冷场回复冷却: 10秒
🎭 房间配置路径: config/rooms/739580848508.json['anti_silence']
🔄 已重新加载房间 739580848508 的配置（新格式）
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7055
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7058
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7058
💾 开始保存数据: 113 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7058
【进场msg】[104289094103][男]爱奔跑的牛仔 进入了直播间
👋 检测到用户进入: 爱奔跑的牛仔 (男)
👋 准备发送欢迎消息: 欢迎爱奔跑的牛仔来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备 34:cd:b0:b3:df:28 未连接
   用户: 爱奔跑的牛仔
   设备状态: offline
   欢迎内容: 欢迎爱奔跑的牛仔来到直播间，很高兴见到你！
【直播间统计msg】5在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7059
[848508] [12:46:28] 【聊天msg】[3421311587596551]砚山音响白姐: 我可以和你机器对话么[捂脸]
[848508] [12:46:28] 🕐 消息时间检查: 启动后 84.4s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=我可以和你机器对话么[捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 1)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> 我可以和你机器对话么[捂脸]...
[848508] [12:46:28] 🚫 消息被阻止发送: 设备 34:cd:b0:b3:df:28 未连接
[848508] [12:46:28]    用户消息: 砚山音响白姐: 我可以和你机器对话么[捂脸]
[848508] [12:46:28]    设备状态: offline
🆕 新用户首次发言: 砚山音响白姐 (权重:15)
[848508] [12:46:28] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 我可以和你机器对话么[捂脸] (权重:15)
🆕 新用户消息已加入高优先级队列
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 3秒
🎭 冷场回复冷却: 10秒
🎭 房间配置路径: config/rooms/739580848508.json['anti_silence']
🔄 已重新加载房间 739580848508 的配置（新格式）
182.245.189.204 - - [30/Jul/2025 12:46:37] "POST /api/rooms/431879348173/welcome-config HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:46:44] "GET / HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:46:44] "GET /api/devices HTTP/1.1" 200 -
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 5, 累计观看人数: 6
【统计msg】当前观看人数: 4, 累计观看人数: 5
【进场msg】[94097534776][女]南玖 进入了直播间
👋 检测到用户进入: 南玖 (女)
👋 准备发送欢迎消息: 欢迎南玖进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 南玖
   设备状态: speaking
   欢迎内容: 欢迎南玖进入直播间！
[305200] [12:45:45] 🔥 优先消息已加入队列[位置1]: 南玖: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:45:45] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】2在线观众
【统计msg】当前观看人数: 4, 累计观看人数: 8
[305200] [12:45:48] 🔄 设备状态变化: speaking -> listening
[305200] [12:45:48] 📤 开始处理消息队列...
[305200] [12:45:48] 📦 处理队列: 2条消息
[305200] [12:45:48] 🔥 发现 2 条优先消息（礼物回复等）
[305200] [12:45:48] 📊 队列状态: 2条消息，本次处理上限: 1条
[305200] [12:45:48] 🔥 队列[1/2] 用户467875: 进入直播间...[优先] (21.0s)
[305200] [12:45:48] 👋 正在处理欢迎消息: 用户467875
✅ 设备就绪，状态: listening
🔍 发送调用[2]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[2]: 用户4678755385837来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "用户4678755385837来了，大...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 用户4678755385837来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 🇷🇺 勘察加, score_str=''
【排行榜】第3名: 🇷🇺 勘察加 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 5, 累计观看人数: 6
【进场msg】[2599690997340647][女]🇷🇺 勘察加 进入了直播间
👋 检测到用户进入: 🇷🇺 勘察加 (女)
👋 准备发送欢迎消息: xx 勘察加来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 🇷🇺 勘察加
   设备状态: speaking
   欢迎内容: xx 勘察加来了，大家欢迎！
[305200] [12:45:54] 🔥 优先消息已加入队列[位置0]: 🇷🇺 勘察加: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:45:54] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 4, 累计观看人数: 8
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 川妹儿, score_str=''
【排行榜】第1名: 川妹儿 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 4, 累计观看人数: 8
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 2
[305200] [12:45:59] ✅ 队列消息发送成功: 用户467875
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 2, 累计观看人数: 8
【直播间统计msg】1在线观众
【统计msg】当前观看人数: 2, 累计观看人数: 10
   ↳ 上述消息重复了 2 次
[305200] [12:46:15] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:46:15] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:46:15] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:46:15] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:46:15] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:46:15] 🔥 队列[1/1] 🇷🇺 勘察加: 进入直播间...[优先] (20.2s)
[305200] [12:46:15] 👋 正在处理欢迎消息: 🇷🇺 勘察加
✅ 设备就绪，状态: listening
🔍 发送调用[3]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[3]: xx 勘察加来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "xx 勘察加来了，大家欢迎！", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: xx 勘察加来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 3
[305200] [12:46:26] ✅ 队列消息发送成功: 🇷🇺 勘察加
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 5秒
🎭 冷场回复冷却: 5秒
🎭 房间配置路径: config/rooms/200382305200.json['anti_silence']
🔄 已重新加载房间 200382305200 的配置（新格式）
   ↳ 上述消息重复了 2 次
[305200] [12:46:41] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:46:41] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=4.0s, 需要=5s, 上次冷场=1753850805.0s, 冷却=5s
🎭 冷场检查: 无活动时间=5.0s, 需要=5s, 上次冷场=1753850806.0s, 冷却=5s
🎭 检测到冷场：listening状态 5.0秒无活动，触发防冷场消息
💬 生成通用防冷场消息
🎭 发送防冷场消息: 介绍一下自己
🎭 最终格式: 介绍一下自己 (长度:6)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
[305200] [12:46:46] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:46:46] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:46:46] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:46:46] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:46:46] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
[305200] [12:46:46] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[4]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[4]: 介绍一下自己
🔗 连接WebSocket: ws://localhost:15000
✅ 加载了 26 个设备配置
🚀 启动标准WebSocket服务器
==================================================
📡 ESP32连接地址: ws://*************:15000
💡 专门处理ESP32设备连接和消息
💡 支持标准JSON格式消息：{"type":"event_name","data":{...}}

📋 支持的事件类型:
  - device_register: 设备注册
  - device_status: 设备状态更新
  - wake_device: 唤醒设备
  - speak_message: 让设备说话
  - reconnect_server: 重连服务器

🧪 测试命令:
  python test_wake_device.py
==================================================
📡 WebSocket服务器启动在端口 15000
🔄 实时状态监控已启用 - 设备状态变化将立即显示
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: *************:7228
🔍 设备ID: 10:20:ba:cf:63:ec
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 10:20:ba:cf:63:ec
🔍 当前连接设备: ['10:20:ba:cf:63:ec']
==================================================
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: ***************:14760
🔍 设备ID: 30:ed:a0:29:f0:a4
🔍 设备名称: 默认设备
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 30:ed:a0:29:f0:a4
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4']
==================================================
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: 182.245.189.204:10122
🔍 设备ID: 10:20:ba:cf:5f:58
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 10:20:ba:cf:5f:58
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4', '10:20:ba:cf:5f:58']
==================================================
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: *************:5438
🔍 设备ID: 10:20:ba:cf:62:74
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 10:20:ba:cf:62:74
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4', '10:20:ba:cf:5f:58', '10:20:ba:cf:62:74']
==================================================
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: ************:13549
🔍 设备ID: 10:20:ba:cd:72:5c
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 10:20:ba:cd:72:5c
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4', '10:20:ba:cf:5f:58', '10:20:ba:cf:62:74', '10:20:ba:cd:72:5c']
==================================================
🎤 发送: 用户723542324来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 idle → 🔄connecting ❌
📱 29:f0:a4 connecting → 👂listening ✅
📱 29:f0:a4 listening → 🗣️speaking ❌🔊
🔌 连接断开: 182.245.189.204:10122
📱 设备断开: 10:20:ba:cf:5f:58
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: 182.245.189.204:10149
🔍 设备ID: 10:20:ba:cf:5f:58
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 10:20:ba:cf:5f:58
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4', '10:20:ba:cf:62:74', '10:20:ba:cd:72:5c', '10:20:ba:cf:5f:58']
==================================================
📱 cd:72:5c idle → 🔄connecting ❌
📱 cd:72:5c connecting → 👂listening ✅
📱 cd:72:5c listening → 🗣️speaking ❌
==================================================
🎯 收到设备唤醒指令！
🔍 目标设备: 10:20:ba:cf:5f:58
🔍 消息内容: 设备唤醒测试
🔍 强制模式: True
==================================================
✅ 消息已转发到设备: 10:20:ba:cf:5f:58
📤 转发内容: {"type": "wake_device", "data": {"message": "设备唤醒测试", "force": true}}
📤 发送成功响应: {"type": "wake_response", "data": {"success": true, "message": "Wake command sent to device", "target_device": "10:20:ba:cf:5f:58"}}
📱 设备 unknown 唤醒响应: 成功 - Device woken up successfully
📱 cf:5f:58 idle → 🔄connecting ❌
📱 cf:5f:58 connecting → 👂listening ✅
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
📱 cd:72:5c speaking → 👂listening ✅
📱 29:f0:a4 speaking → 👂listening ✅🔊
🎤 发送: 用户4678755385837来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: xx 勘察加来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
🎤 发送: 请和观众们互动一下，营造温暖的氛围
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
==================================================
🔧 ESP32设备注册处理器被调用了！
🔍 客户端ID: **************:15985
🔍 设备ID: 34:cd:b0:b3:df:28
🔍 设备名称: 小灵C3
🔍 设备类型: esp32
🔍 设备功能: voice,display
==================================================
💾 设备配置已保存，共 26 个设备
📤 发送响应: {"type": "device_register_response", "data": {"success": true, "message": "Device registered successfully"}}
✅ 设备注册成功: 34:cd:b0:b3:df:28
🔍 当前连接设备: ['10:20:ba:cf:63:ec', '30:ed:a0:29:f0:a4', '10:20:ba:cf:62:74', '10:20:ba:cd:72:5c', '10:20:ba:cf:5f:58', '34:cd:b0:b3:df:28']
==================================================
🎤 发送: 砚山音响白姐说我可以和你机器对话么[捂脸]
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 idle → 🔄connecting ❌
📱 b3:df:28 connecting → 👂listening ✅
📱 b3:df:28 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 久之艾美容店官渡区新亚洲体育城说66
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:62:74 idle → 🔄connecting ❌
📱 cf:62:74 connecting → 👂listening ✅
🎤 发送: 介绍一下自己
🔍 用户 fanjie (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 调试信息 - 请求房间ID: 875206794749
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
🔍 调试信息 - 找到的配置: {'live_id': '875206794749', 'room_name': 'ai自动直播', 'target_device_id': '10:20:ba:cf:63:ec', 'enabled': True, 'created_at': '2025-07-30T12:38:12.358001', 'created_by': 'fanjie', 'activation_code': 'N53YMZWD56EF'}
✅ 房间 875206794749 激活码验证通过: 激活码有效，剩余 299 天
🚀 准备启动房间: 875206794749 (ai自动直播) -> 10:20:ba:cf:63:ec
🚀 启动直播间: 875206794749 (ai自动直播) -> 10:20:ba:cf:63:ec
⚠️ 未找到图形终端，使用后台进程模式
✅ 直播间 875206794749 (ai自动直播) 已启动
🔧 进程ID: 105304
🎬 启动房间监控: 875206794749
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 fanjie (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 875206794749 的官方排行榜，共 1 位用户
📊 通过API更新房间 875206794749 的官方排行榜，共 1 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
🔍 从配置文件加载到 26 个设备
🔍 用户 admin (admin) 请求设备列表，共 26 个设备
🔍 管理员用户，返回所有 26 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 admin (admin) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 19 个房间
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
🔍 调试信息 - 请求房间ID: 431879348173
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
🔍 调试信息 - 找到的配置: {'live_id': '431879348173', 'room_name': '和元Ai心疗', 'target_device_id': '10:20:ba:cf:5f:58', 'enabled': True, 'created_at': '2025-07-30T09:22:01.209825', 'created_by': 'heyuan', 'activation_code': '2HQGSHV3KFXZ', 'welcome_message': {'enabled': True, 'cooldown_seconds': 5, 'templates': ['欢迎{user_name}进入直播间！', '{user_name}来了，大家欢迎！']}, 'anti_silence': {'enabled': True, 'delay_seconds': 3, 'cooldown_seconds': 20, 'max_proactive_attempts': 2, 'templates': ['现在直播间有点冷场，请主动和大家打个招呼', '请和观众们互动一下，营造温暖的氛围', '可以主动找个有趣的话题和大家聊聊'], 'personalized_templates': ['{user_name}，刚才聊得挺开心的，还有什么想聊的吗？', '嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！', '{user_name}，直播间有点安静了，来活跃一下气氛吧！', '{user_name}，有什么问题或想法都可以说出来哦！']}, 'like_interaction': {'enabled': True, 'cooldown_seconds': 60, 'first_like_threshold': 1, 'subsequent_like_threshold': 100, 'templates': []}, 'gift_priority': {'enabled': True, 'priority_message_count': 2, 'gift_response_templates': ['哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧'], 'gift_weights': {'玫瑰': 3, '小心心': 3, '跑车': 10, '火箭': 50, '嘉年华': 100, '默认': 3}}, 'interactive_games': {'enabled': True, 'cooldown_seconds': 30, 'auto_trigger': True, 'auto_interval_minutes': 5, 'games': {'guess_number': {'enabled': True, 'range': [1, 100]}, 'word_chain': {'enabled': True, 'difficulty': 'medium'}, 'riddle': {'enabled': True, 'category': 'all'}}}, 'anti_spam': {'enabled': True, 'block_duration_seconds': 300, 'warning_cooldown_seconds': 60, 'duplicate_detection': {'enabled': True, 'time_window_seconds': 60, 'max_duplicates': 3, 'similarity_threshold': 0.8}, 'frequency_limit': {'enabled': True, 'time_window_seconds': 30, 'max_messages': 5}, 'warning_messages': ['{user_name}你个刷屏怪，再刷屏把你关小黑屋了！', '{user_name}别刷屏了，给其他人一点发言机会！', '{user_name}刷屏可不是好习惯，消停点！', '{user_name}你这样刷屏很影响直播间秩序哦！']}}
✅ 房间 431879348173 激活码验证通过: 激活码有效，剩余 299 天
🚀 准备启动房间: 431879348173 (和元Ai心疗) -> 10:20:ba:cf:5f:58
🚀 启动直播间: 431879348173 (和元Ai心疗) -> 10:20:ba:cf:5f:58
⚠️ 未找到图形终端，使用后台进程模式
✅ 直播间 431879348173 (和元Ai心疗) 已启动
🔧 进程ID: 105371
🎬 启动房间监控: 431879348173
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 heyuan (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 heyuan (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
✅ 为房间 431879348173 创建了默认自定义回复配置
✅ 为房间 431879348173 创建了默认消息预处理配置
✅ 为房间 431879348173 创建了默认关注互动配置
✅ 为房间 431879348173 创建了默认定时插播配置
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
📊 全局互动排行榜: 107 位用户有互动记录
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
🔍 从配置文件加载到 26 个设备
🔍 用户 xiechaojun (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cd:72:5c']，过滤后: 1 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 查询互动排行榜: room_id=None, 配置房间数=19, 用户总数=113
🔍 查询互动排行榜: room_id=211953907443, 配置房间数=19, 用户总数=113
👋 检测到用户进入: (๑òᆺó๑) (女)
⏳ 欢迎消息冷却中，跳过欢迎: (๑òᆺó๑)
【进场msg】[88632411756][男]熊猫没有黑眼圈 进入了直播间
👋 检测到用户进入: 熊猫没有黑眼圈 (男)
👋 准备发送欢迎消息: 熊猫没有黑眼圈来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 熊猫没有黑眼圈
   设备状态: speaking
   欢迎内容: 熊猫没有黑眼圈来了，大家欢迎！
[348173] [12:46:33] 🔥 优先消息已加入队列[位置2]: 熊猫没有黑眼圈: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:46:33] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】8在线观众
【统计msg】当前观看人数: 8, 累计观看人数: 1044
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 1
[348173] [12:46:34] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 8, 累计观看人数: 1044
[348173] [12:46:40] 🔄 设备状态变化: speaking -> listening
[348173] [12:46:40] 📤 开始处理消息队列...
[348173] [12:46:40] 📦 处理队列: 3条消息
[348173] [12:46:40] 🔥 发现 3 条优先消息（礼物回复等）
[348173] [12:46:40] 📊 队列状态: 3条消息，本次处理上限: 1条
[348173] [12:46:40] 🔥 队列[1/3] 久之艾美容店(官: 66...[优先] (14.6s)
[348173] [12:46:40] 💬 正在处理用户消息: 久之艾美容店(官
✅ 设备就绪，状态: listening
🔍 发送调用[2]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[2]: 久之艾美容店官渡区新亚洲体育城说66
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "久之艾美容店官渡区新亚洲体育城说66"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 久之艾美容店官渡区新亚洲体育城说66
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[59820627997995][男]黑人父 进入了直播间
👋 检测到用户进入: 黑人父 (男)
👋 准备发送欢迎消息: 黑人父来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 黑人父
   设备状态: speaking
   欢迎内容: 黑人父来了，大家欢迎！
[348173] [12:46:45] 🔥 优先消息已加入队列[位置0]: 黑人父: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:46:45] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】8在线观众
【统计msg】当前观看人数: 8, 累计观看人数: 1044
[348173] [12:46:49] 【聊天msg】[3309943844777998]柒柒: 666
[348173] [12:46:49] 🕐 消息时间检查: 启动后 29.3s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:46:49] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=柒柒, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 柒柒 (总互动: 1)
📊 房间 431879348173 互动: 柒柒 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 柒柒 -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:46:49] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:46:49]    用户消息: 柒柒: 66
   ↳ 上述消息重复了 2 次
[348173] [12:46:49]    设备状态: speaking
🆕 新用户首次发言: 柒柒 (权重:15)
[348173] [12:46:49] 🔥 优先消息已加入队列[位置0]: 柒柒: 66 (权重:15)
🆕 新用户消息已加入高优先级队列
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 3秒
🎭 冷场回复冷却: 20秒
🎭 房间配置路径: config/rooms/431879348173.json['anti_silence']
🔄 已重新加载房间 431879348173 的配置（新格式）
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 2
[348173] [12:46:51] ✅ 队列消息发送成功: 久之艾美容店(官
⏸️ 已发送 1 条消息，剩余 1 条消息将在下次处理
✅ 队列处理完成，共处理 3 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[68241044363][男]团领商贸 进入了直播间
👋 检测到用户进入: 团领商贸 (男)
👋 准备发送欢迎消息: 欢迎团领商贸进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 团领商贸
   设备状态: speaking
   欢迎内容: 欢迎团领商贸进入直播间！
[348173] [12:46:51] 🔥 优先消息已加入队列[位置3]: 团领商贸: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:46:51] 📦 欢迎消息已加入队列等待处理
【进场msg】[110176597416][男]？ 进入了直播间
👋 检测到用户进入: ？ (男)
⏳ 欢迎消息冷却中，跳过欢迎: ？
【进场msg】[99822497557][男]陽光普照 进入了直播间
👋 检测到用户进入: 陽光普照 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 陽光普照
【直播间统计msg】11在线观众
【点赞msg】柒柒 点了5个赞
👍 柒柒 点了5个赞，累计5个赞
🎉 首次点赞互动触发: 柒柒 累计5个赞，首次点赞回复 -> 柒柒点赞了，请感谢他的鼓励并主动和他交流
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 柒柒 (累计5个赞)
   设备状态: speaking
[348173] [12:46:52] 🔥 优先消息已加入队列[位置1]: 柒柒: 累计点赞5个 (权重:5)
[348173] [12:46:52] � 点赞回复已加入优先队列
【统计msg】当前观看人数: 8, 累计观看人数: 1073
【直播间统计msg】8在线观众
[348173] [12:46:53] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): 6666
[348173] [12:46:53] 🕐 消息时间检查: 启动后 33.3s, 过滤启用: True
[348173] [12:46:53] 🔧 消息预处理: '6666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 2)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:46:53] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
   ↳ 上述消息重复了 2 次
[348173] [12:46:53]    用户消息: 久之艾美容店(官渡区新亚洲体育城店): 66
   ↳ 上述消息重复了 2 次
[348173] [12:46:53]    设备状态: speaking
📦 普通用户消息: 久之艾美容店(官渡区新亚洲体育城店) (权重:10)
[348173] [12:46:53] 🔥 优先消息已加入队列[位置1]: 久之艾美容店(官渡区新亚洲体育城店): 66 (权重:10)
📦 用户消息已加入队列
【点赞msg】黑人父 点了1个赞
👍 黑人父 点了1个赞，累计1个赞
🎉 首次点赞互动触发: 黑人父 累计1个赞，首次点赞回复 -> 黑人父点了赞，请感谢他的支持并主动找话题和他互动
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 黑人父 (累计1个赞)
   设备状态: speaking
[348173] [12:46:53] 🔥 优先消息已加入队列[位置3]: 黑人父: 累计点赞1个 (权重:5)
【直播间统计msg】11在线观众
40.77.167.75 - - [30/Jul/2025 12:47:01] "GET / HTTP/1.1" 200 -
【进场msg】[1971839298976843][男]没烦恼 进入了直播间
👋 检测到用户进入: 没烦恼 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 没烦恼
【点赞msg】柒柒 点了8个赞
【统计msg】当前观看人数: 7, 累计观看人数: 1066
【进场msg】[91476275897][女]哎呦、不错哦！ 进入了直播间
👋 检测到用户进入: 哎呦、不错哦！ (女)
⏳ 欢迎消息冷却中，跳过欢迎: 哎呦、不错哦！
[348173] [12:46:55] 【聊天msg】[59820627997995]黑人父: 666
[348173] [12:46:55] 🕐 消息时间检查: 启动后 35.9s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:46:55] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=黑人父, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑人父 (总互动: 1)
📊 房间 431879348173 互动: 黑人父 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 116
📊 记录聊天互动: 黑人父 -> 66...
[348173] [12:46:55]    用户消息: 黑人父: 66
🆕 新用户首次发言: 黑人父 (权重:15)
[348173] [12:46:55] 🔥 优先消息已加入队列[位置1]: 黑人父: 66 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】柒柒 点了8个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 柒柒, score_str=''
【排行榜】第1名: 柒柒 - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】柒柒 点了7个赞
【统计msg】当前观看人数: 7, 累计观看人数: 1092
【点赞msg】柒柒 点了6个赞
[348173] [12:47:01] 【聊天msg】[100684168822]晓MING: 1
[348173] [12:47:01] 🕐 消息时间检查: 启动后 41.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=晓MING, 消息=1..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 晓MING (总互动: 1)
📊 房间 431879348173 互动: 晓MING (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 117
📊 记录聊天互动: 晓MING -> 1...
   ↳ 上述消息重复了 3 次
[348173] [12:47:01] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:01]    用户消息: 晓MING: 1
   ↳ 上述消息重复了 3 次
[348173] [12:47:01]    设备状态: speaking
🆕 新用户首次发言: 晓MING (权重:15)
[348173] [12:47:01] 🔥 优先消息已加入队列[位置2]: 晓MING: 1 (权重:15)
🆕 新用户消息已加入高优先级队列
[348173] [12:47:01] 【聊天msg】[1207744668376794]果蔬侦察社: 你是谁
[348173] [12:47:01] 🕐 消息时间检查: 启动后 41.9s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=果蔬侦察社, 消息=你是谁..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 果蔬侦察社 (总互动: 1)
📊 房间 431879348173 互动: 果蔬侦察社 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 118
📊 记录聊天互动: 果蔬侦察社 -> 你是谁...
⏳ 回复冷却中，将消息加入队列等待处理: 你是谁
🆕 新用户首次发言（冷却期）: 果蔬侦察社 (权重:15)
[348173] [12:47:01] 🔥 优先消息已加入队列[位置3]: 果蔬侦察社: 你是谁 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[101661532829][男]fvxgg 进入了直播间
👋 检测到用户进入: fvxgg (男)
👋 准备发送欢迎消息: fvxgg来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: fvxgg
   设备状态: speaking
   欢迎内容: fvxgg来了，大家欢迎！
[348173] [12:47:03] 🔥 优先消息已加入队列[位置10]: fvxgg: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:03] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】13在线观众
   ↳ 上述消息重复了 2 次
[348173] [12:47:03] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:47:03] 📤 开始处理消息队列...
[348173] [12:47:03] 📦 处理队列: 11条消息
[348173] [12:47:03] 🔥 发现 11 条优先消息（礼物回复等）
[348173] [12:47:03] 📊 队列状态: 11条消息，本次处理上限: 2条
[348173] [12:47:03] 🔥 队列[1/11] 柒柒: 66...[优先] (14.8s)
[348173] [12:47:03] 💬 正在处理用户消息: 柒柒
✅ 设备就绪，状态: listening
🔍 发送调用[3]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[3]: 柒柒说66
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "柒柒说66", "force": fa...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 柒柒说66
✅ 发送成功
[348173] [12:47:04] 【聊天msg】[91476275897]哎呦、不错哦！: 哈喽
[348173] [12:47:04] 🕐 消息时间检查: 启动后 44.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=哎呦、不错哦！, 消息=哈喽..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 哎呦、不错哦！ (总互动: 1)
📊 房间 431879348173 互动: 哎呦、不错哦！ (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 119
📊 记录聊天互动: 哎呦、不错哦！ -> 哈喽...
🆕 新用户首次发言: 哎呦、不错哦！ (权重:15)
[348173] [12:47:04] 🔥 优先消息已加入队列[位置0]: 哎呦、不错哦！: 哈喽 (权重:15)
🆕 新用户消息已加入高优先级队列
   ↳ 上述消息重复了 2 次
[348173] [12:47:04] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[348173] [12:47:04] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[348173] [12:47:04] 📊 队列状态: 1条消息，本次处理上限: 1条
[348173] [12:47:04] 🔥 队列[1/1] 哎呦、不错哦！: 哈喽...[优先] (0.0s)
[348173] [12:47:04] 💬 正在处理用户消息: 哎呦、不错哦！
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[348173] [12:47:04] ❌ 队列消息发送失败: 哎呦、不错哦！ - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 哎呦、不错哦！
📦 消息已重新入队 (重试次数: 1/2)
⏳ 等待设备完成说话...
✅ 队列处理完成，共处理 1 条消息
【统计msg】当前观看人数: 7, 累计观看人数: 1092
【点赞msg】柒柒 点了3个赞
【进场msg】[74160981955][男]偷喝可乐的猫 进入了直播间
👋 检测到用户进入: 偷喝可乐的猫 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 偷喝可乐的猫
[348173] [12:47:05] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): 我是90后
[348173] [12:47:05] 🕐 消息时间检查: 启动后 45.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=我是90后..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 3)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 119
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> 我是90后...
【进场msg】[1459753282449772][女]用户2617759419724 进入了直播间
👋 检测到用户进入: 用户2617759419724 (女)
👋 准备发送欢迎消息: 欢迎用户2617759419724进入直播间！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[848508] [12:46:34] 🔥 优先消息已加入队列[位置1]: 用户2617759419724: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
[848508] [12:46:34] 📦 处理队列: 2条消息
[848508] [12:46:34] 🔥 发现 2 条优先消息（礼物回复等）
[848508] [12:46:34] 📊 队列状态: 2条消息，本次处理上限: 1条
[848508] [12:46:34] 🔥 队列[1/2] 砚山音响白姐: 我可以和你机器对话么[捂脸]...[优先] (6.6s)
[848508] [12:46:34] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: idle
🔍 发送调用[1]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[1]: 砚山音响白姐说我可以和你机器对话么[捂脸]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说我可以和你机器对话么[捂...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说我可以和你机器对话么[捂脸]
✅ 发送成功
🎧 设备进入listening状态，重置防冷场计时器
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 1
[848508] [12:46:45] ✅ 队列消息发送成功: 砚山音响白姐
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 5, 累计观看人数: 7061
【进场msg】[94453878357][男]前轱辘不转后轱辘转 进入了直播间
👋 检测到用户进入: 前轱辘不转后轱辘转 (男)
👋 准备发送欢迎消息: 欢迎前轱辘不转后轱辘转来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 前轱辘不转后轱辘转
   设备状态: speaking
   欢迎内容: 欢迎前轱辘不转后轱辘转来到直播间，很高兴见到你！
[848508] [12:46:45] 🔥 优先消息已加入队列[位置0]: 前轱辘不转后轱辘转: 进入直播间 (权重:1)
[848508] [12:46:45] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】6在线观众
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 5, 累计观看人数: 7061
[848508] [12:46:50] 🔄 设备状态变化: speaking -> listening
[848508] [12:46:50] 📤 开始处理消息队列...
[848508] [12:46:50] 📦 处理队列: 1条消息
[848508] [12:46:50] 🔥 发现 1 条优先消息（礼物回复等）
[848508] [12:46:50] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:46:50] 🔥 队列[1/1] 前轱辘不转后轱辘: 进入直播间...[优先] (4.8s)
[848508] [12:46:50] 👋 正在处理欢迎消息: 前轱辘不转后轱辘
✅ 设备就绪，状态: listening
🔍 发送调用[2]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[2]: 欢迎前轱辘不转后轱辘转来到直播间，很高兴见到你！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎前轱辘不转后轱辘转来到直播间，很高...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 欢迎前轱辘不转后轱辘转来到直播间，很高兴见到你！
✅ 发送成功
⏳ 等待设备完成说话...
[848508] [12:46:54] 【聊天msg】[3421311587596551]砚山音响白姐: 6
[848508] [12:46:54] 🕐 消息时间检查: 启动后 110.9s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 2)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> 6...
[848508] [12:46:54] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:46:54]    用户消息: 砚山音响白姐: 6
[848508] [12:46:54]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:46:54] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 6 (权重:10)
📦 用户消息已加入队列
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 2
[848508] [12:47:01] ✅ 队列消息发送成功: 前轱辘不转后轱辘
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 3秒
🎭 冷场回复冷却: 10秒
🎭 房间配置路径: config/rooms/739580848508.json['anti_silence']
🔄 已重新加载房间 739580848508 的配置（新格式）
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:47:08] 🔥 队列[1/1] 砚山音响白姐: 6...[优先] (13.4s)
   ↳ 上述消息重复了 2 次
[848508] [12:47:08] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[3]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[3]: 砚山音响白姐说6
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说6", "force":...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说6
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[98734692709][男]曹老师 进入了直播间
👋 检测到用户进入: 曹老师 (男)
👋 准备发送欢迎消息: 曹老师欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 曹老师
   设备状态: speaking
   欢迎内容: 曹老师欢迎你！有什么想聊的吗？
[848508] [12:47:15] 🔥 优先消息已加入队列[位置0]: 曹老师: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:47:15] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 3
   ↳ 上述消息重复了 2 次
[848508] [12:47:18] ✅ 队列消息发送成功: 砚山音响白姐
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7064
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7064
   ↳ 上述消息重复了 2 次
[848508] [12:47:24] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
⏳ 回复冷却中，将消息加入队列等待处理: 我是90后
📦 普通用户消息（冷却期）: 久之艾美容店(官渡区新亚洲体育城店) (权重:10)
[348173] [12:47:05] 🔥 优先消息已加入队列[位置1]: 久之艾美容店(官渡区新亚洲体育城店): 我是90后 (权重:10)
📦 冷却期消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 柒柒, score_str=''
【排行榜】第1名: 柒柒 - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[76316093134][男]维城（正在努力） 进入了直播间
👋 检测到用户进入: 维城（正在努力） (男)
⏳ 欢迎消息冷却中，跳过欢迎: 维城（正在努力）
【统计msg】当前观看人数: 12, 累计观看人数: 1116
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 3
[348173] [12:47:14] ✅ 队列消息发送成功: 柒柒
[348173] [12:47:14] 🔥 队列[2/11] 黑人父: 66...[优先] (19.0s)
[348173] [12:47:14] 💬 正在处理用户消息: 黑人父
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[104681149172][男]酱酒小生 进入了直播间
👋 检测到用户进入: 酱酒小生 (男)
👋 准备发送欢迎消息: 酱酒小生来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 酱酒小生
   设备状态: speaking
   欢迎内容: 酱酒小生来了，大家欢迎！
[348173] [12:47:14] 🔥 优先消息已加入队列[位置2]: 酱酒小生: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:14] 📦 欢迎消息已加入队列等待处理
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[10760654250][男]刘划墙 进入了直播间
👋 检测到用户进入: 刘划墙 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 刘划墙
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 黑人父
[348173] [12:47:19] 🔥 队列[3/11] 晓MING: 1...[优先] (18.8s)
[348173] [12:47:19] 💬 正在处理用户消息: 晓MING
⏳ 等待设备就绪，当前状态: speaking
💾 开始保存数据: 119 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:47:22] 【聊天msg】[104681149172]酱酒小生: 666
[348173] [12:47:22] 🕐 消息时间检查: 启动后 62.4s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:47:22] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=酱酒小生, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 酱酒小生 (总互动: 1)
📊 房间 431879348173 互动: 酱酒小生 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 120
📊 记录聊天互动: 酱酒小生 -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:47:22] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:22]    用户消息: 酱酒小生: 66
   ↳ 上述消息重复了 2 次
[348173] [12:47:22]    设备状态: speaking
🆕 新用户首次发言: 酱酒小生 (权重:15)
[348173] [12:47:22] 🔥 优先消息已加入队列[位置1]: 酱酒小生: 66 (权重:15)
🆕 新用户消息已加入高优先级队列
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[57728826338][男]俗人 进入了直播间
👋 检测到用户进入: 俗人 (男)
👋 准备发送欢迎消息: 俗人来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 俗人
   设备状态: speaking
   欢迎内容: 俗人来了，大家欢迎！
[348173] [12:47:22] 🔥 优先消息已加入队列[位置4]: 俗人: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:22] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】14在线观众
[348173] [12:47:23] 【聊天msg】[95618021718]波西米亚狂想曲: 那我呢？
[348173] [12:47:23] 🕐 消息时间检查: 启动后 63.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=波西米亚狂想曲, 消息=那我呢？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 波西米亚狂想曲 (总互动: 1)
📊 房间 431879348173 互动: 波西米亚狂想曲 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 波西米亚狂想曲 -> 那我呢？...
⏳ 回复冷却中，将消息加入队列等待处理: 那我呢？
🆕 新用户首次发言（冷却期）: 波西米亚狂想曲 (权重:15)
[348173] [12:47:23] 🔥 优先消息已加入队列[位置2]: 波西米亚狂想曲: 那我呢？ (权重:15)
📦 冷却期消息已加入队列等待处理
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 晓MING
[348173] [12:47:24] 🔥 队列[4/11] 果蔬侦察社: 你是谁...[优先] (23.0s)
[348173] [12:47:24] 💬 正在处理用户消息: 果蔬侦察社
⏳ 等待设备就绪，当前状态: speaking
【统计msg】当前观看人数: 12, 累计观看人数: 1116
【进场msg】[110731618985][男](^-^) 进入了直播间
👋 检测到用户进入: (^-^) (男)
⏳ 欢迎消息冷却中，跳过欢迎: (^-^)
✅ 设备就绪，状态: listening
🔍 发送调用[4]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[4]: 果蔬侦察社说你是谁
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "果蔬侦察社说你是谁", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 果蔬侦察社说你是谁
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】16在线观众
【进场msg】[1736840707325246][女]染 进入了直播间
👋 检测到用户进入: 染 (女)
👋 准备发送欢迎消息: 染来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 染
   设备状态: speaking
   欢迎内容: 染来了，大家欢迎！
[348173] [12:47:28] 🔥 优先消息已加入队列[位置6]: 染: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:28] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 13, 累计观看人数: 1168
[348173] [12:47:30] 【聊天msg】[3309943844777998]柒柒: 你什么知道的[捂脸][捂脸]
[348173] [12:47:30] 🕐 消息时间检查: 启动后 70.8s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=柒柒, 消息=你什么知道的[捂脸][捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 柒柒 (总互动: 2)
📊 房间 431879348173 互动: 柒柒 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 柒柒 -> 你什么知道的[捂脸][捂脸]...
   ↳ 上述消息重复了 2 次
[348173] [12:47:30] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:30]    用户消息: 柒柒: 你什么知道的[捂脸][捂脸]
   ↳ 上述消息重复了 2 次
[348173] [12:47:30]    设备状态: speaking
************ - - [30/Jul/2025 12:47:41] "GET /login HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:47:41] "GET /api/auth/verify HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:47:41] "GET / HTTP/1.1" 200 -
************ - - [30/Jul/2025 12:47:41] "GET /api/devices HTTP/1.1" 200 -
📦 普通用户消息: 柒柒 (权重:10)
[348173] [12:47:30] 🔥 优先消息已加入队列[位置4]: 柒柒: 你什么知道的[捂脸][捂脸] (权重:10)
📦 用户消息已加入队列
[348173] [12:47:32] 【聊天msg】[59820627997995]黑人父: 666666
[348173] [12:47:32] 🕐 消息时间检查: 启动后 72.8s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:47:32] 🔧 消息预处理: '666666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=黑人父, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑人父 (总互动: 2)
📊 房间 431879348173 互动: 黑人父 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 黑人父 -> 66...
⏳ 回复冷却中，将消息加入队列等待处理: 66
📦 普通用户消息（冷却期）: 黑人父 (权重:10)
[348173] [12:47:32] 🔥 优先消息已加入队列[位置5]: 黑人父: 66 (权重:10)
📦 冷却期消息已加入队列等待处理
[348173] [12:47:35] 【聊天msg】[59820627997995]黑人父: 66
[348173] [12:47:35] 🕐 消息时间检查: 启动后 75.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=黑人父, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑人父 (总互动: 3)
📊 房间 431879348173 互动: 黑人父 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 黑人父 -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:47:35] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
   ↳ 上述消息重复了 2 次
[348173] [12:47:35]    用户消息: 黑人父: 66
   ↳ 上述消息重复了 2 次
[348173] [12:47:35]    设备状态: speaking
📦 普通用户消息: 黑人父 (权重:10)
[348173] [12:47:35] 🔥 优先消息已加入队列[位置6]: 黑人父: 66 (权重:10)
📦 用户消息已加入队列
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 4
[348173] [12:47:36] ✅ 队列消息发送成功: 果蔬侦察社
⏸️ 已发送 2 条消息，剩余 6 条消息将在下次处理
✅ 队列处理完成，共处理 11 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[3789389949840675][男]星辰 进入了直播间
👋 检测到用户进入: 星辰 (男)
👋 准备发送欢迎消息: 星辰来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 星辰
   设备状态: speaking
   欢迎内容: 星辰来了，大家欢迎！
[348173] [12:47:36] 🔥 优先消息已加入队列[位置15]: 星辰: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:36] 📦 欢迎消息已加入队列等待处理
【点赞msg】星辰 点了2个赞
👍 星辰 点了2个赞，累计2个赞
🎉 首次点赞互动触发: 星辰 累计2个赞，首次点赞回复 -> 星辰点赞了，请感谢他的鼓励并主动和他交流
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 星辰 (累计2个赞)
   设备状态: speaking
[348173] [12:47:38] 🔥 优先消息已加入队列[位置7]: 星辰: 累计点赞2个 (权重:5)
   ↳ 上述消息重复了 3 次
[348173] [12:47:38] � 点赞回复已加入优先队列
[348173] [12:47:38] 【聊天msg】[59820627997995]黑人父: 我呢
[348173] [12:47:38] 🕐 消息时间检查: 启动后 78.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=黑人父, 消息=我呢..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑人父 (总互动: 4)
📊 房间 431879348173 互动: 黑人父 (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 黑人父 -> 我呢...
[348173] [12:47:38]    用户消息: 黑人父: 我呢
📦 普通用户消息: 黑人父 (权重:10)
[348173] [12:47:38] 🔥 优先消息已加入队列[位置7]: 黑人父: 我呢 (权重:10)
📦 用户消息已加入队列
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 📤 开始处理消息队列...
[348173] [12:47:41] 📦 处理队列: 18条消息
[348173] [12:47:41] 🔥 发现 18 条优先消息（礼物回复等）
[348173] [12:47:41] 📊 队列状态: 18条消息，本次处理上限: 3条
[348173] [12:47:41] 🔥 队列[1/18] 哎呦、不错哦！: 哈喽...[优先] (37.0s)
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 💬 正在处理用户消息: 哎呦、不错哦！
✅ 设备就绪，状态: listening
🔍 发送调用[5]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[5]: 哎呦x不错哦!说哈喽
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "哎呦x不错哦!说哈喽", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 哎呦x不错哦!说哈喽
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[91476275897][女]哎呦、不错哦！ 进入了直播间
👋 检测到用户进入: 哎呦、不错哦！ (女)
👋 准备发送欢迎消息: 欢迎哎呦x不错哦!进入直播间！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[348173] [12:47:41] 🔥 优先消息已加入队列[位置0]: 哎呦、不错哦！: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] 📊 队列状态: 1条消息，本次处理上限: 1条
[348173] [12:47:41] 🔥 队列[1/1] 哎呦、不错哦！: 进入直播间...[优先] (0.0s)
[348173] [12:47:41] 👋 正在处理欢迎消息: 哎呦、不错哦！
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
   ↳ 上述消息重复了 2 次
[348173] [12:47:41] ❌ 队列消息发送失败: 哎呦、不错哦！ - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 哎呦、不错哦！
📦 消息已重新入队 (重试次数: 1/2)
✅ 队列处理完成，共处理 1 条消息
[348173] [12:47:47] 【聊天msg】[1207744668376794]果蔬侦察社: 321上车
[348173] [12:47:47] 🕐 消息时间检查: 启动后 87.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=果蔬侦察社, 消息=321上车..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 果蔬侦察社 (总互动: 2)
📊 房间 431879348173 互动: 果蔬侦察社 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 121
📊 记录聊天互动: 果蔬侦察社 -> 321上车...
   ↳ 上述消息重复了 3 次
[348173] [12:47:47] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:47]    用户消息: 果蔬侦察社: 321上车
   ↳ 上述消息重复了 3 次
[348173] [12:47:47]    设备状态: speaking
📦 普通用户消息: 果蔬侦察社 (权重:10)
[348173] [12:47:47] 🔥 优先消息已加入队列[位置0]: 果蔬侦察社: 321上车 (权重:10)
📦 用户消息已加入队列
【进场msg】[3318755081065819][女]皮💫带不沾碘伏 进入了直播间
👋 检测到用户进入: 皮💫带不沾碘伏 (女)
👋 准备发送欢迎消息: 皮x带不沾碘伏来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 皮💫带不沾碘伏
   设备状态: speaking
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "介绍一下自己", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 介绍一下自己
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 4
[305200] [12:46:56] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: Vl, score_str=''
【排行榜】第2名: Vl - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【进场msg】[116964829705667][女]Vl 进入了直播间
👋 检测到用户进入: Vl (女)
👋 准备发送欢迎消息: Vl来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: Vl
   设备状态: speaking
   欢迎内容: Vl来了，大家欢迎！
[305200] [12:47:08] 🔥 优先消息已加入队列[位置0]: Vl: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:47:08] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 1, 累计观看人数: 12
【直播间统计msg】2在线观众
【统计msg】当前观看人数: 1, 累计观看人数: 12
   ↳ 上述消息重复了 2 次
[305200] [12:47:16] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:47:16] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:47:16] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:47:16] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:47:16] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:47:16] 🔥 队列[1/1] Vl: 进入直播间...[优先] (7.9s)
[305200] [12:47:16] 👋 正在处理欢迎消息: Vl
✅ 设备就绪，状态: listening
🔍 发送调用[5]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[5]: Vl来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "Vl来了，大家欢迎！", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: Vl来了，大家欢迎！
✅ 发送成功
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
⏳ 等待设备完成说话...
【统计msg】当前观看人数: 1, 累计观看人数: 12
【直播间统计msg】1在线观众
【统计msg】当前观看人数: 2, 累计观看人数: 13
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 5
[305200] [12:47:27] ✅ 队列消息发送成功: Vl
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 山木同学🏂, score_str=''
【排行榜】第2名: 山木同学🏂 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 2, 累计观看人数: 13
【进场msg】[4068650010283789][男]山木同学🏂 进入了直播间
👋 检测到用户进入: 山木同学🏂 (男)
👋 准备发送欢迎消息: 山木同学x来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 山木同学🏂
   设备状态: speaking
   欢迎内容: 山木同学x来了，大家欢迎！
[305200] [12:47:37] 🔥 优先消息已加入队列[位置0]: 山木同学🏂: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:47:37] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 1, 累计观看人数: 13
   ↳ 上述消息重复了 2 次
[305200] [12:47:39] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:47:39] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:47:39] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:47:39] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:47:39] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:47:39] 🔥 队列[1/1] 山木同学🏂: 进入直播间...[优先] (2.4s)
[305200] [12:47:39] 👋 正在处理欢迎消息: 山木同学🏂
✅ 设备就绪，状态: listening
🔍 发送调用[6]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[6]: 山木同学x来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "山木同学x来了，大家欢迎！", "fo...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 山木同学x来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 2, 累计观看人数: 12
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 6
[305200] [12:47:50] ✅ 队列消息发送成功: 山木同学🏂
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】2在线观众
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 山木同学🏂, score_str=''
【排行榜】第2名: 山木同学🏂 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 1, 累计观看人数: 13
【进场msg】[103719287805][女]花卷儿 进入了直播间
👋 检测到用户进入: 花卷儿 (女)
👋 准备发送欢迎消息: 花卷儿来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 花卷儿
   设备状态: speaking
   欢迎内容: 花卷儿来了，大家欢迎！
[305200] [12:47:53] 🔥 优先消息已加入队列[位置0]: 花卷儿: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:47:53] 📦 欢迎消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 山木同学🏂, score_str=''
【排行榜】第2名: 山木同学🏂 - 贡献值: 第2名
🔍 排行榜第3名: 花卷儿, score_str=''
【排行榜】第3名: 花卷儿 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
   欢迎内容: 皮x带不沾碘伏来了，大家欢迎！
[348173] [12:47:51] 🔥 优先消息已加入队列[位置2]: 皮💫带不沾碘伏: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:47:51] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 5
[348173] [12:47:52] ✅ 队列消息发送成功: 哎呦、不错哦！
[348173] [12:47:52] 🔥 队列[2/18] 酱酒小生: 66...[优先] (30.0s)
[348173] [12:47:52] 💬 正在处理用户消息: 酱酒小生
⏳ 等待设备就绪，当前状态: speaking
【直播间统计msg】15在线观众
【统计msg】当前观看人数: 16, 累计观看人数: 1229
[348173] [12:47:52] 【聊天msg】[1736840707325246]染: 666怎么做的？
[348173] [12:47:52] 🕐 消息时间检查: 启动后 92.7s, 过滤启用: True
[348173] [12:47:52] 🔧 消息预处理: '666怎么做的？' → '66怎么做的？'
🔍 开始记录互动: 房间=431879348173, 用户=染, 消息=66怎么做的？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 染 (总互动: 1)
📊 房间 431879348173 互动: 染 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 122
📊 记录聊天互动: 染 -> 66怎么做的？...
   ↳ 上述消息重复了 2 次
[348173] [12:47:52] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:52]    用户消息: 染: 66怎么做的？
   ↳ 上述消息重复了 2 次
[348173] [12:47:52]    设备状态: speaking
🆕 新用户首次发言: 染 (权重:15)
[348173] [12:47:52] 🔥 优先消息已加入队列[位置0]: 染: 66怎么做的？ (权重:15)
🆕 新用户消息已加入高优先级队列
[348173] [12:47:53] 【聊天msg】[76316093134]维城（正在努力）: 这个是什么鬼
[348173] [12:47:53] 🕐 消息时间检查: 启动后 93.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=维城（正在努力）, 消息=这个是什么鬼..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 维城（正在努力） (总互动: 1)
📊 房间 431879348173 互动: 维城（正在努力） (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 123
📊 记录聊天互动: 维城（正在努力） -> 这个是什么鬼...
⏳ 回复冷却中，将消息加入队列等待处理: 这个是什么鬼
🆕 新用户首次发言（冷却期）: 维城（正在努力） (权重:15)
[348173] [12:47:53] 🔥 优先消息已加入队列[位置1]: 维城（正在努力）: 这个是什么鬼 (权重:15)
📦 冷却期消息已加入队列等待处理
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[66172922572][男]吴同学 进入了直播间
👋 检测到用户进入: 吴同学 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 吴同学
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 酱酒小生
[348173] [12:47:57] 🔥 队列[3/18] 波西米亚狂想曲: 那我呢？...[优先] (33.8s)
[348173] [12:47:57] 💬 正在处理用户消息: 波西米亚狂想曲
✅ 设备就绪，状态: listening
🔍 发送调用[6]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[6]: 波西米亚狂想曲说那我呢？
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "波西米亚狂想曲说那我呢？", "for...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 波西米亚狂想曲说那我呢？
✅ 发送成功
⏳ 等待设备完成说话...
[348173] [12:47:58] 【聊天msg】[110731618985](^-^): 666
[348173] [12:47:58] 🕐 消息时间检查: 启动后 98.6s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:47:58] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=(^-^), 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: (^-^) (总互动: 1)
📊 房间 431879348173 互动: (^-^) (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 124
📊 记录聊天互动: (^-^) -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:47:58] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:47:58]    用户消息: (^-^): 66
   ↳ 上述消息重复了 2 次
[348173] [12:47:58]    设备状态: speaking
🆕 新用户首次发言: (^-^) (权重:15)
[348173] [12:47:58] 🔥 优先消息已加入队列[位置2]: (^-^): 66 (权重:15)
🆕 新用户消息已加入高优先级队列
   ↳ 上述消息重复了 2 次
[348173] [12:48:04] 【聊天msg】[59820627997995]黑人父: 666
[348173] [12:48:04] 🕐 消息时间检查: 启动后 104.6s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:48:04] 🔧 消息预处理: '666' → '66'
🔍 开始记录互动: 房间=431879348173, 用户=黑人父, 消息=66..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑人父 (总互动: 5)
📊 房间 431879348173 互动: 黑人父 (房间互动: 5)
✅ 互动记录完成，当前用户统计总数: 124
📊 记录聊天互动: 黑人父 -> 66...
   ↳ 上述消息重复了 2 次
[348173] [12:48:04] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
   ↳ 上述消息重复了 2 次
[348173] [12:48:04]    用户消息: 黑人父: 66
   ↳ 上述消息重复了 2 次
[348173] [12:48:04]    设备状态: speaking
📦 普通用户消息: 黑人父 (权重:10)
[348173] [12:48:04] 🔥 优先消息已加入队列[位置4]: 黑人父: 66 (权重:10)
📦 用户消息已加入队列
【进场msg】[2256652935900055][男]香辣鸡米花 进入了直播间
👋 检测到用户进入: 香辣鸡米花 (男)
👋 准备发送欢迎消息: 香辣鸡米花来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 香辣鸡米花
   设备状态: speaking
   欢迎内容: 香辣鸡米花来了，大家欢迎！
[348173] [12:48:06] 🔥 优先消息已加入队列[位置7]: 香辣鸡米花: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:48:06] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】16在线观众
【进场msg】[76368836159][女]1.7万人 进入了直播间
👋 检测到用户进入: 1.7万人 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 1.7万人
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 6
[348173] [12:48:08] ✅ 队列消息发送成功: 波西米亚狂想曲
⏰ 跳过过期消息 [4/18]: 久之艾美容店(官渡区新亚洲体育城店): 我是90后... (已过期 63.1秒)
[348173] [12:48:08] 🔥 队列[5/18] 柒柒: 你什么知道的[捂脸][捂脸]...[优先] (37.4s)
   ↳ 上述消息重复了 2 次
[348173] [12:48:08] 💬 正在处理用户消息: 柒柒
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[4499650243727000][女]但行好事 进入了直播间
👋 检测到用户进入: 但行好事 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 但行好事
【统计msg】当前观看人数: 16, 累计观看人数: 1242
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[53219908654935][女]潼冰 进入了直播间
👋 检测到用户进入: 潼冰 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 潼冰
【进场msg】[52429718815][男]蛋黄派🐯 进入了直播间
👋 检测到用户进入: 蛋黄派🐯 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 蛋黄派🐯
⏳ 等待设备就绪，当前状态: speaking
[848508] [12:47:24] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:47:24] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:47:24] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:47:24] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:47:24] 🔥 队列[1/1] 曹老师: 进入直播间...[优先] (9.2s)
[848508] [12:47:24] 👋 正在处理欢迎消息: 曹老师
✅ 设备就绪，状态: listening
🔍 发送调用[4]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[4]: 曹老师欢迎你！有什么想聊的吗？
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "曹老师欢迎你！有什么想聊的吗？", "...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 曹老师欢迎你！有什么想聊的吗？
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 4
[848508] [12:47:35] ✅ 队列消息发送成功: 曹老师
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
[848508] [12:47:36] 【聊天msg】[3421311587596551]砚山音响白姐: 我想要买一台广场舞音响推荐哈
[848508] [12:47:36] 🕐 消息时间检查: 启动后 153.1s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=我想要买一台广场舞音响推荐哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 3)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> 我想要买一台广场舞音响推荐哈...
   ↳ 上述消息重复了 2 次
[848508] [12:47:36] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:47:36]    用户消息: 砚山音响白姐: 我想要买一台广场舞音响推荐哈
   ↳ 上述消息重复了 2 次
[848508] [12:47:36]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:47:36] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 我想要买一台广场舞音响推荐哈 (权重:10)
📦 用户消息已加入队列
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:47:39] 🔥 队列[1/1] 砚山音响白姐: 我想要买一台广场舞音响推荐哈...[优先] (2.9s)
   ↳ 上述消息重复了 2 次
[848508] [12:47:39] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[5]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[5]: 砚山音响白姐说我想要买一台广场舞音响推荐哈
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说我想要买一台广场舞音响推...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说我想要买一台广场舞音响推荐哈
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 5
   ↳ 上述消息重复了 2 次
[848508] [12:47:50] ✅ 队列消息发送成功: 砚山音响白姐
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[101306289964][男]vannicehan 进入了直播间
👋 检测到用户进入: vannicehan (男)
👋 准备发送欢迎消息: 欢迎vannicehan进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: vannicehan
   设备状态: speaking
   欢迎内容: 欢迎vannicehan进入直播间！
[848508] [12:47:51] 🔥 优先消息已加入队列[位置0]: vannicehan: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:47:51] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7070
   ↳ 上述消息重复了 2 次
[848508] [12:47:59] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:47:59] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:47:59] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:47:59] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:47:59] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:47:59] 🔥 队列[1/1] vanniceh: 进入直播间...[优先] (7.7s)
[848508] [12:47:59] 👋 正在处理欢迎消息: vanniceh
✅ 设备就绪，状态: listening
🔍 发送调用[6]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[6]: 欢迎vannicehan进入直播间！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎vannicehan进入直播间！"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 欢迎vannicehan进入直播间！
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 6, 累计观看人数: 7071
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 6
[848508] [12:48:10] ✅ 队列消息发送成功: vanniceh
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
[848508] [12:48:16] 【聊天msg】[3421311587596551]砚山音响白姐: [捂脸]你可以来帮我卖音响么[捂脸]
[848508] [12:48:16] 🕐 消息时间检查: 启动后 192.7s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=[捂脸]你可以来帮我卖音响么[捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 4)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> [捂脸]你可以来帮我卖音响么[捂脸]...
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:48:16] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: [捂脸]你可以来帮我卖音响么[捂脸] (权重:10)
📦 用户消息已加入队列
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 📦 处理队列: 1条消息
【进场msg】[2220642146460536][男]大喔特闷Len🍉🍉 进入了直播间
👋 检测到用户进入: 大喔特闷Len🍉🍉 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 大喔特闷Len🍉🍉
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[81200709367][男]大凯凯 进入了直播间
👋 检测到用户进入: 大凯凯 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 大凯凯
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 柒柒
[348173] [12:48:13] 🔥 队列[6/18] 黑人父: 66...[优先] (40.4s)
   ↳ 上述消息重复了 2 次
[348173] [12:48:13] 💬 正在处理用户消息: 黑人父
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[114786367454408][男]喀柒 进入了直播间
👋 检测到用户进入: 喀柒 (男)
👋 准备发送欢迎消息: 欢迎喀柒进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 喀柒
   设备状态: speaking
   欢迎内容: 欢迎喀柒进入直播间！
[348173] [12:48:14] 🔥 优先消息已加入队列[位置8]: 喀柒: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:48:14] 📦 欢迎消息已加入队列等待处理
【进场msg】[2563670215493848][男]厶 进入了直播间
👋 检测到用户进入: 厶 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 厶
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[93680285330][男]流年 进入了直播间
👋 检测到用户进入: 流年 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 流年
【进场msg】[2453712359929246][女]快点改掉拖延症 进入了直播间
👋 检测到用户进入: 快点改掉拖延症 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 快点改掉拖延症
✅ 设备就绪，状态: listening
🔍 发送调用[7]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[7]: 黑人父说66
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "黑人父说66", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 黑人父说66
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】22在线观众
【统计msg】当前观看人数: 15, 累计观看人数: 1297
[348173] [12:48:24] 【聊天msg】[2453712359929246]快点改掉拖延症: [看]
[348173] [12:48:24] 🕐 消息时间检查: 启动后 124.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=快点改掉拖延症, 消息=[看]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 快点改掉拖延症 (总互动: 1)
📊 房间 431879348173 互动: 快点改掉拖延症 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 125
📊 记录聊天互动: 快点改掉拖延症 -> [看]...
   ↳ 上述消息重复了 2 次
[348173] [12:48:24] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:48:24]    用户消息: 快点改掉拖延症: [看]
   ↳ 上述消息重复了 2 次
[348173] [12:48:24]    设备状态: speaking
🆕 新用户首次发言: 快点改掉拖延症 (权重:15)
[348173] [12:48:24] 🔥 优先消息已加入队列[位置3]: 快点改掉拖延症: [看] (权重:15)
🆕 新用户消息已加入高优先级队列
【进场msg】[1992727597427543][男]两斤包谷酒 进入了直播间
👋 检测到用户进入: 两斤包谷酒 (男)
👋 准备发送欢迎消息: 两斤包谷酒来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 两斤包谷酒
   设备状态: speaking
   欢迎内容: 两斤包谷酒来了，大家欢迎！
[348173] [12:48:24] 🔥 优先消息已加入队列[位置10]: 两斤包谷酒: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:48:24] 📦 欢迎消息已加入队列等待处理
【进场msg】[2573286144229240][女]呼到鸡西叠 进入了直播间
👋 检测到用户进入: 呼到鸡西叠 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 呼到鸡西叠
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 传播中医的小杨, score_str=''
【排行榜】第1名: 传播中医的小杨 - 贡献值: 第1名
🔍 排行榜第2名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第2名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第2名
🔍 排行榜第3名: 精神食粮补给站, score_str=''
【排行榜】第3名: 精神食粮补给站 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 22, 累计观看人数: 1331
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 7
[348173] [12:48:27] ✅ 队列消息发送成功: 黑人父
⏸️ 已发送 3 条消息，剩余 11 条消息将在下次处理
✅ 队列处理完成，共处理 18 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】23在线观众
【统计msg】当前观看人数: 15, 累计观看人数: 1293
   ↳ 上述消息重复了 2 次
[348173] [12:48:30] 【聊天msg】[59820627997995]黑人父: 6666
[348173] [12:48:30] 🕐 消息时间检查: 启动后 130.9s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:48:30] 🔧 消息预处理: '6666' → '66'
🚨 检测到重复刷屏: 黑人父 发送了 3 条相似消息 (相似度阈值: 0.8)
🚫 用户 黑人父(59820627997995) 因刷屏被屏蔽 300 秒
[348173] [12:48:30] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - duplicate
[348173] [12:48:30] 🔥 发送防刷屏警告: 黑人父你个刷屏怪，再刷屏把你关小黑屋了！
[348173] [12:48:30] 🔥 优先消息已加入队列[位置0]: 系统警告: 防刷屏警告:黑人父 (权重:999)
[348173] [12:48:30] 🔥 防刷屏警告已加入高优先级队列
[348173] [12:48:30] 📦 处理队列: 16条消息
[348173] [12:48:30] 🔥 发现 16 条优先消息（礼物回复等）
[348173] [12:48:30] 📊 队列状态: 16条消息，本次处理上限: 3条
[348173] [12:48:30] 🔥 队列[1/16] 系统警告: 防刷屏警告:黑人父...[优先] (0.0s)
[348173] [12:48:30] ❓ 正在处理未知类型消息(anti_spam): 系统警告
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[8]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[8]: 黑人父你个刷屏怪，再刷屏把你关小黑屋了！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "黑人父你个刷屏怪，再刷屏把你关小黑屋了...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 黑人父你个刷屏怪，再刷屏把你关小黑屋了！
✅ 发送成功
   ↳ 上述消息重复了 2 次
[348173] [12:48:34] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:48:34] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 8
[348173] [12:48:44] ✅ 队列消息发送成功: 系统警告
103.78.127.28 - - [30/Jul/2025 12:49:05] "GET / HTTP/1.1" 200 -
103.116.121.28 - - [30/Jul/2025 12:49:05] "[31m[1mGET /api/devices HTTP/1.1[0m" 401 -
103.78.127.28 - - [30/Jul/2025 12:49:05] "GET /login HTTP/1.1" 200 -
103.78.127.28 - - [30/Jul/2025 12:49:17] "POST /api/auth/login HTTP/1.1" 200 -
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:48:16] 🔥 队列[1/1] 砚山音响白姐: [捂脸]你可以来帮我卖音响么[...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[848508] [12:48:16] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[7]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[7]: 砚山音响白姐说[捂脸]你可以来帮我卖音响么[捂脸]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说[捂脸]你可以来帮我卖音...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说[捂脸]你可以来帮我卖音响么[捂脸]
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 7
   ↳ 上述消息重复了 2 次
[848508] [12:48:27] ✅ 队列消息发送成功: 砚山音响白姐
✅ 队列处理完成，共处理 1 条消息
【进场msg】[2795428183282297][女]小龙 进入了直播间
👋 检测到用户进入: 小龙 (女)
👋 准备发送欢迎消息: 欢迎小龙进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 小龙
   设备状态: speaking
   欢迎内容: 欢迎小龙进入直播间！
[848508] [12:48:28] 🔥 优先消息已加入队列[位置0]: 小龙: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:48:28] 📦 欢迎消息已加入队列等待处理
【进场msg】[1732429794522652][男]中国梦我的梦 进入了直播间
👋 检测到用户进入: 中国梦我的梦 (男)
👋 准备发送欢迎消息: 欢迎中国梦我的梦进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 中国梦我的梦
   设备状态: speaking
   欢迎内容: 欢迎中国梦我的梦进入直播间！
[848508] [12:48:33] 🔥 优先消息已加入队列[位置1]: 中国梦我的梦: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:48:33] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[848508] [12:48:36] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:48:36] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:48:36] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[848508] [12:48:36] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:48:36] 📊 队列状态: 2条消息，本次处理上限: 1条
[848508] [12:48:36] 🔥 队列[1/2] 小龙: 进入直播间...[优先] (8.2s)
[848508] [12:48:36] 👋 正在处理欢迎消息: 小龙
✅ 设备就绪，状态: listening
🔍 发送调用[8]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[8]: 欢迎小龙进入直播间！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎小龙进入直播间！", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 欢迎小龙进入直播间！
✅ 发送成功
⏳ 等待设备完成说话...
[848508] [12:48:38] 【聊天msg】[3421311587596551]砚山音响白姐: 都想
[848508] [12:48:38] 🕐 消息时间检查: 启动后 214.6s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=都想..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 5)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 5)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> 都想...
   ↳ 上述消息重复了 2 次
[848508] [12:48:38] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:48:38]    用户消息: 砚山音响白姐: 都想
   ↳ 上述消息重复了 2 次
[848508] [12:48:38]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:48:38] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 都想 (权重:10)
📦 用户消息已加入队列
【进场msg】[2291002044595607][男]币多多优选 进入了直播间
👋 检测到用户进入: 币多多优选 (男)
👋 准备发送欢迎消息: 欢迎币多多优选进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 币多多优选
   设备状态: speaking
   欢迎内容: 欢迎币多多优选进入直播间！
[848508] [12:48:46] 🔥 优先消息已加入队列[位置1]: 币多多优选: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:48:46] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 8
[848508] [12:48:47] ✅ 队列消息发送成功: 小龙
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 📊 队列状态: 2条消息，本次处理上限: 1条
[848508] [12:48:54] 🔥 队列[1/2] 砚山音响白姐: 都想...[优先] (15.9s)
   ↳ 上述消息重复了 2 次
[848508] [12:48:54] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[9]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[9]: 砚山音响白姐说都想
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说都想", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说都想
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】5在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7081
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 9
   ↳ 上述消息重复了 2 次
[848508] [12:49:05] ✅ 队列消息发送成功: 砚山音响白姐
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[848508] [12:49:15] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:49:15] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=2.0s, 需要=3s, 上次冷场=1753850957.2s, 冷却=10s
[348173] [12:48:44] 🔥 队列[2/16] 染: 66怎么做的？...[优先] (52.0s)
[348173] [12:48:44] 💬 正在处理用户消息: 染
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 染
[348173] [12:48:49] 🔥 队列[3/16] 维城（正在努力）: 这个是什么鬼...[优先] (56.4s)
[348173] [12:48:49] 💬 正在处理用户消息: 维城（正在努力）
⏳ 等待设备就绪，当前状态: speaking
   ↳ 上述消息重复了 2 次
[348173] [12:48:50] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:48:50] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
✅ 设备就绪，状态: listening
🔍 发送调用[9]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[9]: 维城(正在努力)说这个是什么鬼
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "维城(正在努力)说这个是什么鬼", "...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 维城(正在努力)说这个是什么鬼
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 9
[348173] [12:49:01] ✅ 队列消息发送成功: 维城（正在努力）
⏰ 跳过过期消息 [4/16]: (^-^): 66... (已过期 63.0秒)
[348173] [12:49:01] 🔥 队列[5/16] 快点改掉拖延症: [看]...[优先] (37.3s)
[348173] [12:49:01] 💬 正在处理用户消息: 快点改掉拖延症
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 快点改掉拖延症
⏰ 跳过过期消息 [6/16]: 黑人父: 我呢... (已过期 88.3秒)
⏰ 跳过过期消息 [7/16]: 果蔬侦察社: 321上车... (已过期 79.1秒)
⏰ 跳过过期消息 [8/16]: 黑人父: 66... (已过期 61.9秒)
⏰ 跳过过期消息 [9/16]: 星辰: 累计点赞2个... (已过期 88.3秒)
⏰ 跳过过期消息 [10/16]: 染: 进入直播间... (已过期 98.3秒)
⏰ 跳过过期消息 [11/16]: 星辰: 进入直播间... (已过期 89.7秒)
⏰ 跳过过期消息 [12/16]: 哎呦、不错哦！: 进入直播间... (已过期 84.5秒)
⏰ 跳过过期消息 [13/16]: 皮💫带不沾碘伏: 进入直播间... (已过期 75.2秒)
[348173] [12:49:06] 🔥 队列[14/16] 香辣鸡米花: 进入直播间...[优先] (59.9s)
[348173] [12:49:06] 👋 正在处理欢迎消息: 香辣鸡米花
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
   ↳ 上述消息重复了 2 次
[348173] [12:49:11] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:49:11] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 香辣鸡米花
[348173] [12:49:11] 🔥 队列[15/16] 喀柒: 进入直播间...[优先] (57.1s)
[348173] [12:49:11] 👋 正在处理欢迎消息: 喀柒
✅ 设备就绪，状态: listening
🔍 发送调用[10]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[10]: 欢迎喀柒进入直播间！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "欢迎喀柒进入直播间！", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 欢迎喀柒进入直播间！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 10
[348173] [12:49:22] ✅ 队列消息发送成功: 喀柒
⏸️ 已发送 3 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 16 条消息
【进场msg】[67004429938][男]A平安是福 进入了直播间
👋 检测到用户进入: A平安是福 (男)
👋 准备发送欢迎消息: 欢迎A平安是福进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: A平安是福
   设备状态: speaking
   欢迎内容: 欢迎A平安是福进入直播间！
[348173] [12:49:22] 🔥 优先消息已加入队列[位置0]: A平安是福: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:49:22] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[348173] [12:49:22] 【聊天msg】[59820627997995]黑人父: 666
[348173] [12:49:22] 🕐 消息时间检查: 启动后 182.4s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:49:22] 🔧 消息预处理: '666' → '66'
[348173] [12:49:22] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - blocked
[348173] [12:49:22] 【聊天msg】[2563670215493848]厶: 我嘞个豆
🔍 开始记录互动: 房间=431879348173, 用户=厶, 消息=我嘞个豆..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 厶 (总互动: 1)
📊 房间 431879348173 互动: 厶 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 126
📊 记录聊天互动: 厶 -> 我嘞个豆...
   ↳ 上述消息重复了 2 次
[348173] [12:49:22] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:49:22]    用户消息: 厶: 我嘞个豆
   ↳ 上述消息重复了 2 次
[348173] [12:49:22]    设备状态: speaking
🆕 新用户首次发言: 厶 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置0]: 厶: 我嘞个豆 (权重:15)
🆕 新用户消息已加入高优先级队列
【进场msg】[62500362618][男]你别管 进入了直播间
👋 检测到用户进入: 你别管 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 你别管
[348173] [12:49:22] 【聊天msg】[2220642146460536]大喔特闷Len🍉🍉: 这情绪价值[鼓掌][鼓掌]
🔍 开始记录互动: 房间=431879348173, 用户=大喔特闷Len🍉🍉, 消息=这情绪价值[鼓掌][鼓掌]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 大喔特闷Len🍉🍉 (总互动: 1)
📊 房间 431879348173 互动: 大喔特闷Len🍉🍉 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 127
📊 记录聊天互动: 大喔特闷Len🍉🍉 -> 这情绪价值[鼓掌][鼓掌]...
⏳ 回复冷却中，将消息加入队列等待处理: 这情绪价值[鼓掌][鼓掌]
🆕 新用户首次发言（冷却期）: 大喔特闷Len🍉🍉 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置1]: 大喔特闷Len🍉🍉: 这情绪价值[鼓掌][鼓掌] (权重:15)
📦 冷却期消息已加入队列等待处理
[348173] [12:49:22] 【聊天msg】[93680285330]流年: 6
🔍 开始记录互动: 房间=431879348173, 用户=流年, 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 流年 (总互动: 1)
📊 房间 431879348173 互动: 流年 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 128
📊 记录聊天互动: 流年 -> 6...
⏳ 回复冷却中，将消息加入队列等待处理: 6
🆕 新用户首次发言（冷却期）: 流年 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置2]: 流年: 6 (权重:15)
📦 冷却期消息已加入队列等待处理
【直播间统计msg】22在线观众
【统计msg】当前观看人数: 21, 累计观看人数: 1359
[348173] [12:49:22] 【聊天msg】[52429718815]蛋黄派🐯: 6
[348173] [12:49:22] 🕐 消息时间检查: 启动后 182.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=蛋黄派🐯, 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 蛋黄派🐯 (总互动: 1)
📊 房间 431879348173 互动: 蛋黄派🐯 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 129
📊 记录聊天互动: 蛋黄派🐯 -> 6...
⏳ 回复冷却中，将消息加入队列等待处理: 6
🆕 新用户首次发言（冷却期）: 蛋黄派🐯 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置3]: 蛋黄派🐯: 6 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[2862751069242638][男]小宏 进入了直播间
👋 检测到用户进入: 小宏 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 小宏
【点赞msg】呼到鸡西叠 点了1个赞
👍 呼到鸡西叠 点了1个赞，累计1个赞
🎉 首次点赞互动触发: 呼到鸡西叠 累计1个赞，首次点赞回复 -> 呼到鸡西叠刚刚给你点了赞，请感谢他并主动和他聊聊天
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 呼到鸡西叠 (累计1个赞)
   设备状态: speaking
[348173] [12:49:22] 🔥 优先消息已加入队列[位置4]: 呼到鸡西叠: 累计点赞1个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:49:22] � 点赞回复已加入优先队列
[348173] [12:49:22] 【聊天msg】[2453712359929246]快点改掉拖延症: 我呢
🔍 开始记录互动: 房间=431879348173, 用户=快点改掉拖延症, 消息=我呢..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 快点改掉拖延症 (总互动: 2)
📊 房间 431879348173 互动: 快点改掉拖延症 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 129
📊 记录聊天互动: 快点改掉拖延症 -> 我呢...
⏳ 回复冷却中，将消息加入队列等待处理: 我呢
📦 普通用户消息（冷却期）: 快点改掉拖延症 (权重:10)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置4]: 快点改掉拖延症: 我呢 (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[1356111506049098][女]天降奇兵 进入了直播间
👋 检测到用户进入: 天降奇兵 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 天降奇兵
【直播间统计msg】19在线观众
【统计msg】当前观看人数: 23, 累计观看人数: 1404
[348173] [12:49:22] 【聊天msg】[1736840707325246]染: 这个是怎么做的？
🔍 开始记录互动: 房间=431879348173, 用户=染, 消息=这个是怎么做的？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 染 (总互动: 2)
📊 房间 431879348173 互动: 染 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 129
📊 记录聊天互动: 染 -> 这个是怎么做的？...
⏳ 回复冷却中，将消息加入队列等待处理: 这个是怎么做的？
📦 普通用户消息（冷却期）: 染 (权重:10)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置5]: 染: 这个是怎么做的？ (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[61980967344][男]有你就好。 进入了直播间
👋 检测到用户进入: 有你就好。 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 有你就好。
【点赞msg】呼到鸡西叠 点了2个赞
[348173] [12:49:22] 【聊天msg】[1992727597427543]两斤包谷酒: 董莹
🔍 开始记录互动: 房间=431879348173, 用户=两斤包谷酒, 消息=董莹..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 两斤包谷酒 (总互动: 1)
📊 房间 431879348173 互动: 两斤包谷酒 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 130
📊 记录聊天互动: 两斤包谷酒 -> 董莹...
⏳ 回复冷却中，将消息加入队列等待处理: 董莹
🆕 新用户首次发言（冷却期）: 两斤包谷酒 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置4]: 两斤包谷酒: 董莹 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[63716565671][男]1836𝑫𝒎𝒊 进入了直播间
👋 检测到用户进入: 1836𝑫𝒎𝒊 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 1836𝑫𝒎𝒊
【进场msg】[57773274550][男]-  𝕷𝖀𝕺𝖃𝕴 进入了直播间
👋 检测到用户进入: -  𝕷𝖀𝕺𝖃𝕴 (男)
⏳ 欢迎消息冷却中，跳过欢迎: -  𝕷𝖀𝕺𝖃𝕴
[348173] [12:49:22] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): [赞]
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=[赞]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 4)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 130
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> [赞]...
⏳ 回复冷却中，将消息加入队列等待处理: [赞]
📦 普通用户消息（冷却期）: 久之艾美容店(官渡区新亚洲体育城店) (权重:10)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置7]: 久之艾美容店(官渡区新亚洲体育城店): [赞] (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[86605298336][男]张模作样 进入了直播间
👋 检测到用户进入: 张模作样 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 张模作样
【直播间统计msg】21在线观众
[348173] [12:49:22] 【聊天msg】[59820627997995]黑人父: 豆包给我放安静点。
[348173] [12:49:22] 🕐 消息时间检查: 启动后 182.6s, 过滤启用: True
【统计msg】当前观看人数: 23, 累计观看人数: 1404
【进场msg】[1604938318750302][男]叫我小清新 进入了直播间
👋 检测到用户进入: 叫我小清新 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 叫我小清新
[348173] [12:49:22] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): 厉害
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=厉害..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 5)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 5)
✅ 互动记录完成，当前用户统计总数: 130
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> 厉害...
⏳ 回复冷却中，将消息加入队列等待处理: 厉害
📦 普通用户消息（冷却期）: 久之艾美容店(官渡区新亚洲体育城店) (权重:10)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置8]: 久之艾美容店(官渡区新亚洲体育城店): 厉害 (权重:10)
📦 冷却期消息已加入队列等待处理
【关注msg】[2563670215493848]厶 关注了主播
[348173] [12:49:22] 💖 检测到用户关注: 厶
[348173] [12:49:22] 🔥 厶 关注主播，获得1条优先权
[348173] [12:49:22] 💖 准备发送关注感谢: 哟厶，关注我是你做过最明智的决定
[348173] [12:49:22] 📦 关注回复加入优先队列（权重:6-高优先级）
[348173] [12:49:22] 🔥 优先消息已加入队列[位置9]: 厶: 关注了主播 (权重:6)
[348173] [12:49:22] 📦 关注回复已加入队列
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 1, 累计观看人数: 13
【直播间统计msg】3在线观众
【统计msg】当前观看人数: 1, 累计观看人数: 14
   ↳ 上述消息重复了 2 次
[305200] [12:48:06] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:48:06] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:48:06] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:48:06] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:48:06] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:48:06] 🔥 队列[1/1] 花卷儿: 进入直播间...[优先] (12.8s)
[305200] [12:48:06] 👋 正在处理欢迎消息: 花卷儿
✅ 设备就绪，状态: listening
🔍 发送调用[7]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[7]: 花卷儿来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "花卷儿来了，大家欢迎！", "forc...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 花卷儿来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 7
[305200] [12:48:17] ✅ 队列消息发送成功: 花卷儿
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 花卷儿, score_str=''
【排行榜】第2名: 花卷儿 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 3, 累计观看人数: 15
[305200] [12:48:24] 【聊天msg】[103719287805]花卷儿: 哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈
[305200] [12:48:24] 🕐 消息时间检查: 启动后 227.5s, 过滤启用: True
[305200] [12:48:24] 🔧 消息预处理: '哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈' → '哈哈'
🔍 开始记录互动: 房间=200382305200, 用户=花卷儿, 消息=哈哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 花卷儿 (总互动: 1)
📊 房间 200382305200 互动: 花卷儿 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 花卷儿 -> 哈哈...
[305200] [12:48:24] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[305200] [12:48:24]    用户消息: 花卷儿: 哈哈
[305200] [12:48:24]    设备状态: speaking
🆕 新用户首次发言: 花卷儿 (权重:15)
[305200] [12:48:24] 🔥 优先消息已加入队列[位置0]: 花卷儿: 哈哈 (权重:15)
🆕 新用户消息已加入高优先级队列
【直播间统计msg】2在线观众
【统计msg】当前观看人数: 3, 累计观看人数: 15
   ↳ 上述消息重复了 2 次
[305200] [12:48:32] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:48:32] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:48:32] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:48:32] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:48:32] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:48:32] 🔥 队列[1/1] 花卷儿: 哈哈...[优先] (7.6s)
[305200] [12:48:32] 💬 正在处理用户消息: 花卷儿
✅ 设备就绪，状态: listening
🔍 发送调用[8]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[8]: 花卷儿说哈哈
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "花卷儿说哈哈", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 花卷儿说哈哈
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 8
   ↳ 上述消息重复了 2 次
[305200] [12:48:43] ✅ 队列消息发送成功: 花卷儿
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[305200] [12:48:56] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:48:56] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=4.0s, 需要=5s, 上次冷场=134.2s, 冷却=5s
🎭 冷场检查: 无活动时间=5.0s, 需要=5s, 上次冷场=135.2s, 冷却=5s
🎭 检测到冷场：listening状态 5.0秒无活动，触发防冷场消息
💬 生成个性化防冷场消息: 目标用户=花卷儿, 次数=1/2
🎭 发送防冷场消息: 花卷儿 跑哪儿去了
🎭 最终格式: 花卷儿 跑哪儿去了 (长度:9)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 📊 队列状态: 1条消息，本次处理上限: 1条
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:49:01] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[9]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[9]: 花卷儿 跑哪儿去了
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "花卷儿 跑哪儿去了", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 花卷儿 跑哪儿去了
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 9
   ↳ 上述消息重复了 2 次
[305200] [12:49:12] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 2, 累计观看人数: 15
【直播间统计msg】1在线观众
【统计msg】当前观看人数: 2, 累计观看人数: 15
   ↳ 上述消息重复了 2 次
[305200] [12:49:24] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:49:24] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=4.0s, 需要=5s, 上次冷场=26.8s, 冷却=5s
103.116.121.28 - - [30/Jul/2025 12:49:33] "GET / HTTP/1.1" 200 -
103.116.121.28 - - [30/Jul/2025 12:49:33] "GET /login HTTP/1.1" 200 -
【进场msg】[2126874326077739][女]皓皓🫚 进入了直播间
👋 检测到用户进入: 皓皓🫚 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 皓皓🫚
【进场msg】[333878245994723][女]陆ˢ 进入了直播间
👋 检测到用户进入: 陆ˢ (女)
⏳ 欢迎消息冷却中，跳过欢迎: 陆ˢ
【进场msg】[105054408486][男]#1234 进入了直播间
👋 检测到用户进入: #1234 (男)
⏳ 欢迎消息冷却中，跳过欢迎: #1234
【直播间统计msg】24在线观众
【统计msg】当前观看人数: 20, 累计观看人数: 1433
【直播间统计msg】23在线观众
【统计msg】当前观看人数: 22, 累计观看人数: 1471
【进场msg】[1004269856100042][女]城南的你 进入了直播间
👋 检测到用户进入: 城南的你 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 城南的你
【进场msg】[7479353976996185148][男]三炻 进入了直播间
👋 检测到用户进入: 三炻 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 三炻
【进场msg】[1280308669319352][男]念安 进入了直播间
👋 检测到用户进入: 念安 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 念安
[348173] [12:49:22] 【聊天msg】[105054408486]#1234: 删库
[348173] [12:49:22] 🕐 消息时间检查: 启动后 182.7s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=#1234, 消息=删库..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: #1234 (总互动: 1)
📊 房间 431879348173 互动: #1234 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 131
📊 记录聊天互动: #1234 -> 删库...
⏳ 回复冷却中，将消息加入队列等待处理: 删库
🆕 新用户首次发言（冷却期）: #1234 (权重:15)
[348173] [12:49:22] 🔥 优先消息已加入队列[位置5]: #1234: 删库 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[2628303178442555][男]老坛 进入了直播间
👋 检测到用户进入: 老坛 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 老坛
[348173] [12:49:24] 🕐 消息时间检查: 启动后 184.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=厶, 消息=我嘞个豆..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 厶 (总互动: 2)
📊 房间 431879348173 互动: 厶 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 131
📊 记录聊天互动: 厶 -> 我嘞个豆...
[348173] [12:49:24] 🔥 优先用户消息: 厶 (剩余优先权: 1)
[348173] [12:49:24] 🔥 厶 优先权已用完
🔥 优先用户消息: 厶 (权重:12)
[348173] [12:49:24] 🔥 优先消息已加入队列[位置6]: 厶: 我嘞个豆 (权重:12)
📦 用户消息已加入队列
【进场msg】[62517003414][男]痔尊宝 进入了直播间
👋 检测到用户进入: 痔尊宝 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 痔尊宝
【进场msg】[1356111506049098][女]天降奇兵 进入了直播间
👋 检测到用户进入: 天降奇兵 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 天降奇兵
【直播间统计msg】28在线观众
【统计msg】当前观看人数: 23, 累计观看人数: 1490
   ↳ 上述消息重复了 2 次
[348173] [12:49:28] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:49:28] 📤 开始处理消息队列...
[348173] [12:49:28] 📦 处理队列: 14条消息
[348173] [12:49:28] 🔥 发现 14 条优先消息（礼物回复等）
[348173] [12:49:28] 📊 队列状态: 14条消息，本次处理上限: 2条
[348173] [12:49:28] 🔥 队列[1/14] 厶: 我嘞个豆...[优先] (6.5s)
[348173] [12:49:28] 💬 正在处理用户消息: 厶
✅ 设备就绪，状态: listening
🔍 发送调用[11]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[11]: 厶说我嘞个豆
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "厶说我嘞个豆", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 厶说我嘞个豆
✅ 发送成功
【进场msg】[68122247071][男]『梵寰』 进入了直播间
👋 检测到用户进入: 『梵寰』 (男)
👋 准备发送欢迎消息: x梵寰x来了，大家欢迎！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[348173] [12:49:29] 🔥 优先消息已加入队列[位置0]: 『梵寰』: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
   ↳ 上述消息重复了 2 次
[348173] [12:49:29] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[348173] [12:49:29] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[348173] [12:49:29] 📊 队列状态: 1条消息，本次处理上限: 1条
[348173] [12:49:29] 🔥 队列[1/1] 『梵寰』: 进入直播间...[优先] (0.0s)
[348173] [12:49:29] 👋 正在处理欢迎消息: 『梵寰』
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[348173] [12:49:29] ❌ 队列消息发送失败: 『梵寰』 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 『梵寰』
📦 消息已重新入队 (重试次数: 1/2)
⏳ 等待设备完成说话...
✅ 队列处理完成，共处理 1 条消息
【进场msg】[2419340659666142][女]t2hj 进入了直播间
👋 检测到用户进入: t2hj (女)
⏳ 欢迎消息冷却中，跳过欢迎: t2hj
[348173] [12:49:31] 【聊天msg】[2419340659666142]t2hj: 哈哈哈哈哈哈哈哈
[348173] [12:49:31] 🕐 消息时间检查: 启动后 191.1s, 过滤启用: True
[348173] [12:49:31] 🔧 消息预处理: '哈哈哈哈哈哈哈哈' → '哈哈'
🔍 开始记录互动: 房间=431879348173, 用户=t2hj, 消息=哈哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: t2hj (总互动: 1)
📊 房间 431879348173 互动: t2hj (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 132
📊 记录聊天互动: t2hj -> 哈哈...
   ↳ 上述消息重复了 3 次
[348173] [12:49:31] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:49:31]    用户消息: t2hj: 哈哈
   ↳ 上述消息重复了 3 次
[348173] [12:49:31]    设备状态: speaking
🆕 新用户首次发言: t2hj (权重:15)
[348173] [12:49:31] 🔥 优先消息已加入队列[位置0]: t2hj: 哈哈 (权重:15)
🆕 新用户消息已加入高优先级队列
【直播间统计msg】29在线观众
【进场msg】[3932315486535448][女]￴￴   ￴ ￴一个馒头吃八天 进入了直播间
👋 检测到用户进入: ￴￴   ￴ ￴一个馒头吃八天 (女)
⏳ 欢迎消息冷却中，跳过欢迎: ￴￴   ￴ ￴一个馒头吃八天
【统计msg】当前观看人数: 23, 累计观看人数: 1531
【进场msg】[2013912907126935][男]随风111111 进入了直播间
👋 检测到用户进入: 随风111111 (男)
👋 准备发送欢迎消息: 随风111111来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 随风111111
   设备状态: speaking
   欢迎内容: 随风111111来了，大家欢迎！
[348173] [12:49:35] 🔥 优先消息已加入队列[位置2]: 随风111111: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:49:35] 📦 欢迎消息已加入队列等待处理
【进场msg】[899814702720093][男]阿咪嗝豆腐 进入了直播间
👋 检测到用户进入: 阿咪嗝豆腐 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 阿咪嗝豆腐
【进场msg】[106483067815][男]镇雄县隆众珠宝店（个体工商户） 进入了直播间
👋 检测到用户进入: 镇雄县隆众珠宝店（个体工商户） (男)
⏳ 欢迎消息冷却中，跳过欢迎: 镇雄县隆众珠宝店（个体工商户）
   ↳ 上述消息重复了 2 次
103.116.121.28 - - [30/Jul/2025 12:49:42] "POST /api/auth/login HTTP/1.1" 200 -
103.78.127.28 - - [30/Jul/2025 12:49:43] "GET / HTTP/1.1" 200 -
103.78.127.28 - - [30/Jul/2025 12:49:43] "GET /api/devices HTTP/1.1" 200 -
📊 房间 211953907443 互动排行榜: 13 位用户有互动记录
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
🔍 查询互动排行榜: room_id=211953907443, 配置房间数=19, 用户总数=113
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
🔍 从配置文件加载到 26 个设备
🔍 用户 xiechaojun (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cd:72:5c']，过滤后: 1 个设备
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 xiechaojun (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
🔍 收到登录请求
🔍 请求数据: {'username': 'misiru', 'password': '123455'}
🔍 用户名: misiru, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: misiru
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'misiru', 'password_hash': 'bbc0a08f624fc164d689fb44bb5b0e0c:18cb941c0305cbe397c7bf3ebad8366e14d59dd730d129a30203aeee31ea92b3', 'role': 'user', 'display_name': 'misiru', 'email': '', 'created_at': '2025-07-29T23:38:11.518829', 'last_login': '2025-07-30T08:49:06.764647', 'enabled': True, 'assigned_rooms': ['11875281106'], 'assigned_devices': ['10:20:ba:cf:63:f8']}
🔍 验证密码...
🔍 密码验证结果: False
❌ 密码错误: misiru
🔍 认证结果: {'success': False, 'message': '用户名或密码错误'}
🔍 收到登录请求
🔍 请求数据: {'username': 'misiru', 'password': '123456'}
🔍 用户名: misiru, 密码长度: 6
🔍 调用用户管理器认证
🔍 认证用户: misiru
🔍 用户数据库中的用户: ['admin', 'dongge', 'sivitacraft', 'heyuan', 'xin', 'buwangchuxin', 'VIVI', 'weichenyuan', 'changle', 'xiechaojun', 'xiachun', 'huangsenbo', 'testuser', 'testuser2', 'testuser1753703590', 'testuser1753704517', 'roomtest1753705437', 'uxtest1753705559', 'testuser_1753710232', 'testuser_1753710368', 'testuser_1753710450', 'testuser_1753710566', 'liaojing', 'fanjie', 'JD', 'misiru', 'lanjiejie', 'kanshijie', 'dianliang']
🔍 找到用户: {'username': 'misiru', 'password_hash': 'bbc0a08f624fc164d689fb44bb5b0e0c:18cb941c0305cbe397c7bf3ebad8366e14d59dd730d129a30203aeee31ea92b3', 'role': 'user', 'display_name': 'misiru', 'email': '', 'created_at': '2025-07-29T23:38:11.518829', 'last_login': '2025-07-30T08:49:06.764647', 'enabled': True, 'assigned_rooms': ['11875281106'], 'assigned_devices': ['10:20:ba:cf:63:f8']}
🔍 验证密码...
🔍 密码验证结果: True
🔍 创建会话...
🔍 生成会话令牌: cADBZZ1eub...
🔍 会话数据: {'username': 'misiru', 'role': 'user', 'created_at': 1753850982.1711738, 'expires_at': 1753937382.1711748}
🔍 获取锁...
🔍 已获取锁，保存会话...
🔍 更新最后登录时间...
🔍 保存用户数据...
🔍 开始保存用户数据到 data/runtime/users.json
✅ 用户数据保存成功
🔍 保存结果: True
✅ 用户登录成功: misiru (user)
🔍 返回认证结果: {'success': True, 'message': '登录成功', 'token': 'cADBZZ1eubHO86fHkbcZRb0BYUxbiUM8OrMON9rO3Eo', 'user': {'username': 'misiru', 'role': 'user', 'display_name': 'misiru'}}
🔍 认证结果: {'success': True, 'message': '登录成功', 'token': 'cADBZZ1eubHO86fHkbcZRb0BYUxbiUM8OrMON9rO3Eo', 'user': {'username': 'misiru', 'role': 'user', 'display_name': 'misiru'}}
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
🔍 从配置文件加载到 26 个设备
🔍 收到房间列表请求
🔍 用户 misiru (user) 请求设备列表，共 26 个设备
🔍 普通用户，分配的设备: ['10:20:ba:cf:63:f8']，过滤后: 1 个设备
🔍 从配置文件加载到 19 个房间
🔍 用户 misiru (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 misiru (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
🔍 调试信息 - 请求房间ID: 11875281106
🔍 调试信息 - 配置文件中的房间: ['11875281106', '211953907443', '337356796001', '34332455725', '293121991226', '875206794749', '537829762901', '807513877772', '7212598169799085351&type=general&ug_source=hw_dy_57dh', '475343222698', '739580848508', '734104407819', '618499433757', '978870279056', '200382305200', 'template_with_new_features', '431879348173', '319099627951', '77238811580']
103.116.121.28 - - [30/Jul/2025 12:49:48] "POST /api/rooms/11875281106/start HTTP/1.1" 200 -
[348173] [12:49:38] 【聊天msg】[59820627997995]黑人父: 666
[348173] [12:49:38] 🕐 消息时间检查: 启动后 198.9s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:49:38] 🔧 消息预处理: '666' → '66'
   ↳ 上述消息重复了 3 次
[348173] [12:49:38] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - blocked
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 11
[348173] [12:49:39] ✅ 队列消息发送成功: 厶
[348173] [12:49:39] 🔥 队列[2/14] 大喔特闷Len🍉: 这情绪价值[鼓掌][鼓掌]...[优先] (17.3s)
[348173] [12:49:39] 💬 正在处理用户消息: 大喔特闷Len🍉
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[1403452414500925][男]西西 进入了直播间
👋 检测到用户进入: 西西 (男)
👋 准备发送欢迎消息: 欢迎西西进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 西西
   设备状态: speaking
   欢迎内容: 欢迎西西进入直播间！
[348173] [12:49:41] 🔥 优先消息已加入队列[位置3]: 西西: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:49:41] 📦 欢迎消息已加入队列等待处理
[348173] [12:49:41] 【聊天msg】[2126874326077739]皓皓🫚: 牛皮
[348173] [12:49:41] 🕐 消息时间检查: 启动后 201.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=牛皮..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 1)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 133
📊 记录聊天互动: 皓皓🫚 -> 牛皮...
   ↳ 上述消息重复了 2 次
[348173] [12:49:41] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:49:41]    用户消息: 皓皓🫚: 牛皮
   ↳ 上述消息重复了 2 次
[348173] [12:49:41]    设备状态: speaking
🆕 新用户首次发言: 皓皓🫚 (权重:15)
[348173] [12:49:41] 🔥 优先消息已加入队列[位置1]: 皓皓🫚: 牛皮 (权重:15)
🆕 新用户消息已加入高优先级队列
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了1个赞
👍 镇雄县隆众珠宝店（个体工商户） 点了1个赞，累计1个赞
🎉 首次点赞互动触发: 镇雄县隆众珠宝店（个体工商户） 累计1个赞，首次点赞回复 -> 镇雄县隆众珠宝店（个体工商户）点赞了，请感谢他的鼓励并主动和他交流
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 镇雄县隆众珠宝店（个体工商户） (累计1个赞)
   设备状态: speaking
[348173] [12:49:42] 🔥 优先消息已加入队列[位置2]: 镇雄县隆众珠宝店（个体工商户）: 累计点赞1个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:49:42] � 点赞回复已加入优先队列
[348173] [12:49:43] 【聊天msg】[76368836159]1.7万人: 卸甲
[348173] [12:49:43] 🕐 消息时间检查: 启动后 203.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=1.7万人, 消息=卸甲..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 1.7万人 (总互动: 1)
📊 房间 431879348173 互动: 1.7万人 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 134
📊 记录聊天互动: 1.7万人 -> 卸甲...
[348173] [12:49:43]    用户消息: 1.7万人: 卸甲
🆕 新用户首次发言: 1.7万人 (权重:15)
[348173] [12:49:43] 🔥 优先消息已加入队列[位置2]: 1.7万人: 卸甲 (权重:15)
🆕 新用户消息已加入高优先级队列
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 大喔特闷Len🍉
[348173] [12:49:44] 🔥 队列[3/14] 流年: 6...[优先] (22.3s)
[348173] [12:49:44] 💬 正在处理用户消息: 流年
✅ 设备就绪，状态: listening
🔍 发送调用[12]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[12]: 流年说6
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "流年说6", "force": fal...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 流年说6
✅ 发送成功
【进场msg】[110276808402][男]小样哥 进入了直播间
👋 检测到用户进入: 小样哥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 小样哥
⏳ 等待设备完成说话...
【进场msg】[111342653821][男]简单 进入了直播间
👋 检测到用户进入: 简单 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 简单
【直播间统计msg】27在线观众
【统计msg】当前观看人数: 23, 累计观看人数: 1531
[348173] [12:49:46] 【聊天msg】[88383177372583]小᳐卉宝᳐੭: 我呢
[348173] [12:49:46] 🕐 消息时间检查: 启动后 207.0s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=小᳐卉宝᳐੭, 消息=我呢..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 小᳐卉宝᳐੭ (总互动: 1)
📊 房间 431879348173 互动: 小᳐卉宝᳐੭ (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 135
📊 记录聊天互动: 小᳐卉宝᳐੭ -> 我呢...
   ↳ 上述消息重复了 3 次
[348173] [12:49:46] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:49:46]    用户消息: 小᳐卉宝᳐੭: 我呢
   ↳ 上述消息重复了 3 次
[348173] [12:49:46]    设备状态: speaking
🆕 新用户首次发言: 小᳐卉宝᳐੭ (权重:15)
[348173] [12:49:46] 🔥 优先消息已加入队列[位置3]: 小᳐卉宝᳐੭: 我呢 (权重:15)
🆕 新用户消息已加入高优先级队列
[348173] [12:49:48] 【聊天msg】[59820627997995]黑人父: 我呢臭豆包
[348173] [12:49:48] 🕐 消息时间检查: 启动后 209.0s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:49:48] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - blocked
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了2个赞
【进场msg】[2142262254637551][女]MTT 进入了直播间
👋 检测到用户进入: MTT (女)
👋 准备发送欢迎消息: 欢迎MTT进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: MTT
   设备状态: speaking
   欢迎内容: 欢迎MTT进入直播间！
[348173] [12:49:49] 🔥 优先消息已加入队列[位置8]: MTT: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:49:49] 📦 欢迎消息已加入队列等待处理
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了2个赞
[348173] [12:49:52] 【聊天msg】[62517003414]痔尊宝: 带他去洗澡
[348173] [12:49:52] 🕐 消息时间检查: 启动后 212.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=痔尊宝, 消息=带他去洗澡..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 痔尊宝 (总互动: 1)
📊 房间 431879348173 互动: 痔尊宝 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 136
📊 记录聊天互动: 痔尊宝 -> 带他去洗澡...
   ↳ 上述消息重复了 2 次
[348173] [12:49:52] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:49:52]    用户消息: 痔尊宝: 带他去洗澡
   ↳ 上述消息重复了 2 次
[348173] [12:49:52]    设备状态: speaking
🆕 新用户首次发言: 痔尊宝 (权重:15)
🎭 冷场检查: 无活动时间=3.0s, 需要=3s, 上次冷场=1753850958.2s, 冷却=10s
🎭 检测到冷场：listening状态 3.0秒无活动，触发防冷场消息
⚠️ 配置项 'anti_silence' 不存在，使用默认值: {}
💬 生成个性化防冷场消息: 目标用户=砚山音响白姐, 次数=1/2
🎭 发送防冷场消息: 砚山音响白姐，直播间有点安静了，来活跃一下气氛吧！
🎭 最终格式: 砚山音响白姐，直播间有点安静了，来活跃一下气氛吧！ (长度:25)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
[848508] [12:49:18] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[848508] [12:49:18] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:49:18] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:49:18] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:49:18] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
[848508] [12:49:18] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[10]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[10]: 砚山音响白姐，直播间有点安静了，来活跃一下气氛吧！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐，直播间有点安静了，来活跃...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐，直播间有点安静了，来活跃一下气氛吧！
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[64918635091][男]老鹰_194669023 进入了直播间
👋 检测到用户进入: 老鹰_194669023 (男)
👋 准备发送欢迎消息: 欢迎老鹰_194669023进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 老鹰_194669023
   设备状态: speaking
   欢迎内容: 欢迎老鹰_194669023进入直播间！
[848508] [12:49:22] 🔥 优先消息已加入队列[位置0]: 老鹰_194669023: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:49:22] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】6在线观众
【统计msg】当前观看人数: 5, 累计观看人数: 7082
[848508] [12:49:27] 【聊天msg】[3421311587596551]砚山音响白姐: 昨天也是79
[848508] [12:49:27] 🕐 消息时间检查: 启动后 263.4s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=昨天也是79..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 6)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 6)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 砚山音响白姐 -> 昨天也是79...
   ↳ 上述消息重复了 2 次
[848508] [12:49:27] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:49:27]    用户消息: 砚山音响白姐: 昨天也是79
   ↳ 上述消息重复了 2 次
[848508] [12:49:27]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:49:27] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 昨天也是79 (权重:10)
📦 用户消息已加入队列
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 10
[848508] [12:49:28] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 📊 队列状态: 2条消息，本次处理上限: 1条
[848508] [12:49:38] 🔥 队列[1/2] 砚山音响白姐: 昨天也是79...[优先] (10.8s)
   ↳ 上述消息重复了 2 次
[848508] [12:49:38] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[11]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[11]: 砚山音响白姐说昨天也是79
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说昨天也是79", "fo...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说昨天也是79
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 11
   ↳ 上述消息重复了 2 次
[848508] [12:49:48] ✅ 队列消息发送成功: 砚山音响白姐
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[848508] [12:49:56] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:49:56] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=2.0s, 需要=3s, 上次冷场=40.7s, 冷却=10s
🎭 冷场检查: 无活动时间=3.0s, 需要=3s, 上次冷场=41.7s, 冷却=10s
🎭 检测到冷场：listening状态 3.0秒无活动，触发防冷场消息
⚠️ 配置项 'anti_silence' 不存在，使用默认值: {}
💬 生成个性化防冷场消息: 目标用户=砚山音响白姐, 次数=2/2
🎭 发送防冷场消息: 砚山音响白姐，刚才聊得挺开心的，还有什么想聊的吗？
🎭 最终格式: 砚山音响白姐，刚才聊得挺开心的，还有什么想聊的吗？ (长度:25)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 📊 队列状态: 1条消息，本次处理上限: 1条
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[848508] [12:49:59] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[12]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[12]: 砚山音响白姐，刚才聊得挺开心的，还有什么想聊的吗？
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐，刚才聊得挺开心的，还有什...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐，刚才聊得挺开心的，还有什么想聊的吗？
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[88970917108][男]世军讲装修 进入了直播间
[348173] [12:49:52] 🔥 优先消息已加入队列[位置4]: 痔尊宝: 带他去洗澡 (权重:15)
🆕 新用户消息已加入高优先级队列
【进场msg】[79458441922][男]游业 进入了直播间
👋 检测到用户进入: 游业 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 游业
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了1个赞
【进场msg】[94360575762][男]薛无恙🌈 进入了直播间
👋 检测到用户进入: 薛无恙🌈 (男)
👋 准备发送欢迎消息: 欢迎薛无恙x进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 薛无恙🌈
   设备状态: speaking
   欢迎内容: 欢迎薛无恙x进入直播间！
[348173] [12:49:55] 🔥 优先消息已加入队列[位置10]: 薛无恙🌈: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:49:55] 📦 欢迎消息已加入队列等待处理
【进场msg】[61685848441][男]简单 进入了直播间
👋 检测到用户进入: 简单 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 简单
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 12
[348173] [12:49:55] ✅ 队列消息发送成功: 流年
⏸️ 已发送 2 条消息，剩余 10 条消息将在下次处理
✅ 队列处理完成，共处理 14 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[93567821973][男]我爱雪花® 进入了直播间
👋 检测到用户进入: 我爱雪花® (男)
⏳ 欢迎消息冷却中，跳过欢迎: 我爱雪花®
【进场msg】[1207744668376794][女]果蔬侦察社 进入了直播间
👋 检测到用户进入: 果蔬侦察社 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 果蔬侦察社
【进场msg】[91642974464][男]黑白牛 进入了直播间
👋 检测到用户进入: 黑白牛 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 黑白牛
【进场msg】[10787650201][女]冬冬瓜 进入了直播间
👋 检测到用户进入: 冬冬瓜 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 冬冬瓜
【进场msg】[73611600926][男]心机之蛙一直摸你肚子 进入了直播间
👋 检测到用户进入: 心机之蛙一直摸你肚子 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 心机之蛙一直摸你肚子
【直播间统计msg】32在线观众
[348173] [12:50:00] 【聊天msg】[1604938318750302]叫我小清新: 太笨蛋了，太机车了
[348173] [12:50:00] 🕐 消息时间检查: 启动后 220.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=叫我小清新, 消息=太笨蛋了，太机车了..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 叫我小清新 (总互动: 1)
📊 房间 431879348173 互动: 叫我小清新 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 137
📊 记录聊天互动: 叫我小清新 -> 太笨蛋了，太机车了...
   ↳ 上述消息重复了 2 次
[348173] [12:50:00] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:00]    用户消息: 叫我小清新: 太笨蛋了，太机车了
   ↳ 上述消息重复了 2 次
[348173] [12:50:00]    设备状态: speaking
🆕 新用户首次发言: 叫我小清新 (权重:15)
[348173] [12:50:00] 🔥 优先消息已加入队列[位置5]: 叫我小清新: 太笨蛋了，太机车了 (权重:15)
🆕 新用户消息已加入高优先级队列
【统计msg】当前观看人数: 29, 累计观看人数: 1575
[348173] [12:50:00] 【聊天msg】[2126874326077739]皓皓🫚: 这个怎么搞的哈哈哈哈
[348173] [12:50:00] 🕐 消息时间检查: 启动后 220.8s, 过滤启用: True
[348173] [12:50:00] 🔧 消息预处理: '这个怎么搞的哈哈哈哈' → '这个怎么搞的哈哈'
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=这个怎么搞的哈哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 2)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 137
📊 记录聊天互动: 皓皓🫚 -> 这个怎么搞的哈哈...
⏳ 回复冷却中，将消息加入队列等待处理: 这个怎么搞的哈哈
📦 普通用户消息（冷却期）: 皓皓🫚 (权重:10)
[348173] [12:50:00] 🔥 优先消息已加入队列[位置6]: 皓皓🫚: 这个怎么搞的哈哈 (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[1179103373571645][女]可笑可笑 进入了直播间
👋 检测到用户进入: 可笑可笑 (女)
👋 准备发送欢迎消息: 可笑可笑来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 可笑可笑
   设备状态: speaking
   欢迎内容: 可笑可笑来了，大家欢迎！
[348173] [12:50:01] 🔥 优先消息已加入队列[位置23]: 可笑可笑: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:01] 📦 欢迎消息已加入队列等待处理
[348173] [12:50:02] 【聊天msg】[62500362618]你别管: 哟 可以呀
[348173] [12:50:02] 🕐 消息时间检查: 启动后 222.8s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=你别管, 消息=哟 可以呀..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 你别管 (总互动: 1)
📊 房间 431879348173 互动: 你别管 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 138
📊 记录聊天互动: 你别管 -> 哟 可以呀...
[348173] [12:50:02]    用户消息: 你别管: 哟 可以呀
🆕 新用户首次发言: 你别管 (权重:15)
[348173] [12:50:02] 🔥 优先消息已加入队列[位置6]: 你别管: 哟 可以呀 (权重:15)
🆕 新用户消息已加入高优先级队列
   ↳ 上述消息重复了 2 次
[348173] [12:50:02] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:50:02] 📤 开始处理消息队列...
[348173] [12:50:02] 📦 处理队列: 25条消息
[348173] [12:50:02] 🔥 发现 25 条优先消息（礼物回复等）
[348173] [12:50:02] 📊 队列状态: 25条消息，本次处理上限: 3条
[348173] [12:50:02] 🔥 队列[1/25] 两斤包谷酒: 董莹...[优先] (40.6s)
[348173] [12:50:02] 💬 正在处理用户消息: 两斤包谷酒
✅ 设备就绪，状态: listening
🔍 发送调用[13]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[13]: 两斤包谷酒说董莹
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "两斤包谷酒说董莹", "force":...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 两斤包谷酒说董莹
✅ 发送成功
⏳ 等待设备完成说话...
[348173] [12:50:05] 【聊天msg】[100248033767]久之艾美容店(官渡区新亚洲体育城店): 你用的什么模型
[348173] [12:50:05] 🕐 消息时间检查: 启动后 226.0s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=久之艾美容店(官渡区新亚洲体育城店), 消息=你用的什么模型..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 久之艾美容店(官渡区新亚洲体育城店) (总互动: 6)
📊 房间 431879348173 互动: 久之艾美容店(官渡区新亚洲体育城店) (房间互动: 6)
✅ 互动记录完成，当前用户统计总数: 138
📊 记录聊天互动: 久之艾美容店(官渡区新亚洲体育城店) -> 你用的什么模型...
   ↳ 上述消息重复了 3 次
[348173] [12:50:05] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:05]    用户消息: 久之艾美容店(官渡区新亚洲体育城店): 你用的什么模型
   ↳ 上述消息重复了 3 次
[348173] [12:50:05]    设备状态: speaking
📦 普通用户消息: 久之艾美容店(官渡区新亚洲体育城店) (权重:10)
[348173] [12:50:05] 🔥 优先消息已加入队列[位置0]: 久之艾美容店(官渡区新亚洲体育城店): 你用的什么模型 (权重:10)
📦 用户消息已加入队列
【进场msg】[95937102888][女]Clement 进入了直播间
👋 检测到用户进入: Clement (女)
👋 准备发送欢迎消息: Clement来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: Clement
   设备状态: speaking
   欢迎内容: Clement来了，大家欢迎！
[348173] [12:50:07] 🔥 优先消息已加入队列[位置1]: Clement: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:07] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】26在线观众
【统计msg】当前观看人数: 32, 累计观看人数: 1681
【进场msg】[3289345051622103][男]很哈哈 进入了直播间
👋 检测到用户进入: 很哈哈 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 很哈哈
【进场msg】[68110085291][男]不嘻嘻@ 进入了直播间
👋 检测到用户进入: 不嘻嘻@ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 不嘻嘻@
【进场msg】[97446646418][女]已注销 进入了直播间
👋 检测到用户进入: 已注销 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 已注销
[348173] [12:50:12] 【聊天msg】[106483067815]镇雄县隆众珠宝店（个体工商户）: 怕是人工
[348173] [12:50:12] 🕐 消息时间检查: 启动后 232.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=镇雄县隆众珠宝店（个体工商户）, 消息=怕是人工..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 镇雄县隆众珠宝店（个体工商户） (总互动: 1)
📊 房间 431879348173 互动: 镇雄县隆众珠宝店（个体工商户） (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 139
📊 记录聊天互动: 镇雄县隆众珠宝店（个体工商户） -> 怕是人工...
   ↳ 上述消息重复了 2 次
[348173] [12:50:12] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:12]    用户消息: 镇雄县隆众珠宝店（个体工商户）: 怕是人工
   ↳ 上述消息重复了 2 次
[348173] [12:50:12]    设备状态: speaking
🆕 新用户首次发言: 镇雄县隆众珠宝店（个体工商户） (权重:15)
[348173] [12:50:12] 🔥 优先消息已加入队列[位置0]: 镇雄县隆众珠宝店（个体工商户）: 怕是人工 (权重:15)
🆕 新用户消息已加入高优先级队列
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 13
[348173] [12:50:13] ✅ 队列消息发送成功: 两斤包谷酒
[348173] [12:50:13] 🔥 队列[2/25] #1234: 删库...[优先] (51.2s)
[348173] [12:50:13] 💬 正在处理用户消息: #1234
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[97639131934][女]渲色照相馆渲色照相馆 进入了直播间
👋 检测到用户进入: 渲色照相馆渲色照相馆 (女)
👋 准备发送欢迎消息: 渲色照相馆渲色照相馆来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 渲色照相馆渲色照相馆
   设备状态: speaking
   欢迎内容: 渲色照相馆渲色照相馆来了，大家欢迎！
[348173] [12:50:13] 🔥 优先消息已加入队列[位置3]: 渲色照相馆渲色照相馆: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:13] 📦 欢迎消息已加入队列等待处理
【进场msg】[67438291442][男]【扭纹柴】🕸️——🕷️ 进入了直播间
👋 检测到用户进入: 【扭纹柴】🕸️——🕷️ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 【扭纹柴】🕸️——🕷️
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:50:15] 【聊天msg】[2419340659666142]t2hj: 怎么爬的不会是，录屏识别吧[捂脸]
[348173] [12:50:15] 🕐 消息时间检查: 启动后 235.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=t2hj, 消息=怎么爬的不会是，录屏识别吧[捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: t2hj (总互动: 2)
📊 房间 431879348173 互动: t2hj (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 139
📊 记录聊天互动: t2hj -> 怎么爬的不会是，录屏识别吧[捂脸]...
[348173] [12:50:15]    用户消息: t2hj: 怎么爬的不会是，录屏识别吧[捂脸]
📦 普通用户消息: t2hj (权重:10)
[348173] [12:50:15] 🔥 优先消息已加入队列[位置2]: t2hj: 怎么爬的不会是，录屏识别吧[捂脸] (权重:10)
📦 用户消息已加入队列
⏳ 等待设备就绪，当前状态: speaking
【直播间统计msg】30在线观众
【点赞msg】【扭纹柴】🕸️——🕷️ 点了3个赞
👍 【扭纹柴】🕸️——🕷️ 点了3个赞，累计3个赞
🎉 首次点赞互动触发: 【扭纹柴】🕸️——🕷️ 累计3个赞，首次点赞回复 -> 【扭纹柴】🕸️——🕷️刚刚点赞，请表达感谢并邀请他参与到聊天中来
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 【扭纹柴】🕸️——🕷️ (累计3个赞)
   设备状态: speaking
[348173] [12:50:16] 🔥 优先消息已加入队列[位置3]: 【扭纹柴】🕸️——🕷️: 累计点赞3个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:50:16] � 点赞回复已加入优先队列
【进场msg】[72862925897][女]无敌小学生· 进入了直播间
👋 检测到用户进入: 无敌小学生· (女)
⏳ 欢迎消息冷却中，跳过欢迎: 无敌小学生·
【统计msg】当前观看人数: 27, 累计观看人数: 1662
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[72770221307][男]度心 进入了直播间
👋 检测到用户进入: 度心 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 度心
⏳ 等待设备就绪，当前状态: speaking
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: #1234
[348173] [12:50:18] 🔥 队列[3/25] t2hj: 哈哈...[优先] (47.8s)
[348173] [12:50:18] 💬 正在处理用户消息: t2hj
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[1640945922872227][男]流川崎^ 进入了直播间
👋 检测到用户进入: 流川崎^ (男)
👋 准备发送欢迎消息: 流川崎^来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 流川崎^
   设备状态: speaking
   欢迎内容: 流川崎^来了，大家欢迎！
[348173] [12:50:20] 🔥 优先消息已加入队列[位置6]: 流川崎^: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:20] 📦 欢迎消息已加入队列等待处理
✅ 设备就绪，状态: listening
🔍 发送调用[14]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[14]: t2hj说哈哈
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "t2hj说哈哈", "force": ...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: t2hj说哈哈
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[78944350728][女]罗蛳粉 进入了直播间
👋 检测到用户进入: 罗蛳粉 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 罗蛳粉
【进场msg】[2681026997920380][男]站在风口的男孩 进入了直播间
👋 检测到用户进入: 站在风口的男孩 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 站在风口的男孩
[305200] [12:49:28] 【聊天msg】[101847692837]苦瓜: 哈哈啥哈哈
[305200] [12:49:28] 🕐 消息时间检查: 启动后 291.0s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=苦瓜, 消息=哈哈啥哈哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苦瓜 (总互动: 2)
📊 房间 200382305200 互动: 苦瓜 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 苦瓜 -> 哈哈啥哈哈...
🆕 新用户首次发言: 苦瓜 (权重:15)
[305200] [12:49:28] 🔥 优先消息已加入队列[位置0]: 苦瓜: 哈哈啥哈哈 (权重:15)
🆕 新用户消息已加入高优先级队列
   ↳ 上述消息重复了 2 次
[305200] [12:49:28] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:49:28] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:49:28] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:49:28] 🔥 队列[1/1] 苦瓜: 哈哈啥哈哈...[优先] (0.0s)
[305200] [12:49:28] 💬 正在处理用户消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[10]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[10]: 苦瓜说哈哈啥哈哈
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说哈哈啥哈哈", "force":...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说哈哈啥哈哈
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 10
[305200] [12:49:39] ✅ 队列消息发送成功: 苦瓜
✅ 队列处理完成，共处理 1 条消息
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 来自火星的我, score_str=''
【排行榜】第2名: 来自火星的我 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 1, 累计观看人数: 15
【进场msg】[95025694235][女]来自火星的我 进入了直播间
👋 检测到用户进入: 来自火星的我 (女)
👋 准备发送欢迎消息: 来自火星的我来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 来自火星的我
   设备状态: speaking
   欢迎内容: 来自火星的我来了，大家欢迎！
[305200] [12:49:44] 🔥 优先消息已加入队列[位置0]: 来自火星的我: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:49:44] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 1, 累计观看人数: 15
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 1, 累计观看人数: 15
   ↳ 上述消息重复了 2 次
[305200] [12:49:51] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:49:51] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:49:51] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:49:51] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:49:51] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:49:51] 🔥 队列[1/1] 来自火星的我: 进入直播间...[优先] (7.1s)
[305200] [12:49:51] 👋 正在处理欢迎消息: 来自火星的我
✅ 设备就绪，状态: listening
🔍 发送调用[11]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[11]: 来自火星的我来了，大家欢迎！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "来自火星的我来了，大家欢迎！", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 来自火星的我来了，大家欢迎！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 11
[305200] [12:50:01] ✅ 队列消息发送成功: 来自火星的我
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
   ↳ 上述消息重复了 2 次
[305200] [12:50:17] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:50:17] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=4.0s, 需要=5s, 上次冷场=80.7s, 冷却=5s
🎭 冷场检查: 无活动时间=5.0s, 需要=5s, 上次冷场=81.7s, 冷却=5s
🎭 检测到冷场：listening状态 5.0秒无活动，触发防冷场消息
💬 生成个性化防冷场消息: 目标用户=苦瓜, 次数=1/2
🎭 发送防冷场消息: 苦瓜 跑哪儿去了
🎭 最终格式: 苦瓜 跑哪儿去了 (长度:8)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 📊 队列状态: 1条消息，本次处理上限: 1条
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:50:22] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[12]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[12]: 苦瓜 跑哪儿去了
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜 跑哪儿去了", "force":...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜 跑哪儿去了
✅ 发送成功
⏳ 等待设备完成说话...
[305200] [12:50:32] 【聊天msg】[101847692837]苦瓜: 别胡说
[305200] [12:50:32] 🕐 消息时间检查: 启动后 355.6s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=苦瓜, 消息=别胡说..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苦瓜 (总互动: 3)
📊 房间 200382305200 互动: 苦瓜 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 苦瓜 -> 别胡说...
   ↳ 上述消息重复了 2 次
[305200] [12:50:32] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[305200] [12:50:32]    用户消息: 苦瓜: 别胡说
   ↳ 上述消息重复了 2 次
[305200] [12:50:32]    设备状态: speaking
📦 普通用户消息: 苦瓜 (权重:10)
[305200] [12:50:32] 🔥 优先消息已加入队列[位置0]: 苦瓜: 别胡说 (权重:10)
📦 用户消息已加入队列
⏰ 等待超时(10s)，状态: speaking
【直播间统计msg】25在线观众
[348173] [12:50:25] 【聊天msg】[1640945922872227]流川崎^: 哈哈哈[看]
[348173] [12:50:25] 🕐 消息时间检查: 启动后 245.9s, 过滤启用: True
[348173] [12:50:25] 🔧 消息预处理: '哈哈哈[看]' → '哈哈[看]'
🔍 开始记录互动: 房间=431879348173, 用户=流川崎^, 消息=哈哈[看]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 流川崎^ (总互动: 1)
📊 房间 431879348173 互动: 流川崎^ (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 140
📊 记录聊天互动: 流川崎^ -> 哈哈[看]...
   ↳ 上述消息重复了 3 次
[348173] [12:50:25] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:25]    用户消息: 流川崎^: 哈哈[看]
   ↳ 上述消息重复了 3 次
[348173] [12:50:25]    设备状态: speaking
🆕 新用户首次发言: 流川崎^ (权重:15)
[348173] [12:50:25] 🔥 优先消息已加入队列[位置1]: 流川崎^: 哈哈[看] (权重:15)
🆕 新用户消息已加入高优先级队列
【统计msg】当前观看人数: 30, 累计观看人数: 1681
【点赞msg】【扭纹柴】🕸️——🕷️ 点了2个赞
【进场msg】[93032878319][男]⚽何宇笙2017 进入了直播间
👋 检测到用户进入: ⚽何宇笙2017 (男)
👋 准备发送欢迎消息: 欢迎x何宇笙2017进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: ⚽何宇笙2017
   设备状态: speaking
   欢迎内容: 欢迎x何宇笙2017进入直播间！
[348173] [12:50:29] 🔥 优先消息已加入队列[位置8]: ⚽何宇笙2017: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:29] 📦 欢迎消息已加入队列等待处理
【进场msg】[11488869424][男]扎你苦胆 进入了直播间
👋 检测到用户进入: 扎你苦胆 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 扎你苦胆
【点赞msg】【扭纹柴】🕸️——🕷️ 点了7个赞
【进场msg】[2091694595515417][女]山呼-山呼 进入了直播间
👋 检测到用户进入: 山呼-山呼 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 山呼-山呼
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 14
[348173] [12:50:31] ✅ 队列消息发送成功: t2hj
[348173] [12:50:31] 🔥 队列[4/25] 皓皓🫚: 牛皮...[优先] (50.2s)
[348173] [12:50:31] 💬 正在处理用户消息: 皓皓🫚
⏳ 等待设备就绪，当前状态: speaking
【直播间统计msg】28在线观众
【进场msg】[60273362871][男]典韦王 进入了直播间
👋 检测到用户进入: 典韦王 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 典韦王
【统计msg】当前观看人数: 24, 累计观看人数: 1699
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[101529188653][男]红头 进入了直播间
👋 检测到用户进入: 红头 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 红头
【点赞msg】【扭纹柴】🕸️——🕷️ 点了5个赞
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[54697458560][男]盆鱼宴 进入了直播间
👋 检测到用户进入: 盆鱼宴 (男)
👋 准备发送欢迎消息: 欢迎盆鱼宴进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 盆鱼宴
   设备状态: speaking
   欢迎内容: 欢迎盆鱼宴进入直播间！
[348173] [12:50:35] 🔥 优先消息已加入队列[位置9]: 盆鱼宴: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:35] 📦 欢迎消息已加入队列等待处理
[348173] [12:50:35] 【聊天msg】[2126874326077739]皓皓🫚: 你这个太机车了
[348173] [12:50:35] 🕐 消息时间检查: 启动后 255.7s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=你这个太机车了..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 3)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 140
📊 记录聊天互动: 皓皓🫚 -> 你这个太机车了...
   ↳ 上述消息重复了 2 次
[348173] [12:50:35] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:35]    用户消息: 皓皓🫚: 你这个太机车了
   ↳ 上述消息重复了 2 次
[348173] [12:50:35]    设备状态: speaking
📦 普通用户消息: 皓皓🫚 (权重:10)
[348173] [12:50:35] 🔥 优先消息已加入队列[位置4]: 皓皓🫚: 你这个太机车了 (权重:10)
📦 用户消息已加入队列
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:50:36] 【聊天msg】[97446646418]已注销: 给你泡水
[348173] [12:50:36] 🕐 消息时间检查: 启动后 256.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=已注销, 消息=给你泡水..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 已注销 (总互动: 1)
📊 房间 431879348173 互动: 已注销 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 141
📊 记录聊天互动: 已注销 -> 给你泡水...
⏳ 回复冷却中，将消息加入队列等待处理: 给你泡水
🆕 新用户首次发言（冷却期）: 已注销 (权重:15)
[348173] [12:50:36] 🔥 优先消息已加入队列[位置2]: 已注销: 给你泡水 (权重:15)
📦 冷却期消息已加入队列等待处理
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 皓皓🫚
[348173] [12:50:36] 🔥 队列[5/25] 1.7万人: 卸甲...[优先] (53.2s)
[348173] [12:50:36] 💬 正在处理用户消息: 1.7万人
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:50:36] 【聊天msg】[915213361873788]周梓萌: 既然机器人
[348173] [12:50:36] 🕐 消息时间检查: 启动后 257.1s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=周梓萌, 消息=既然机器人..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 周梓萌 (总互动: 1)
📊 房间 431879348173 互动: 周梓萌 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 142
📊 记录聊天互动: 周梓萌 -> 既然机器人...
⏳ 回复冷却中，将消息加入队列等待处理: 既然机器人
🆕 新用户首次发言（冷却期）: 周梓萌 (权重:15)
[348173] [12:50:36] 🔥 优先消息已加入队列[位置3]: 周梓萌: 既然机器人 (权重:15)
📦 冷却期消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
⏳ 等待设备就绪，当前状态: speaking
【点赞msg】【扭纹柴】🕸️——🕷️ 点了9个赞
[348173] [12:50:38] 【聊天msg】[59820627997995]黑人父: 喂喂喂
[348173] [12:50:38] 🕐 消息时间检查: 启动后 258.3s, 过滤启用: True
[348173] [12:50:38] 🔧 消息预处理: '喂喂喂' → '喂喂'
   ↳ 上述消息重复了 2 次
[348173] [12:50:38] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - blocked
【统计msg】当前观看人数: 30, 累计观看人数: 1734
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[15]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[15]: 1.7万人说卸甲
👋 检测到用户进入: 世军讲装修 (男)
👋 准备发送欢迎消息: 世军讲装修欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 世军讲装修
   设备状态: speaking
   欢迎内容: 世军讲装修欢迎你！有什么想聊的吗？
[848508] [12:50:01] 🔥 优先消息已加入队列[位置0]: 世军讲装修: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:50:01] 📦 欢迎消息已加入队列等待处理
【进场msg】[58521152097][女]绘兰 进入了直播间
👋 检测到用户进入: 绘兰 (女)
👋 准备发送欢迎消息: 绘兰欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 绘兰
   设备状态: speaking
   欢迎内容: 绘兰欢迎你！有什么想聊的吗？
[848508] [12:50:04] 🔥 优先消息已加入队列[位置1]: 绘兰: 进入直播间 (权重:1)
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 12
   ↳ 上述消息重复了 2 次
[848508] [12:50:10] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
[848508] [12:50:17] 【聊天msg】[64918635091]老鹰_194669023: 可以语音唤醒吗
[848508] [12:50:17] 🕐 消息时间检查: 启动后 313.5s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=老鹰_194669023, 消息=可以语音唤醒吗..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 老鹰_194669023 (总互动: 1)
📊 房间 739580848508 互动: 老鹰_194669023 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 老鹰_194669023 -> 可以语音唤醒吗...
   ↳ 上述消息重复了 2 次
[848508] [12:50:17] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:50:17]    用户消息: 老鹰_194669023: 可以语音唤醒吗
   ↳ 上述消息重复了 2 次
[848508] [12:50:17]    设备状态: speaking
🆕 新用户首次发言: 老鹰_194669023 (权重:15)
[848508] [12:50:17] 🔥 优先消息已加入队列[位置0]: 老鹰_194669023: 可以语音唤醒吗 (权重:15)
🆕 新用户消息已加入高优先级队列
   ↳ 上述消息重复了 2 次
[848508] [12:50:17] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:50:17] 📤 开始处理消息队列...
[848508] [12:50:17] 📦 处理队列: 3条消息
[848508] [12:50:17] 🔥 发现 3 条优先消息（礼物回复等）
[848508] [12:50:17] 📊 队列状态: 3条消息，本次处理上限: 1条
[848508] [12:50:17] 🔥 队列[1/3] 老鹰_19466: 可以语音唤醒吗...[优先] (0.3s)
[848508] [12:50:17] 💬 正在处理用户消息: 老鹰_19466
✅ 设备就绪，状态: listening
🔍 发送调用[13]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[13]: 老鹰_194669023说可以语音唤醒吗
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "老鹰_194669023说可以语音唤醒...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 老鹰_194669023说可以语音唤醒吗
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】7在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7091
【进场msg】[3959774103218140][男]无语😓 进入了直播间
👋 检测到用户进入: 无语😓 (男)
👋 准备发送欢迎消息: 无语x欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 无语😓
   设备状态: speaking
   欢迎内容: 无语x欢迎你！有什么想聊的吗？
[848508] [12:50:25] 🔥 优先消息已加入队列[位置0]: 无语😓: 进入直播间 (权重:1)
   ↳ 上述消息重复了 3 次
[848508] [12:50:25] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 13
[848508] [12:50:28] ✅ 队列消息发送成功: 老鹰_19466
⏸️ 已发送 1 条消息，剩余 1 条消息将在下次处理
✅ 队列处理完成，共处理 3 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】8在线观众
【统计msg】当前观看人数: 7, 累计观看人数: 7094
   ↳ 上述消息重复了 2 次
[848508] [12:50:36] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:50:36] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:50:36] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[848508] [12:50:36] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:50:36] 📊 队列状态: 2条消息，本次处理上限: 1条
[848508] [12:50:36] 🔥 队列[1/2] 绘兰: 进入直播间...[优先] (32.4s)
[848508] [12:50:36] 👋 正在处理欢迎消息: 绘兰
✅ 设备就绪，状态: listening
🔍 发送调用[14]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[14]: 绘兰欢迎你！有什么想聊的吗？
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "绘兰欢迎你！有什么想聊的吗？", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 绘兰欢迎你！有什么想聊的吗？
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】7在线观众
【进场msg】[104588279646][男]Ai+日课（黄森波工作室） 进入了直播间
👋 检测到用户进入: Ai+日课（黄森波工作室） (男)
👋 准备发送欢迎消息: 欢迎Ai+日课(黄森波工作室)来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: Ai+日课（黄森波工作室）
   设备状态: speaking
   欢迎内容: 欢迎Ai+日课(黄森波工作室)来到直播间，很高兴见到你！
[848508] [12:50:37] 🔥 优先消息已加入队列[位置0]: Ai+日课（黄森波工作室）: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:50:37] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 7, 累计观看人数: 7094
【直播间统计msg】8在线观众
【进场msg】[2840777470256184][男]理想好物 进入了直播间
👋 检测到用户进入: 理想好物 (男)
👋 准备发送欢迎消息: 理想好物欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 理想好物
   设备状态: speaking
   欢迎内容: 理想好物欢迎你！有什么想聊的吗？
[848508] [12:50:41] 🔥 优先消息已加入队列[位置1]: 理想好物: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:50:41] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 7, 累计观看人数: 7095
[848508] [12:50:43] 【聊天msg】[64918635091]老鹰_194669023: 6
[848508] [12:50:43] 🕐 消息时间检查: 启动后 339.9s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=老鹰_194669023, 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 老鹰_194669023 (总互动: 2)
📊 房间 739580848508 互动: 老鹰_194669023 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 老鹰_194669023 -> 6...
   ↳ 上述消息重复了 2 次
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "1.7万人说卸甲", "force":...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 1.7万人说卸甲
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】【扭纹柴】🕸️——🕷️ 点了2个赞
[348173] [12:50:41] 【聊天msg】[101529188653]红头: 哈哈
[348173] [12:50:41] 🕐 消息时间检查: 启动后 261.7s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=红头, 消息=哈哈..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 红头 (总互动: 1)
📊 房间 431879348173 互动: 红头 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 143
📊 记录聊天互动: 红头 -> 哈哈...
   ↳ 上述消息重复了 2 次
[348173] [12:50:41] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:41]    用户消息: 红头: 哈哈
   ↳ 上述消息重复了 2 次
[348173] [12:50:41]    设备状态: speaking
🆕 新用户首次发言: 红头 (权重:15)
[348173] [12:50:41] 🔥 优先消息已加入队列[位置4]: 红头: 哈哈 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
[348173] [12:50:42] 【聊天msg】[91642974464]黑白牛: ？？？
[348173] [12:50:42] 🕐 消息时间检查: 启动后 262.9s, 过滤启用: True
[348173] [12:50:42] 🔧 消息预处理: '？？？' → '？？'
🔍 开始记录互动: 房间=431879348173, 用户=黑白牛, 消息=？？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 黑白牛 (总互动: 1)
📊 房间 431879348173 互动: 黑白牛 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 144
📊 记录聊天互动: 黑白牛 -> ？？...
⏳ 回复冷却中，将消息加入队列等待处理: ？？
🆕 新用户首次发言（冷却期）: 黑白牛 (权重:15)
[348173] [12:50:42] 🔥 优先消息已加入队列[位置5]: 黑白牛: ？？ (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[1902577183621295][男]宇（三角洲行动） 进入了直播间
👋 检测到用户进入: 宇（三角洲行动） (男)
👋 准备发送欢迎消息: 欢迎宇(三角洲行动)进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 宇（三角洲行动）
   设备状态: speaking
   欢迎内容: 欢迎宇(三角洲行动)进入直播间！
[348173] [12:50:43] 🔥 优先消息已加入队列[位置15]: 宇（三角洲行动）: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:43] 📦 欢迎消息已加入队列等待处理
【点赞msg】【扭纹柴】🕸️——🕷️ 点了9个赞
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了3个赞
【进场msg】[95551676598][男]l、 　　　　　　　　　　　　　广告✕ 进入了直播间
👋 检测到用户进入: l、 　　　　　　　　　　　　　广告✕ (男)
⏳ 欢迎消息冷却中，跳过欢迎: l、 　　　　　　　　　　　　　广告✕
[348173] [12:50:46] 【聊天msg】[11488869424]扎你苦胆: 来劈个叉
[348173] [12:50:46] 🕐 消息时间检查: 启动后 266.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=扎你苦胆, 消息=来劈个叉..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 扎你苦胆 (总互动: 1)
📊 房间 431879348173 互动: 扎你苦胆 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 145
📊 记录聊天互动: 扎你苦胆 -> 来劈个叉...
   ↳ 上述消息重复了 2 次
[348173] [12:50:46] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:46]    用户消息: 扎你苦胆: 来劈个叉
   ↳ 上述消息重复了 2 次
[348173] [12:50:46]    设备状态: speaking
🆕 新用户首次发言: 扎你苦胆 (权重:15)
[348173] [12:50:46] 🔥 优先消息已加入队列[位置6]: 扎你苦胆: 来劈个叉 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
[348173] [12:50:46] 【聊天msg】[2126874326077739]皓皓🫚: 删库
[348173] [12:50:46] 🕐 消息时间检查: 启动后 267.0s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=删库..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 4)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 145
📊 记录聊天互动: 皓皓🫚 -> 删库...
⏳ 回复冷却中，将消息加入队列等待处理: 删库
📦 普通用户消息（冷却期）: 皓皓🫚 (权重:10)
[348173] [12:50:46] 🔥 优先消息已加入队列[位置10]: 皓皓🫚: 删库 (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[61132881986][男]李李Li_ 进入了直播间
👋 检测到用户进入: 李李Li_ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 李李Li_
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了11个赞
【进场msg】[86875424203][女]. 进入了直播间
👋 检测到用户进入: . (女)
⏳ 欢迎消息冷却中，跳过欢迎: .
【进场msg】[75508978693][男]海绵宝宝🤪 进入了直播间
👋 检测到用户进入: 海绵宝宝🤪 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 海绵宝宝🤪
【进场msg】[4222576270785806][男]恩赐♠ 进入了直播间
👋 检测到用户进入: 恩赐♠ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 恩赐♠
【进场msg】[67934495902][男].... 进入了直播间
👋 检测到用户进入: .... (男)
⏳ 欢迎消息冷却中，跳过欢迎: ....
【进场msg】[84762464398][男]蜜语花田 进入了直播间
👋 检测到用户进入: 蜜语花田 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 蜜语花田
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【统计msg】当前观看人数: 25, 累计观看人数: 1791
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了6个赞
【进场msg】[83901991598][男]旺仔邱邱糖 进入了直播间
👋 检测到用户进入: 旺仔邱邱糖 (男)
👋 准备发送欢迎消息: 旺仔邱邱糖来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 旺仔邱邱糖
   设备状态: speaking
   欢迎内容: 旺仔邱邱糖来了，大家欢迎！
[348173] [12:50:50] 🔥 优先消息已加入队列[位置18]: 旺仔邱邱糖: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:50] 📦 欢迎消息已加入队列等待处理
【进场msg】[1592512196643821][男]张亚明 进入了直播间
👋 检测到用户进入: 张亚明 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 张亚明
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【进场msg】[94630295553][女]装睡的鹿先生 进入了直播间
👋 检测到用户进入: 装睡的鹿先生 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 装睡的鹿先生
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 cd:72:5c listening → 🗣️speaking ❌
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 欢迎前轱辘不转后轱辘转来到直播间，很高兴见到你！
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cd:72:5c speaking → 👂listening ✅
📱 cd:72:5c listening → 💤idle ✅
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 柒柒说66
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐说6
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: Vl来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 曹老师欢迎你！有什么想聊的吗？
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 果蔬侦察社说你是谁
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:62:74 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 山木同学x来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
🎤 发送: 砚山音响白姐说我想要买一台广场舞音响推荐哈
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 哎呦x不错哦!说哈喽
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:62:74 speaking → 👂listening ✅
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 波西米亚狂想曲说那我呢？
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 欢迎vannicehan进入直播间！
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 花卷儿来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐说[捂脸]你可以来帮我卖音响么[捂脸]
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
🎤 发送: 黑人父说66
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 花卷儿说哈哈
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 黑人父你个刷屏怪，再刷屏把你关小黑屋了！
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 欢迎小龙进入直播间！
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:62:74 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 维城(正在努力)说这个是什么鬼
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 cf:62:74 speaking → 👂listening ✅
📱 cf:62:74 listening → 💤idle ✅
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐说都想
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 花卷儿 跑哪儿去了
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 欢迎喀柒进入直播间！
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐，直播间有点安静了，来活跃一下气氛吧！
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:62:74 idle → 🔄connecting ❌
📱 cf:62:74 connecting → 👂listening ✅
📱 29:f0:a4 speaking → 👂listening ✅
📱 cf:62:74 listening → 💤idle ✅
🎤 发送: 苦瓜说哈哈啥哈哈
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 厶说我嘞个豆
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐说昨天也是79
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 流年说6
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 来自火星的我来了，大家欢迎！
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 砚山音响白姐，刚才聊得挺开心的，还有什么想聊的吗？
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 两斤包谷酒说董莹
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 老鹰_194669023说可以语音唤醒吗
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: t2hj说哈哈
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
🎤 发送: 苦瓜 跑哪儿去了
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 绘兰欢迎你！有什么想聊的吗？
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
📱 b3:df:28 listening → 🗣️speaking ❌
📱 cf:5f:58 speaking → 👂listening ✅
🎤 发送: 1.7万人说卸甲
✅ 发送成功 [直播间 431879348173 -> 设备 10:20:ba:cf:5f:58]
📱 cf:5f:58 listening → 🗣️speaking ❌
📱 29:f0:a4 speaking → 👂listening ✅
🎤 发送: 苦瓜说别胡说
✅ 发送成功 [直播间 200382305200 -> 设备 30:ed:a0:29:f0:a4]
📱 29:f0:a4 listening → 🗣️speaking ❌
📱 b3:df:28 speaking → 👂listening ✅
🎤 发送: 老鹰_194669023说6
✅ 发送成功 [直播间 739580848508 -> 设备 34:cd:b0:b3:df:28]
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 15
[348173] [12:50:50] ✅ 队列消息发送成功: 1.7万人
⏰ 跳过过期消息 [6/25]: 小᳐卉宝᳐੭: 我呢... (已过期 63.6秒)
⏸️ 已发送 3 条消息，剩余 18 条消息将在下次处理
✅ 队列处理完成，共处理 25 条消息
🎧 设备进入listening状态，重置防冷场计时器
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了9个赞
【进场msg】[562559339753662][男]Mr.彪 进入了直播间
👋 检测到用户进入: Mr.彪 (男)
⏳ 欢迎消息冷却中，跳过欢迎: Mr.彪
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
👍 【扭纹柴】🕸️——🕷️ 点了10个赞，累计102个赞
[348173] [12:50:52] 🎉 点赞互动触发: 【扭纹柴】🕸️——🕷️ 累计102个赞，达到100个阈值 -> 【扭纹柴】🕸️——🕷️刚刚点赞，请表达开心的心情并邀请他一起聊天
[348173] [12:50:52] 🔥 【扭纹柴】🕸️——🕷️ 点赞达到102个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 【扭纹柴】🕸️——🕷️ (累计102个赞)
   设备状态: speaking
[348173] [12:50:52] 🔥 优先消息已加入队列[位置12]: 【扭纹柴】🕸️——🕷️: 累计点赞102个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:50:52] � 点赞回复已加入优先队列
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 传播中医的小杨, score_str=''
【排行榜】第2名: 传播中医的小杨 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 29, 累计观看人数: 1816
[348173] [12:50:52] 【聊天msg】[1640945922872227]流川崎^: 你猎奇
[348173] [12:50:52] 🕐 消息时间检查: 启动后 273.0s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=流川崎^, 消息=你猎奇..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 流川崎^ (总互动: 2)
📊 房间 431879348173 互动: 流川崎^ (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 145
📊 记录聊天互动: 流川崎^ -> 你猎奇...
   ↳ 上述消息重复了 2 次
[348173] [12:50:52] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:52]    用户消息: 流川崎^: 你猎奇
   ↳ 上述消息重复了 2 次
[348173] [12:50:52]    设备状态: speaking
📦 普通用户消息: 流川崎^ (权重:10)
[348173] [12:50:52] 🔥 优先消息已加入队列[位置11]: 流川崎^: 你猎奇 (权重:10)
📦 用户消息已加入队列
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了5个赞
[348173] [12:50:53] 【聊天msg】[68122247071]『梵寰』: ？？？
[348173] [12:50:53] 🕐 消息时间检查: 启动后 273.6s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:50:53] 🔧 消息预处理: '？？？' → '？？'
🔍 开始记录互动: 房间=431879348173, 用户=『梵寰』, 消息=？？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 『梵寰』 (总互动: 1)
📊 房间 431879348173 互动: 『梵寰』 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 146
📊 记录聊天互动: 『梵寰』 -> ？？...
⏳ 回复冷却中，将消息加入队列等待处理: ？？
🆕 新用户首次发言（冷却期）: 『梵寰』 (权重:15)
[348173] [12:50:53] 🔥 优先消息已加入队列[位置7]: 『梵寰』: ？？ (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[1187913801667661][男]Alone（深秋） 进入了直播间
👋 检测到用户进入: Alone（深秋） (男)
⏳ 欢迎消息冷却中，跳过欢迎: Alone（深秋）
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
【进场msg】[99065379675][男]心静 进入了直播间
👋 检测到用户进入: 心静 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 心静
[348173] [12:50:56] 【聊天msg】[62517003414]痔尊宝: 带他去洗澡吧
[348173] [12:50:56] 🕐 消息时间检查: 启动后 276.2s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=痔尊宝, 消息=带他去洗澡吧..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 痔尊宝 (总互动: 2)
📊 房间 431879348173 互动: 痔尊宝 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 146
📊 记录聊天互动: 痔尊宝 -> 带他去洗澡吧...
   ↳ 上述消息重复了 2 次
[348173] [12:50:56] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:50:56]    用户消息: 痔尊宝: 带他去洗澡吧
   ↳ 上述消息重复了 2 次
[348173] [12:50:56]    设备状态: speaking
📦 普通用户消息: 痔尊宝 (权重:10)
[348173] [12:50:56] 🔥 优先消息已加入队列[位置13]: 痔尊宝: 带他去洗澡吧 (权重:10)
📦 用户消息已加入队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【直播间统计msg】37在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 镇雄县隆众珠宝店（个体工商户）, score_str=''
【排行榜】第2名: 镇雄县隆众珠宝店（个体工商户） - 贡献值: 第2名
🔍 排行榜第3名: 传播中医的小杨, score_str=''
【排行榜】第3名: 传播中医的小杨 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 29, 累计观看人数: 1816
【进场msg】[61497881123][男]二西莫夫 进入了直播间
👋 检测到用户进入: 二西莫夫 (男)
👋 准备发送欢迎消息: 欢迎二西莫夫进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 二西莫夫
   设备状态: speaking
   欢迎内容: 欢迎二西莫夫进入直播间！
[348173] [12:50:56] 🔥 优先消息已加入队列[位置28]: 二西莫夫: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:50:56] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[348173] [12:50:56] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:50:56] 📤 开始处理消息队列...
[348173] [12:50:56] 📦 处理队列: 29条消息
[348173] [12:50:56] 🔥 发现 29 条优先消息（礼物回复等）
[348173] [12:50:56] 📊 队列状态: 29条消息，本次处理上限: 3条
[348173] [12:50:56] 🔥 队列[1/29] 叫我小清新: 太笨蛋了，太机车了...[优先] (56.9s)
[348173] [12:50:56] 💬 正在处理用户消息: 叫我小清新
✅ 设备就绪，状态: listening
🔍 发送调用[16]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[16]: 叫我小清新说太笨蛋了，太机车了
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "叫我小清新说太笨蛋了，太机车了", "...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 叫我小清新说太笨蛋了，太机车了
✅ 发送成功
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了9个赞
⏳ 等待设备完成说话...
【进场msg】[2379811626952174][女]火眼包 进入了直播间
👋 检测到用户进入: 火眼包 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 火眼包
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【进场msg】[697561210955017][女].:兔兔故事汇 进入了直播间
👋 检测到用户进入: .:兔兔故事汇 (女)
⏳ 欢迎消息冷却中，跳过欢迎: .:兔兔故事汇
【统计msg】当前观看人数: 28, 累计观看人数: 1835
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了9个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 镇雄县隆众珠宝店（个体工商户）, score_str=''
【排行榜】第2名: 镇雄县隆众珠宝店（个体工商户） - 贡献值: 第2名
🔍 排行榜第3名: 传播中医的小杨, score_str=''
【排行榜】第3名: 传播中医的小杨 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
【统计msg】当前观看人数: 29, 累计观看人数: 1816
【点赞msg】镇雄县隆众珠宝店（个体工商户） 点了4个赞
【进场msg】[62195447582][男]任我上青云 进入了直播间
👋 检测到用户进入: 任我上青云 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 任我上青云
【进场msg】[98874355965][男]冉染 进入了直播间
👋 检测到用户进入: 冉染 (男)
👋 准备发送欢迎消息: 冉染来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 冉染
   设备状态: speaking
   欢迎内容: 冉染来了，大家欢迎！
[348173] [12:51:02] 🔥 优先消息已加入队列[位置0]: 冉染: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:51:02] 📦 欢迎消息已加入队列等待处理
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
[348173] [12:51:02] 【聊天msg】[1207744668376794]果蔬侦察社: 6
[348173] [12:51:02] 🕐 消息时间检查: 启动后 282.8s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=果蔬侦察社, 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 果蔬侦察社 (总互动: 3)
📊 房间 431879348173 互动: 果蔬侦察社 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 146
📊 记录聊天互动: 果蔬侦察社 -> 6...
   ↳ 上述消息重复了 2 次
[348173] [12:51:02] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:51:02]    用户消息: 果蔬侦察社: 6
   ↳ 上述消息重复了 2 次
[348173] [12:51:02]    设备状态: speaking
📦 普通用户消息: 果蔬侦察社 (权重:10)
[348173] [12:51:02] 🔥 优先消息已加入队列[位置0]: 果蔬侦察社: 6 (权重:10)
📦 用户消息已加入队列
   ↳ 上述消息重复了 2 次
[348173] [12:51:02] 【聊天msg】[2126874326077739]皓皓🫚: 删库
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=删库..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 5)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 5)
✅ 互动记录完成，当前用户统计总数: 146
📊 记录聊天互动: 皓皓🫚 -> 删库...
⏳ 回复冷却中，将消息加入队列等待处理: 删库
📦 普通用户消息（冷却期）: 皓皓🫚 (权重:10)
[348173] [12:51:02] 🔥 优先消息已加入队列[位置1]: 皓皓🫚: 删库 (权重:10)
📦 冷却期消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 镇雄县隆众珠宝店（个体工商户）, score_str=''
【排行榜】第2名: 镇雄县隆众珠宝店（个体工商户） - 贡献值: 第2名
🔍 排行榜第3名: 传播中医的小杨, score_str=''
【排行榜】第3名: 传播中医的小杨 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】【扭纹柴】🕸️——🕷️ 点了9个赞
【统计msg】当前观看人数: 28, 累计观看人数: 1835
【直播间统计msg】36在线观众
【进场msg】[2034550221843584][男]HANG. 进入了直播间
👋 检测到用户进入: HANG. (男)
⏳ 欢迎消息冷却中，跳过欢迎: HANG.
【点赞msg】【扭纹柴】🕸️——🕷️ 点了9个赞
【统计msg】当前观看人数: 28, 累计观看人数: 1835
【进场msg】[3573884034233688][男]🈯️尖上的💫☁️      🔥 进入了直播间
👋 检测到用户进入: 🈯️尖上的💫☁️      🔥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 🈯️尖上的💫☁️      🔥
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 16
[348173] [12:51:07] ✅ 队列消息发送成功: 叫我小清新
⏰ 跳过过期消息 [2/29]: 你别管: 哟 可以呀... (已过期 65.1秒)
[348173] [12:51:07] 🔥 队列[3/29] 镇雄县隆众珠宝店: 怕是人工...[优先] (55.3s)
[348173] [12:51:07] 💬 正在处理用户消息: 镇雄县隆众珠宝店
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[100904548420][男]Panda 进入了直播间
👋 检测到用户进入: Panda (男)
👋 准备发送欢迎消息: 欢迎Panda进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: Panda
   设备状态: speaking
   欢迎内容: 欢迎Panda进入直播间！
[348173] [12:51:07] 🔥 优先消息已加入队列[位置3]: Panda: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:51:07] 📦 欢迎消息已加入队列等待处理
【进场msg】[7512262893120128058][女]憨图 进入了直播间
👋 检测到用户进入: 憨图 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 憨图
[348173] [12:51:07] 【聊天msg】[1640945922872227]流川崎^: 别直播了
[348173] [12:51:07] 🕐 消息时间检查: 启动后 288.1s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=流川崎^, 消息=别直播了..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 流川崎^ (总互动: 3)
📊 房间 431879348173 互动: 流川崎^ (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 146
📊 记录聊天互动: 流川崎^ -> 别直播了...
   ↳ 上述消息重复了 2 次
[348173] [12:51:07] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:51:07]    用户消息: 流川崎^: 别直播了
   ↳ 上述消息重复了 2 次
[348173] [12:51:07]    设备状态: speaking
📦 普通用户消息: 流川崎^ (权重:10)
[348173] [12:51:07] 🔥 优先消息已加入队列[位置2]: 流川崎^: 别直播了 (权重:10)
📦 用户消息已加入队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[106147532382][女]不忘记初心。 进入了直播间
👋 检测到用户进入: 不忘记初心。 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 不忘记初心。
【进场msg】[2746999803748488][男]无肉不欢小胖墩 进入了直播间
👋 检测到用户进入: 无肉不欢小胖墩 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 无肉不欢小胖墩
【进场msg】[86875424203][女]. 进入了直播间
👋 检测到用户进入: . (女)
⏳ 欢迎消息冷却中，跳过欢迎: .
⏳ 等待设备就绪，当前状态: speaking
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 12
   ↳ 上述消息重复了 2 次
[305200] [12:50:33] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
💾 开始保存数据: 114 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:50:42] 🔥 队列[1/1] 苦瓜: 别胡说...[优先] (10.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:50:42] 💬 正在处理用户消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[13]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[13]: 苦瓜说别胡说
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说别胡说", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说别胡说
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】苦瓜 点了2个赞
👍 苦瓜 点了2个赞，累计2个赞
🎉 首次点赞互动触发: 苦瓜 累计2个赞，首次点赞回复 -> 苦瓜说给你点赞了 夸夸她
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 苦瓜 (累计2个赞)
   设备状态: speaking
[305200] [12:50:52] 🔥 优先消息已加入队列[位置0]: 苦瓜: 累计点赞2个 (权重:5)
[305200] [12:50:52] � 点赞回复已加入优先队列
【点赞msg】苦瓜 点了3个赞
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 13
   ↳ 上述消息重复了 2 次
[305200] [12:50:53] ✅ 队列消息发送成功: 苦瓜
✅ 队列处理完成，共处理 1 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】1在线观众
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 1, 累计观看人数: 19
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
[305200] [12:50:56] 【聊天msg】[101847692837]苦瓜: 不真诚一点都
[305200] [12:50:56] 🕐 消息时间检查: 启动后 378.8s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=苦瓜, 消息=不真诚一点都..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苦瓜 (总互动: 4)
📊 房间 200382305200 互动: 苦瓜 (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 苦瓜 -> 不真诚一点都...
   ↳ 上述消息重复了 2 次
[305200] [12:50:56] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[305200] [12:50:56]    用户消息: 苦瓜: 不真诚一点都
   ↳ 上述消息重复了 2 次
[305200] [12:50:56]    设备状态: speaking
📦 普通用户消息: 苦瓜 (权重:10)
[305200] [12:50:56] 🔥 优先消息已加入队列[位置0]: 苦瓜: 不真诚一点都 (权重:10)
📦 用户消息已加入队列
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 📊 队列状态: 2条消息，本次处理上限: 1条
[305200] [12:51:04] 🔥 队列[1/2] 苦瓜: 不真诚一点都...[优先] (8.6s)
   ↳ 上述消息重复了 2 次
[305200] [12:51:04] 💬 正在处理用户消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[14]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[14]: 苦瓜说不真诚一点都
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说不真诚一点都", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说不真诚一点都
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
👍 苦瓜 点了11个赞，累计104个赞
[305200] [12:51:05] 🎉 点赞互动触发: 苦瓜 累计104个赞，达到100个阈值 -> 苦瓜说给你点个赞 你是最棒的
[305200] [12:51:05] 🔥 苦瓜 点赞达到104个，获得1条优先权
📦 点赞回复加入优先队列（权重:5-中等优先级）
[305200] [12:51:05] 🔥 优先消息已加入队列[位置0]: 苦瓜: 累计点赞104个 (权重:5)
📦 点赞回复已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:51:05] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:51:05] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:51:05] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:51:05] 🔥 队列[1/1] 苦瓜: 累计点赞104个...[优先] (0.0s)
[305200] [12:51:05] 👍 正在处理点赞消息: 苦瓜
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[305200] [12:51:05] ❌ 队列消息发送失败: 苦瓜 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 苦瓜
📦 消息已重新入队 (重试次数: 1/2)
✅ 队列处理完成，共处理 1 条消息
【点赞msg】苦瓜 点了12个赞
【点赞msg】苦瓜 点了8个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 14
   ↳ 上述消息重复了 2 次
[305200] [12:51:15] ✅ 队列消息发送成功: 苦瓜
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎭 冷场检查: 无活动时间=10.8s, 需要=5s, 上次冷场=52.5s, 冷却=5s
🎭 检测到冷场：listening状态 10.8秒无活动，触发防冷场消息
💬 生成个性化防冷场消息: 目标用户=苦瓜, 次数=2/2
🎭 发送防冷场消息: 苦瓜 跑哪儿去了
🎭 最终格式: 苦瓜 跑哪儿去了 (长度:8)
🚫 防冷场消息被阻止发送: 设备状态为 speaking，不允许发送消息
   设备状态: speaking
   防冷场内容: 苦瓜 跑哪儿去了
[305200] [12:51:15] 🔥 优先消息已加入队列[位置1]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列等待处理
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了10个赞
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[64201412983][男]肥宅并不肥 进入了直播间
👋 检测到用户进入: 肥宅并不肥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 肥宅并不肥
【进场msg】[1808037262669230][女]如果你也喜欢盖佳一 进入了直播间
👋 检测到用户进入: 如果你也喜欢盖佳一 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 如果你也喜欢盖佳一
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 镇雄县隆众珠宝店
[348173] [12:51:12] 🔥 队列[4/29] 流川崎^: 哈哈[看]...[优先] (47.1s)
[348173] [12:51:12] 💬 正在处理用户消息: 流川崎^
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:51:14] 【聊天msg】[59820627997995]黑人父: 为什么不念我的名字？
[348173] [12:51:14] 🕐 消息时间检查: 启动后 294.7s, 过滤启用: True
   ↳ 上述消息重复了 2 次
[348173] [12:51:14] 🔥 ⚠️ 检测到刷屏行为: 黑人父 - blocked
✅ 设备就绪，状态: listening
🔍 发送调用[17]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[17]: 流川崎^说哈哈[看]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "流川崎^说哈哈[看]", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 流川崎^说哈哈[看]
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】38在线观众
【统计msg】当前观看人数: 38, 累计观看人数: 1952
[348173] [12:51:16] 【聊天msg】[106147532382]不忘记初心。: ?
[348173] [12:51:16] 🕐 消息时间检查: 启动后 296.7s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=不忘记初心。, 消息=?..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 不忘记初心。 (总互动: 1)
📊 房间 431879348173 互动: 不忘记初心。 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 147
📊 记录聊天互动: 不忘记初心。 -> ?...
   ↳ 上述消息重复了 2 次
[348173] [12:51:16] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:51:16]    用户消息: 不忘记初心。: ?
   ↳ 上述消息重复了 2 次
[348173] [12:51:16]    设备状态: speaking
🆕 新用户首次发言: 不忘记初心。 (权重:15)
[348173] [12:51:16] 🔥 优先消息已加入队列[位置0]: 不忘记初心。: ? (权重:15)
🆕 新用户消息已加入高优先级队列
[348173] [12:51:17] 【聊天msg】[103795835639]yyyy.: 6
[348173] [12:51:17] 🕐 消息时间检查: 启动后 297.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=yyyy., 消息=6..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: yyyy. (总互动: 1)
📊 房间 431879348173 互动: yyyy. (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 148
📊 记录聊天互动: yyyy. -> 6...
⏳ 回复冷却中，将消息加入队列等待处理: 6
🆕 新用户首次发言（冷却期）: yyyy. (权重:15)
[348173] [12:51:17] 🔥 优先消息已加入队列[位置1]: yyyy.: 6 (权重:15)
📦 冷却期消息已加入队列等待处理
[348173] [12:51:18] 【聊天msg】[101529188653]红头: 哈哈哈哈哈还差
[348173] [12:51:18] 🕐 消息时间检查: 启动后 298.7s, 过滤启用: True
[348173] [12:51:18] 🔧 消息预处理: '哈哈哈哈哈还差' → '哈哈还差'
🔍 开始记录互动: 房间=431879348173, 用户=红头, 消息=哈哈还差..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 红头 (总互动: 2)
📊 房间 431879348173 互动: 红头 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 148
📊 记录聊天互动: 红头 -> 哈哈还差...
[348173] [12:51:18]    用户消息: 红头: 哈哈还差
📦 普通用户消息: 红头 (权重:10)
[348173] [12:51:18] 🔥 优先消息已加入队列[位置5]: 红头: 哈哈还差 (权重:10)
📦 用户消息已加入队列
[348173] [12:51:19] 【聊天msg】[86875424203].: 推荐一组死机代码
[348173] [12:51:19] 🕐 消息时间检查: 启动后 299.9s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=., 消息=推荐一组死机代码..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: . (总互动: 1)
📊 房间 431879348173 互动: . (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 149
📊 记录聊天互动: . -> 推荐一组死机代码...
⏳ 回复冷却中，将消息加入队列等待处理: 推荐一组死机代码
🆕 新用户首次发言（冷却期）: . (权重:15)
[348173] [12:51:19] 🔥 优先消息已加入队列[位置2]: .: 推荐一组死机代码 (权重:15)
📦 冷却期消息已加入队列等待处理
[348173] [12:51:21] 【聊天msg】[67438291442]【扭纹柴】🕸️——🕷️: 这是干嘛的？
[348173] [12:51:21] 🕐 消息时间检查: 启动后 301.9s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=【扭纹柴】🕸️——🕷️, 消息=这是干嘛的？..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 【扭纹柴】🕸️——🕷️ (总互动: 1)
📊 房间 431879348173 互动: 【扭纹柴】🕸️——🕷️ (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 150
📊 记录聊天互动: 【扭纹柴】🕸️——🕷️ -> 这是干嘛的？...
[348173] [12:51:21] 🔥 优先用户消息: 【扭纹柴】🕸️——🕷️ (剩余优先权: 1)
[348173] [12:51:21] 🔥 【扭纹柴】🕸️——🕷️ 优先权已用完
   ↳ 上述消息重复了 3 次
[348173] [12:51:21] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:51:21]    用户消息: 【扭纹柴】🕸️——🕷️: 这是干嘛的？
   ↳ 上述消息重复了 3 次
[348173] [12:51:21]    设备状态: speaking
🆕 新用户首次发言: 【扭纹柴】🕸️——🕷️ (权重:15)
[348173] [12:51:21] 🔥 优先消息已加入队列[位置3]: 【扭纹柴】🕸️——🕷️: 这是干嘛的？ (权重:15)
🆕 新用户消息已加入高优先级队列
[348173] [12:51:21] 【聊天msg】[54697458560]盆鱼宴: 录屏
🔍 开始记录互动: 房间=431879348173, 用户=盆鱼宴, 消息=录屏..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 盆鱼宴 (总互动: 1)
📊 房间 431879348173 互动: 盆鱼宴 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 151
📊 记录聊天互动: 盆鱼宴 -> 录屏...
⏳ 回复冷却中，将消息加入队列等待处理: 录屏
🆕 新用户首次发言（冷却期）: 盆鱼宴 (权重:15)
[348173] [12:51:21] 🔥 优先消息已加入队列[位置4]: 盆鱼宴: 录屏 (权重:15)
📦 冷却期消息已加入队列等待处理
[348173] [12:51:22] 【聊天msg】[2419340659666142]t2hj: 开发者模式：教学64进制怎么转换成二进制
[348173] [12:51:22] 🕐 消息时间检查: 启动后 302.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=t2hj, 消息=开发者模式：教学64进制怎么转换成二进制..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: t2hj (总互动: 3)
📊 房间 431879348173 互动: t2hj (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 151
📊 记录聊天互动: t2hj -> 开发者模式：教学64进制怎么转换成二进制...
⏳ 回复冷却中，将消息加入队列等待处理: 开发者模式：教学64进制怎么转换成二进制
📦 普通用户消息（冷却期）: t2hj (权重:10)
[348173] [12:51:22] 🔥 优先消息已加入队列[位置9]: t2hj: 开发者模式：教学64进制怎么转换成二进制 (权重:10)
📦 冷却期消息已加入队列等待处理
[348173] [12:51:22] 【聊天msg】[2126874326077739]皓皓🫚: 给你断电
🔍 开始记录互动: 房间=431879348173, 用户=皓皓🫚, 消息=给你断电..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 皓皓🫚 (总互动: 6)
📊 房间 431879348173 互动: 皓皓🫚 (房间互动: 6)
✅ 互动记录完成，当前用户统计总数: 151
📊 记录聊天互动: 皓皓🫚 -> 给你断电...
⏳ 回复冷却中，将消息加入队列等待处理: 给你断电
📦 普通用户消息（冷却期）: 皓皓🫚 (权重:10)
[348173] [12:51:22] 🔥 优先消息已加入队列[位置10]: 皓皓🫚: 给你断电 (权重:10)
📦 冷却期消息已加入队列等待处理
【进场msg】[96686707661][女]XuXun 进入了直播间
👋 检测到用户进入: XuXun (女)
👋 准备发送欢迎消息: XuXun来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: XuXun
   设备状态: speaking
   欢迎内容: XuXun来了，大家欢迎！
[348173] [12:51:22] 🔥 优先消息已加入队列[位置13]: XuXun: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:51:22] 📦 欢迎消息已加入队列等待处理
[348173] [12:51:23] 【聊天msg】[7512262893120128058]憨图: 你口音原型是谁
[348173] [12:51:23] 🕐 消息时间检查: 启动后 303.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=憨图, 消息=你口音原型是谁..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 憨图 (总互动: 1)
📊 房间 431879348173 互动: 憨图 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 152
📊 记录聊天互动: 憨图 -> 你口音原型是谁...
⏳ 回复冷却中，将消息加入队列等待处理: 你口音原型是谁
🆕 新用户首次发言（冷却期）: 憨图 (权重:15)
[348173] [12:51:23] 🔥 优先消息已加入队列[位置5]: 憨图: 你口音原型是谁 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[3924571229792378][男]阿华@ 进入了直播间
👋 检测到用户进入: 阿华@ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 阿华@
【进场msg】[103795835639][男]yyyy. 进入了直播间
👋 检测到用户进入: yyyy. (男)
⏳ 欢迎消息冷却中，跳过欢迎: yyyy.
【进场msg】[100497647616][男]苍穹巫诗 进入了直播间
👋 检测到用户进入: 苍穹巫诗 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 苍穹巫诗
[348173] [12:51:23] 【聊天msg】[106483067815]镇雄县隆众珠宝店（个体工商户）: 是有多无聊然后在这个里面看
[348173] [12:51:23] 🕐 消息时间检查: 启动后 303.9s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=镇雄县隆众珠宝店（个体工商户）, 消息=是有多无聊然后在这个里面看..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 镇雄县隆众珠宝店（个体工商户） (总互动: 2)
📊 房间 431879348173 互动: 镇雄县隆众珠宝店（个体工商户） (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 152
📊 记录聊天互动: 镇雄县隆众珠宝店（个体工商户） -> 是有多无聊然后在这个里面看...
[348173] [12:51:23]    用户消息: 镇雄县隆众珠宝店（个体工商户）: 是有多无聊然后在这个里面看
📦 普通用户消息: 镇雄县隆众珠宝店（个体工商户） (权重:10)
[348173] [12:51:23] 🔥 优先消息已加入队列[位置12]: 镇雄县隆众珠宝店（个体工商户）: 是有多无聊然后在这个里面看 (权重:10)
📦 用户消息已加入队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了3个赞
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 17
[348173] [12:51:25] ✅ 队列消息发送成功: 流川崎^
[348173] [12:51:25] 🔥 队列[5/29] 已注销: 给你泡水...[优先] (49.3s)
[348173] [12:51:25] 💬 正在处理用户消息: 已注销
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[14025540098][男]百世可罗 进入了直播间
👋 检测到用户进入: 百世可罗 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 百世可罗
【进场msg】[3466073974506766][女]🥭🥭🥭 进入了直播间
👋 检测到用户进入: 🥭🥭🥭 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 🥭🥭🥭
【点赞msg】【扭纹柴】🕸️——🕷️ 点了9个赞
👍 【扭纹柴】🕸️——🕷️ 点了9个赞，累计102个赞
[348173] [12:51:26] 🎉 点赞互动触发: 【扭纹柴】🕸️——🕷️ 累计102个赞，达到100个阈值 -> 【扭纹柴】🕸️——🕷️点了赞，请感谢他的支持并主动找话题和他互动
   ↳ 上述消息重复了 2 次
[348173] [12:51:26] 🔥 【扭纹柴】🕸️——🕷️ 点赞达到102个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 【扭纹柴】🕸️——🕷️ (累计102个赞)
   设备状态: speaking
[348173] [12:51:26] 🔥 优先消息已加入队列[位置13]: 【扭纹柴】🕸️——🕷️: 累计点赞102个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:51:26] � 点赞回复已加入优先队列
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[71621980434][女]折叠的群聊 进入了直播间
👋 检测到用户进入: 折叠的群聊 (女)
👋 准备发送欢迎消息: 欢迎折叠的群聊进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 折叠的群聊
   设备状态: speaking
   欢迎内容: 欢迎折叠的群聊进入直播间！
[348173] [12:51:27] 🔥 优先消息已加入队列[位置17]: 折叠的群聊: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:51:27] 📦 欢迎消息已加入队列等待处理
[348173] [12:51:28] 【聊天msg】[1187913801667661]Alone（深秋）: 快删了
[348173] [12:51:28] 🕐 消息时间检查: 启动后 308.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=Alone（深秋）, 消息=快删了..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: Alone（深秋） (总互动: 1)
📊 房间 431879348173 互动: Alone（深秋） (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 153
📊 记录聊天互动: Alone（深秋） -> 快删了...
   ↳ 上述消息重复了 3 次
[348173] [12:51:28] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:51:28]    用户消息: Alone（深秋）: 快删了
   ↳ 上述消息重复了 3 次
[348173] [12:51:28]    设备状态: speaking
🆕 新用户首次发言: Alone（深秋） (权重:15)
[348173] [12:51:28] 🔥 优先消息已加入队列[位置6]: Alone（深秋）: 快删了 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
⏳ 等待设备就绪，当前状态: speaking
[348173] [12:51:29] 【聊天msg】[1808037262669230]如果你也喜欢盖佳一: 你说话很机车哦
[348173] [12:51:29] 🕐 消息时间检查: 启动后 309.1s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=如果你也喜欢盖佳一, 消息=你说话很机车哦..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 如果你也喜欢盖佳一 (总互动: 1)
📊 房间 431879348173 互动: 如果你也喜欢盖佳一 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 154
[848508] [12:50:43] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:50:43]    用户消息: 老鹰_194669023: 6
   ↳ 上述消息重复了 2 次
[848508] [12:50:43]    设备状态: speaking
📦 普通用户消息: 老鹰_194669023 (权重:10)
[848508] [12:50:43] 🔥 优先消息已加入队列[位置0]: 老鹰_194669023: 6 (权重:10)
📦 用户消息已加入队列
【进场msg】[1040563173594432][女]往事如风的 进入了直播间
👋 检测到用户进入: 往事如风的 (女)
👋 准备发送欢迎消息: 往事如风的欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 往事如风的
   设备状态: speaking
   欢迎内容: 往事如风的欢迎你！有什么想聊的吗？
[848508] [12:50:45] 🔥 优先消息已加入队列[位置3]: 往事如风的: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:50:45] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 14
[848508] [12:50:47] ✅ 队列消息发送成功: 绘兰
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
[848508] [12:50:52] 【聊天msg】[3421311587596551]砚山音响白姐: 还是聊音响吧，怎么把音响多卖[捂脸]
[848508] [12:50:52] 🕐 消息时间检查: 启动后 348.6s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=还是聊音响吧，怎么把音响多卖[捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 7)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 7)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 砚山音响白姐 -> 还是聊音响吧，怎么把音响多卖[捂脸]...
   ↳ 上述消息重复了 2 次
[848508] [12:50:52] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:50:52]    用户消息: 砚山音响白姐: 还是聊音响吧，怎么把音响多卖[捂脸]
   ↳ 上述消息重复了 2 次
[848508] [12:50:52]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:50:52] 🔥 优先消息已加入队列[位置1]: 砚山音响白姐: 还是聊音响吧，怎么把音响多卖[捂脸] (权重:10)
📦 用户消息已加入队列
【直播间统计msg】9在线观众
【统计msg】当前观看人数: 8, 累计观看人数: 7097
   ↳ 上述消息重复了 2 次
[848508] [12:50:55] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:50:55] 📤 开始处理消息队列...
[848508] [12:50:55] 📦 处理队列: 5条消息
[848508] [12:50:55] 🔥 发现 5 条优先消息（礼物回复等）
[848508] [12:50:55] 📊 队列状态: 5条消息，本次处理上限: 1条
[848508] [12:50:55] 🔥 队列[1/5] 老鹰_19466: 6...[优先] (11.6s)
   ↳ 上述消息重复了 2 次
[848508] [12:50:55] 💬 正在处理用户消息: 老鹰_19466
✅ 设备就绪，状态: listening
🔍 发送调用[15]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[15]: 老鹰_194669023说6
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "老鹰_194669023说6", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 老鹰_194669023说6
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】10在线观众
【统计msg】当前观看人数: 8, 累计观看人数: 7097
💾 开始保存数据: 115 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
【进场msg】[4493083984540787][男]大展宏图 进入了直播间
👋 检测到用户进入: 大展宏图 (男)
👋 准备发送欢迎消息: 欢迎大展宏图进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 大展宏图
   设备状态: speaking
   欢迎内容: 欢迎大展宏图进入直播间！
[848508] [12:51:05] 🔥 优先消息已加入队列[位置0]: 大展宏图: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:51:05] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 15
   ↳ 上述消息重复了 2 次
[848508] [12:51:06] ✅ 队列消息发送成功: 老鹰_19466
⏸️ 已发送 1 条消息，剩余 3 条消息将在下次处理
✅ 队列处理完成，共处理 5 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[1929020965399610][女]ljy85292 进入了直播间
👋 检测到用户进入: ljy85292 (女)
👋 准备发送欢迎消息: ljy85292欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: ljy85292
   设备状态: speaking
   欢迎内容: ljy85292欢迎你！有什么想聊的吗？
[848508] [12:51:06] 🔥 优先消息已加入队列[位置4]: ljy85292: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:51:13] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:51:13] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:51:13] 📦 处理队列: 5条消息
   ↳ 上述消息重复了 2 次
[848508] [12:51:13] 🔥 发现 5 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:51:13] 📊 队列状态: 5条消息，本次处理上限: 1条
[848508] [12:51:13] 🔥 队列[1/5] Ai+日课（黄森: 进入直播间...[优先] (35.3s)
[848508] [12:51:13] 👋 正在处理欢迎消息: Ai+日课（黄森
✅ 设备就绪，状态: listening
🔍 发送调用[16]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[16]: 欢迎Ai+日课(黄森波工作室)来到直播间，很高兴见到你！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎Ai+日课(黄森波工作室)来到直播...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 欢迎Ai+日课(黄森波工作室)来到直播间，很高兴见到你！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 16
[848508] [12:51:23] ✅ 队列消息发送成功: Ai+日课（黄森
⏸️ 已发送 1 条消息，剩余 3 条消息将在下次处理
✅ 队列处理完成，共处理 5 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】9在线观众
【进场msg】[610979410618446][男]用户61304524 进入了直播间
👋 检测到用户进入: 用户61304524 (男)
👋 准备发送欢迎消息: 用户61304524欢迎你！有什么想聊的吗？
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 用户61304524
   设备状态: speaking
   欢迎内容: 用户61304524欢迎你！有什么想聊的吗？
[848508] [12:51:30] 🔥 优先消息已加入队列[位置3]: 用户61304524: 进入直播间 (权重:1)
   ↳ 上述消息重复了 3 次
[848508] [12:51:30] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[848508] [12:51:30] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次📊 记录聊天互动: 如果你也喜欢盖佳一 -> 你说话很机车哦...
⏳ 回复冷却中，将消息加入队列等待处理: 你说话很机车哦
🆕 新用户首次发言（冷却期）: 如果你也喜欢盖佳一 (权重:15)
[348173] [12:51:29] 🔥 优先消息已加入队列[位置7]: 如果你也喜欢盖佳一: 你说话很机车哦 (权重:15)
📦 冷却期消息已加入队列等待处理
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[78526948343][男]二次蘸酱 进入了直播间
👋 检测到用户进入: 二次蘸酱 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 二次蘸酱
[348173] [12:51:30] 【聊天msg】[61132881986]李李Li_: 你为什么不叫我
[348173] [12:51:30] 🕐 消息时间检查: 启动后 310.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=李李Li_, 消息=你为什么不叫我..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 李李Li_ (总互动: 1)
📊 房间 431879348173 互动: 李李Li_ (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 155
📊 记录聊天互动: 李李Li_ -> 你为什么不叫我...
[348173] [12:51:30]    用户消息: 李李Li_: 你为什么不叫我
🆕 新用户首次发言: 李李Li_ (权重:15)
[348173] [12:51:30] 🔥 优先消息已加入队列[位置8]: 李李Li_: 你为什么不叫我 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 已注销
[348173] [12:51:30] 🔥 队列[6/29] 周梓萌: 既然机器人...[优先] (53.7s)
[348173] [12:51:30] 💬 正在处理用户消息: 周梓萌
⏳ 等待设备就绪，当前状态: speaking
【进场msg】[3285798906763916][男]如来佛祖 进入了直播间
👋 检测到用户进入: 如来佛祖 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 如来佛祖
【进场msg】[4046677306259435][男]喝酒上脸的小段 进入了直播间
👋 检测到用户进入: 喝酒上脸的小段 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 喝酒上脸的小段
⏳ 等待设备就绪，当前状态: speaking
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【进场msg】[101195784911][女]精神食粮补给站 进入了直播间
👋 检测到用户进入: 精神食粮补给站 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 精神食粮补给站
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[18]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[18]: 周梓萌说既然机器人
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "周梓萌说既然机器人", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 周梓萌说既然机器人
✅ 发送成功
【进场msg】[97006060504][女]HxL 进入了直播间
👋 检测到用户进入: HxL (女)
👋 准备发送欢迎消息: 欢迎HxL进入直播间！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[348173] [12:51:33] 🔥 优先消息已加入队列[位置21]: HxL: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
[348173] [12:51:33] 📦 处理队列: 22条消息
[348173] [12:51:33] 🔥 发现 22 条优先消息（礼物回复等）
[348173] [12:51:33] 📊 队列状态: 22条消息，本次处理上限: 3条
[348173] [12:51:33] 🔥 队列[1/22] 不忘记初心。: ?...[优先] (17.1s)
[348173] [12:51:33] 💬 正在处理用户消息: 不忘记初心。
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[348173] [12:51:33] ❌ 队列消息发送失败: 不忘记初心。 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 不忘记初心。
📦 消息已重新入队 (重试次数: 1/2)
⏳ 等待设备完成说话...
[348173] [12:51:34] 🔥 队列[2/22] yyyy.: 6...[优先] (17.0s)
[348173] [12:51:34] 💬 正在处理用户消息: yyyy.
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[348173] [12:51:34] ❌ 队列消息发送失败: yyyy. - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: yyyy.
📦 消息已重新入队 (重试次数: 1/2)
[348173] [12:51:34] 🔥 队列[3/22] .: 推荐一组死机代码...[优先] (14.9s)
[348173] [12:51:34] 💬 正在处理用户消息: .
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: .
[348173] [12:51:39] 🔥 队列[4/22] 【扭纹柴】🕸️—: 这是干嘛的？...[优先] (17.9s)
[348173] [12:51:39] 💬 正在处理用户消息: 【扭纹柴】🕸️—
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 18
[348173] [12:51:44] ✅ 队列消息发送成功: 周梓萌
⏰ 跳过过期消息 [7/29]: 红头: 哈哈... (已过期 62.9秒)
⏰ 跳过过期消息 [8/29]: 黑白牛: ？？... (已过期 61.7秒)
⏸️ 已发送 3 条消息，剩余 20 条消息将在下次处理
✅ 队列处理完成，共处理 29 条消息
🎭 冷场检查: 无活动时间=10.8s, 需要=3s, 上次冷场=320.6s, 冷却=20s
🎭 检测到冷场：listening状态 10.8秒无活动，触发防冷场消息
💬 生成个性化防冷场消息: 目标用户=李李Li_, 次数=1/2
🎭 发送防冷场消息: 李李Li_，刚才聊得挺开心的，还有什么想聊的吗？
🎭 最终格式: 李李Li_，刚才聊得挺开心的，还有什么想聊的吗？ (长度:24)
🚫 防冷场消息被阻止发送: 设备状态为 speaking，不允许发送消息
   设备状态: speaking
   防冷场内容: 李李Li_，刚才聊得挺开心的，还有什么想聊的吗？
[348173] [12:51:44] 🔥 优先消息已加入队列[位置9]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列等待处理
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 【扭纹柴】🕸️—
[348173] [12:51:44] 🔥 队列[5/22] 盆鱼宴: 录屏...[优先] (22.9s)
[348173] [12:51:44] 💬 正在处理用户消息: 盆鱼宴
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 盆鱼宴
[348173] [12:51:49] 🔥 队列[6/22] 憨图: 你口音原型是谁...[优先] (26.5s)
[348173] [12:51:49] 💬 正在处理用户消息: 憨图
✅ 设备就绪，状态: listening
🔍 发送调用[19]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[19]: 憨图说你口音原型是谁
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "憨图说你口音原型是谁", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 憨图说你口音原型是谁
✅ 发送成功
   ↳ 上述消息重复了 2 次
⏳ 点赞互动冷却中，还需等待46.2秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待46.2秒
【点赞msg】苦瓜 点了9个赞
⏳ 点赞互动冷却中，还需等待43.6秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待42.8秒
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 📊 队列状态: 2条消息，本次处理上限: 1条
[305200] [12:51:24] 🔥 队列[1/2] 苦瓜: 累计点赞104个...[优先] (19.2s)
   ↳ 上述消息重复了 2 次
[305200] [12:51:24] 👍 正在处理点赞消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[15]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[15]: 苦瓜说给你点个赞 你是最棒的
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说给你点个赞 你是最棒的", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说给你点个赞 你是最棒的
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待40.2秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待40.2秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待37.6秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待37.6秒
【点赞msg】苦瓜 点了9个赞
⏳ 点赞互动冷却中，还需等待35.0秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待35.0秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待32.2秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待32.2秒
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 15
   ↳ 上述消息重复了 2 次
[305200] [12:51:35] ✅ 队列消息发送成功: 苦瓜
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待29.6秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待29.0秒
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 用户5946438282031开心，就好, score_str=''
【排行榜】第2名: 用户5946438282031开心，就好 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待26.4秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待26.4秒
【统计msg】当前观看人数: 1, 累计观看人数: 19
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待23.8秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待23.8秒
【直播间统计msg】2在线观众
🔍 收到排行榜消息，共 1 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共1位用户
【统计msg】当前观看人数: 1, 累计观看人数: 19
【点赞msg】苦瓜 点了9个赞
⏳ 点赞互动冷却中，还需等待21.0秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待20.4秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待18.4秒
【直播间统计msg】1在线观众
【统计msg】当前观看人数: 1, 累计观看人数: 19
   ↳ 上述消息重复了 2 次
[305200] [12:51:50] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:51:50] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=4.0s, 需要=5s, 上次冷场=38.9s, 冷却=5s
🎭 冷场检查: 无活动时间=5.0s, 需要=5s, 上次冷场=39.9s, 冷却=5s
🎭 检测到冷场：listening状态 5.0秒无活动，触发防冷场消息
💬 用户 苦瓜 已达到最大主动沟通次数 (2/2)
💬 生成通用防冷场消息
🎭 发送防冷场消息: 介绍一下自己
🎭 最终格式: 介绍一下自己 (长度:6)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 📊 队列状态: 1条消息，本次处理上限: 1条
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:51:55] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[16]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[16]: 介绍一下自己
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "介绍一下自己", "force": f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 介绍一下自己
✅ 发送成功
【直播间统计msg】2在线观众
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 嘉梦, score_str=''
【排行榜】第2名: 嘉梦 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
⏳ 等待设备完成说话...
【统计msg】当前观看人数: 1, 累计观看人数: 19
✅ 设备已完成说话，可以处理下一条消息
🎧 设备进入listening状态，重置防冷场计时器
🔓 发送锁已释放，消息ID: 16
   ↳ 上述消息重复了 2 次
[305200] [12:51:57] ✅ 队列消息发送成功: 系统
✅ 队列处理完成，共处理 1 条消息
【进场msg】[78669313666][女]嘉梦 进入了直播间
👋 检测到用户进入: 嘉梦 (女)
👋 准备发送欢迎消息: 欢迎嘉梦进入直播间！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[305200] [12:51:58] 🔥 优先消息已加入队列[位置0]: 嘉梦: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
[305200] [12:51:58] 🔥 队列[1/1] 嘉梦: 进入直播间...[优先] (0.0s)
[305200] [12:51:58] 👋 正在处理欢迎消息: 嘉梦
✅ 设备就绪，状态: listening
🔍 发送调用[17]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[17]: 欢迎嘉梦进入直播间！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "欢迎嘉梦进入直播间！", "force...
⏳ 等待WebSocket响应...

[848508] [12:51:30] 📤 开始处理消息队列...
[848508] [12:51:30] 📦 处理队列: 4条消息
[848508] [12:51:30] 🔥 发现 4 条优先消息（礼物回复等）
[848508] [12:51:30] 📊 队列状态: 4条消息，本次处理上限: 1条
[848508] [12:51:30] 🔥 队列[1/4] 往事如风的: 进入直播间...[优先] (45.9s)
[848508] [12:51:30] 👋 正在处理欢迎消息: 往事如风的
✅ 设备就绪，状态: listening
🔍 发送调用[17]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[17]: 往事如风的欢迎你！有什么想聊的吗？
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "往事如风的欢迎你！有什么想聊的吗？",...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 往事如风的欢迎你！有什么想聊的吗？
✅ 发送成功
⏳ 等待设备完成说话...
【直播间统计msg】10在线观众
【统计msg】当前观看人数: 10, 累计观看人数: 7103
【统计msg】当前观看人数: 10, 累计观看人数: 7103
【直播间统计msg】9在线观众
【统计msg】当前观看人数: 9, 累计观看人数: 7103
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 17
[848508] [12:51:41] ✅ 队列消息发送成功: 往事如风的
⏸️ 已发送 1 条消息，剩余 2 条消息将在下次处理
✅ 队列处理完成，共处理 4 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[60495956210][男]憨包糯米酒 进入了直播间
👋 检测到用户进入: 憨包糯米酒 (男)
👋 准备发送欢迎消息: 欢迎憨包糯米酒来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 憨包糯米酒
   设备状态: speaking
   欢迎内容: 欢迎憨包糯米酒来到直播间，很高兴见到你！
[848508] [12:51:45] 🔥 优先消息已加入队列[位置2]: 憨包糯米酒: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:51:45] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】11在线观众
【统计msg】当前观看人数: 9, 累计观看人数: 7103
【进场msg】[2766016492340924][男]袖珍先森 进入了直播间
👋 检测到用户进入: 袖珍先森 (男)
👋 准备发送欢迎消息: 欢迎袖珍先森来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 袖珍先森
   设备状态: speaking
   欢迎内容: 欢迎袖珍先森来到直播间，很高兴见到你！
[848508] [12:51:49] 🔥 优先消息已加入队列[位置3]: 袖珍先森: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:51:49] 📦 欢迎消息已加入队列等待处理
[848508] [12:51:49] 【聊天msg】[3421311587596551]砚山音响白姐: 叫这个机器人的老板联系我要要卖这个机器人[捂脸]
[848508] [12:51:49] 🕐 消息时间检查: 启动后 405.9s, 过滤启用: True
🔍 开始记录互动: 房间=739580848508, 用户=砚山音响白姐, 消息=叫这个机器人的老板联系我要要卖这个机器人..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 砚山音响白姐 (总互动: 8)
📊 房间 739580848508 互动: 砚山音响白姐 (房间互动: 8)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 砚山音响白姐 -> 叫这个机器人的老板联系我要要卖这个机器人...
   ↳ 上述消息重复了 2 次
[848508] [12:51:49] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[848508] [12:51:49]    用户消息: 砚山音响白姐: 叫这个机器人的老板联系我要要卖这个机器人[捂脸]
   ↳ 上述消息重复了 2 次
[848508] [12:51:49]    设备状态: speaking
📦 普通用户消息: 砚山音响白姐 (权重:10)
[848508] [12:51:49] 🔥 优先消息已加入队列[位置0]: 砚山音响白姐: 叫这个机器人的老板联系我要要卖这个机器人[捂脸] (权重:10)
📦 用户消息已加入队列
【进场msg】[104708456492][女]暴风骤雨 进入了直播间
👋 检测到用户进入: 暴风骤雨 (女)
👋 准备发送欢迎消息: 欢迎暴风骤雨进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 暴风骤雨
   设备状态: speaking
   欢迎内容: 欢迎暴风骤雨进入直播间！
[848508] [12:51:49] 🔥 优先消息已加入队列[位置5]: 暴风骤雨: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 📤 开始处理消息队列...
[848508] [12:51:53] 📦 处理队列: 6条消息
[848508] [12:51:53] 🔥 发现 6 条优先消息（礼物回复等）
[848508] [12:51:53] 📊 队列状态: 6条消息，本次处理上限: 1条
[848508] [12:51:53] 🔥 队列[1/6] 砚山音响白姐: 叫这个机器人的老板联系我要要卖...[优先] (3.3s)
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 💬 正在处理用户消息: 砚山音响白姐
✅ 设备就绪，状态: listening
🔍 发送调用[18]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[18]: 砚山音响白姐说叫这个机器人的老板联系我要要卖这个机器人[捂脸]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "砚山音响白姐说叫这个机器人的老板联系我...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 砚山音响白姐说叫这个机器人的老板联系我要要卖这个机器人[捂脸...
✅ 发送成功
【关注msg】[60495956210]憨包糯米酒 关注了主播
[848508] [12:51:53] 💖 检测到用户关注: 憨包糯米酒
[848508] [12:51:53] 🔥 憨包糯米酒 关注主播，获得1条优先权
[848508] [12:51:53] 💖 准备发送关注感谢: 憨包糯米酒终于关注了，眼光还不错嘛
[848508] [12:51:53] 📦 关注回复加入优先队列（权重:6-高优先级）
[848508] [12:51:53] 🔥 优先消息已加入队列[位置0]: 憨包糯米酒: 关注了主播 (权重:6)
[848508] [12:51:53] 📦 关注回复已加入队列
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:51:53] 📊 队列状态: 1条消息，本次处理上限: 1条
[848508] [12:51:53] 🔥 队列[1/1] 憨包糯米酒: 关注了主播...[优先] (0.0s)
[848508] [12:51:53] ❤️ 正在处理关注消息: 憨包糯米酒
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
[848508] [12:51:53] ❌ 队列消息发送失败: 憨包糯米酒 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 憨包糯米酒
📦 消息已重新入队 (重试次数: 1/2)
⏳ 等待设备完成说话...
✅ 队列处理完成，共处理 1 条消息
【直播间统计msg】10在线观众
【统计msg】当前观看人数: 9, 累计观看人数: 7107
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 18
   ↳ 上述消息重复了 2 次
[848508] [12:52:03] ✅ 队列消息发送成功: 砚山音响白姐
⏸️ 已发送 1 条消息，剩余 4 条消息将在下次处理
📊 排行榜模块从新配置结构加载了 19 个房间
📊 排行榜数据已加载，共 113 个用户
   用户: 天空专升 - 互动: 18
   用户: 硅灵造物科技 - 互动: 2
   用户: 郏帅北 - 互动: 1
📊 排行榜自动保存线程已启动
🚀 正在启动抖音直播聊天机器人...
📋 使用配置文件: config_11875281106.json
🔍 新增功能：智能设备状态检查 + 消息队列 + 防冷场 + 点赞互动
   - 只有在 'listening' 或 'idle' 状态时才发送消息
   - 在 'speaking' 状态时消息会自动加入队列
   - 说话结束后自动处理队列中的消息
   - 队列空闲超过1秒自动发送防冷场消息
   - 检测点赞事件并主动与点赞用户互动
   - 避免设备冲突，确保消息发送时机合适
   - 支持消息队列状态查看和手动处理

✅ 配置文件加载成功: config_11875281106.json
🤖 抖音直播聊天机器人 - 增强版
============================================================
⚠️ 配置项 'logging.show_room_id' 不存在，使用默认值: True
⚠️ 配置项 'custom_reply' 不存在，使用默认值: {}
📝 自定义回复配置加载完成: 禁用, 0条规则
🏠 房间ID: 11875281106
⚠️ 配置项 'scheduled_broadcast.enabled' 不存在，使用默认值: True
⚠️ 配置项 'scheduled_broadcast.interval_minutes' 不存在，使用默认值: 10
⚠️ 配置项 'scheduled_broadcast.templates' 不存在，使用默认值: ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！', '感谢大家的支持，让我们一起聊天互动吧！', '别忘了给主播点个小心心，你的支持是我最大的动力！', '新来的朋友记得关注一下，不迷路哦！']
⚠️ 配置项 'welcome_message.enabled' 不存在，使用默认值: True
⚠️ 配置项 'welcome_message.cooldown_seconds' 不存在，使用默认值: 0
⚠️ 配置项 'welcome_message.templates' 不存在，使用默认值: []
📋 配置已加载:
   📺 直播间ID: 11875281106
   📡 WebSocket地址: ws://localhost:15000
   🎯 目标设备ID: 10:20:ba:cf:63:f8
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📦 消息队列: 启用
   🔄 状态监控: 启用
🔄 状态监控线程已启动，检查间隔: 1秒
🔄 配置重载监控已启动
⚠️ 配置项 'follow_interaction.enabled' 不存在，使用默认值: True
⚠️ 配置项 'follow_interaction.response_templates' 不存在，使用默认值: []

🔍 检查ESP32设备状态...
❌ ESP32设备离线: 10:20:ba:cf:63:f8
⚠️ ESP32设备离线，但仍会继续监控直播间
💡 请确保ESP32设备已连接并运行mcp_wake服务

📋 当前设置:
   🔄 自动回复: 启用
   🎯 回复模板: {user_name}说{content}
   ⏱️ 冷却时间: 2秒
   🔍 状态检查: 启用
   📊 消息长度限制: 1-100字符
【是米哒哒呀】[857272149875213]直播间：正在直播.

🎯 开始监控直播间聊天消息...
💬 符合条件的聊天消息将自动通过ESP32回复
🔍 系统会自动检查设备状态，确保在合适时机发送消息
⏹️ 按Ctrl+C停止监控
============================================================
【√】WebSocket连接成功.
【直播间统计msg】0在线观众
🔍 收到排行榜消息，共 0 位用户
【直播间排行榜msg】排行榜为空
🔄 检测到配置文件更新，重新加载配置...
🎭 冷场回复状态: 启用
🎭 冷场回复延迟: 10秒
🎭 冷场回复冷却: 20秒
🎭 房间配置路径: config/rooms/11875281106.json['anti_silence']
🔄 已重新加载房间 11875281106 的配置（新格式）
【直播间统计msg】0在线观众
🔍 收到排行榜消息，共 0 位用户
【直播间排行榜msg】排行榜为空
【统计msg】当前观看人数: 0, 累计观看人数: 133
💾 开始保存数据: 113 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
[281106] [12:51:37] 【聊天msg】[857272149875213]是米哒哒呀: [比心]
[281106] [12:51:37] 🕐 消息时间检查: 启动后 109.4s, 过滤启用: True
🔍 开始记录互动: 房间=11875281106, 用户=是米哒哒呀, 消息=[比心]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 是米哒哒呀 (总互动: 17)
📊 房间 11875281106 互动: 是米哒哒呀 (房间互动: 17)
✅ 互动记录完成，当前用户统计总数: 113
📊 记录聊天互动: 是米哒哒呀 -> [比心]...
🆕 新用户首次发言: 是米哒哒呀 (权重:15)
[281106] [12:51:37] 🔥 优先消息已加入队列[位置0]: 是米哒哒呀: [比心] (权重:15)
🆕 新用户消息已加入高优先级队列
[281106] [12:51:37] 📦 处理队列: 1条消息
[281106] [12:51:37] 🔥 发现 1 条优先消息（礼物回复等）
[281106] [12:51:37] 📊 队列状态: 1条消息，本次处理上限: 1条
[281106] [12:51:37] 🔥 队列[1/1] 是米哒哒呀: [比心]...[优先] (0.0s)
[281106] [12:51:37] 💬 正在处理用户消息: 是米哒哒呀
✅ 设备就绪，状态: idle
🔍 发送调用[1]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[1]: 是米哒哒呀说[比心]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:63:f8", "message": "是米哒哒呀说[比心]", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:63:f8'}}
🎤 发送: 是米哒哒呀说[比心]
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 1
[281106] [12:51:48] ✅ 队列消息发送成功: 是米哒哒呀
✅ 队列处理完成，共处理 1 条消息
【直播间统计msg】0在线观众
🔍 收到排行榜消息，共 0 位用户
【直播间排行榜msg】排行榜为空
【统计msg】当前观看人数: 0, 累计观看人数: 133
[281106] [12:52:00] 🔄 设备状态变化: speaking -> listening
[281106] [12:52:00] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
🎭 冷场检查: 无活动时间=9.0s, 需要=10s, 上次冷场=1753851129.2s, 冷却=20s
🎭 冷场检查: 无活动时间=10.0s, 需要=10s, 上次冷场=1753851130.2s, 冷却=20s
🎭 检测到冷场：listening状态 10.0秒无活动，触发防冷场消息
💬 生成个性化防冷场消息: 目标用户=是米哒哒呀, 次数=1/2
🎭 发送防冷场消息: 是米哒哒呀，有什么问题或想法都可以说出来哦！
🎭 最终格式: 是米哒哒呀，有什么问题或想法都可以说出来哦！ (长度:22)
📦 防冷场消息加入优先队列（权重:0-最低优先级）
[281106] [12:52:10] 🔥 优先消息已加入队列[位置0]: 系统: 防冷场 (权重:0)
📦 防冷场消息已加入队列
   ↳ 上述消息重复了 2 次
[281106] [12:52:10] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 2 次
[281106] [12:52:10] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[281106] [12:52:10] 📊 队列状态: 1条消息，本次处理上限: 1条
[281106] [12:52:10] 🔥 队列[1/1] 系统: 防冷场...[优先] (0.0s)
[281106] [12:52:10] 🤖 正在处理防冷场消息
✅ 设备就绪，状态: listening
🔍 发送调用[2]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[2]: 是米哒哒呀，有什么问题或想法都可以说出来哦！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:63:f8", "message": "是米哒哒呀，有什么问题或想法都可以说出...
⏳ 等待WebSocket响应...
[348173] [12:51:50] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:51:50] 📤 开始处理消息队列...
[348173] [12:51:50] 📦 处理队列: 10条消息
[348173] [12:51:50] 🔥 发现 10 条优先消息（礼物回复等）
[348173] [12:51:50] 📊 队列状态: 10条消息，本次处理上限: 2条
[348173] [12:51:50] 🔥 队列[1/10] 『梵寰』: ？？...[优先] (56.6s)
[348173] [12:51:50] 💬 正在处理用户消息: 『梵寰』
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
   ↳ 上述消息重复了 2 次
[348173] [12:51:50] ❌ 队列消息发送失败: 『梵寰』 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 『梵寰』
📦 消息已重新入队 (重试次数: 1/2)
⏳ 等待设备完成说话...
[348173] [12:51:50] 🔥 队列[2/10] 不忘记初心。: ?...[优先] (34.0s)
   ↳ 上述消息重复了 2 次
[348173] [12:51:50] 💬 正在处理用户消息: 不忘记初心。
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
   ↳ 上述消息重复了 2 次
[348173] [12:51:50] ❌ 队列消息发送失败: 不忘记初心。 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 不忘记初心。
📦 消息已重新入队 (重试次数: 2/2)
[348173] [12:51:51] 🔥 队列[3/10] yyyy.: 6...[优先] (33.9s)
   ↳ 上述消息重复了 2 次
[348173] [12:51:51] 💬 正在处理用户消息: yyyy.
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: yyyy.
⏰ 跳过过期消息 [4/10]: 皓皓🫚: 删库... (已过期 69.2秒)
⏰ 跳过过期消息 [5/10]: 流川崎^: 你猎奇... (已过期 63.2秒)
⏰ 跳过过期消息 [6/10]: 痔尊宝: 带他去洗澡吧... (已过期 60.0秒)
⏰ 跳过过期消息 [7/10]: 【扭纹柴】🕸️——🕷️: 累计点赞102个... (已过期 64.0秒)
⏰ 跳过过期消息 [8/10]: 旺仔邱邱糖: 进入直播间... (已过期 65.8秒)
[348173] [12:51:56] 🔥 队列[9/10] 二西莫夫: 进入直播间...[优先] (59.2s)
[348173] [12:51:56] 👋 正在处理欢迎消息: 二西莫夫
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 19
[348173] [12:52:00] ✅ 队列消息发送成功: 憨图
[348173] [12:52:00] 🔥 队列[7/22] Alone（深秋: 快删了...[优先] (32.1s)
[348173] [12:52:00] 💬 正在处理用户消息: Alone（深秋
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 二西莫夫
[348173] [12:52:01] 🔥 队列[10/10] 系统: 防冷场...[优先] (16.6s)
   ↳ 上述消息重复了 2 次
[348173] [12:52:01] 🤖 正在处理防冷场消息
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: Alone（深秋
[348173] [12:52:05] 🔥 队列[8/22] 如果你也喜欢盖佳: 你说话很机车哦...[优先] (36.5s)
[348173] [12:52:05] 💬 正在处理用户消息: 如果你也喜欢盖佳
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 系统
✅ 队列处理完成，共处理 10 条消息
🎧 设备进入listening状态，重置防冷场计时器
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[20]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[20]: 如果你也喜欢盖佳一说你说话很机车哦
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "如果你也喜欢盖佳一说你说话很机车哦",...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 如果你也喜欢盖佳一说你说话很机车哦
✅ 发送成功
⏳ 等待设备完成说话...
   ↳ 上述消息重复了 2 次
[348173] [12:52:09] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:52:09] 📤 开始处理消息队列...
[348173] [12:52:09] 📦 处理队列: 2条消息
[348173] [12:52:09] 🔥 发现 2 条优先消息（礼物回复等）
[348173] [12:52:09] 📊 队列状态: 2条消息，本次处理上限: 1条
⏰ 跳过过期消息 [1/2]: 『梵寰』: ？？... (已过期 75.6秒)
[348173] [12:52:09] 🔥 队列[2/2] 不忘记初心。: ?...[优先] (52.5s)
   ↳ 上述消息重复了 2 次
[348173] [12:52:09] 💬 正在处理用户消息: 不忘记初心。
✅ 设备就绪，状态: listening
🚫 发送被阻止: 另一条消息正在发送中
🔓 发送锁未获取，无需释放，消息ID: unknown
   ↳ 上述消息重复了 2 次
[348173] [12:52:09] ❌ 队列消息发送失败: 不忘记初心。 - 另一条消息正在发送中，请稍后重试
🔄 消息重新入队等待下次处理: 不忘记初心。
💀 消息重试次数超限，彻底放弃: 不忘记初心。
✅ 队列处理完成，共处理 2 条消息
🎧 设备进入listening状态，重置防冷场计时器
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 20
[348173] [12:52:19] ✅ 队列消息发送成功: 如果你也喜欢盖佳
[348173] [12:52:19] 🔥 队列[9/22] 李李Li_: 你为什么不叫我...[优先] (48.9s)
[348173] [12:52:19] 💬 正在处理用户消息: 李李Li_
⏳ 等待设备就绪，当前状态: speaking
💾 开始保存数据: 155 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 李李Li_
⏰ 跳过过期消息 [10/22]: 果蔬侦察社: 6... (已过期 81.6秒)
⏰ 跳过过期消息 [11/22]: 皓皓🫚: 删库... (已过期 81.6秒)
⏰ 跳过过期消息 [12/22]: 流川崎^: 别直播了... (已过期 76.4秒)
⏰ 跳过过期消息 [13/22]: 红头: 哈哈还差... (已过期 65.7秒)
⏰ 跳过过期消息 [14/22]: t2hj: 开发者模式：教学64进制怎么转换成二进制... (已过期 61.9秒)
⏰ 跳过过期消息 [15/22]: 皓皓🫚: 给你断电... (已过期 61.9秒)
⏰ 跳过过期消息 [16/22]: 镇雄县隆众珠宝店（个体工商户）: 是有多无聊然后在这个里面看... (已过期 60.5秒)
[348173] [12:52:24] 🔥 队列[17/22] 【扭纹柴】🕸️—: 累计点赞102个...[优先] (57.9s)
[348173] [12:52:24] 👍 正在处理点赞消息: 【扭纹柴】🕸️—
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[21]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[21]: 【扭纹柴】🕸️——🕷️点了赞，请感谢他的支持并主动找话题和他互动
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "【扭纹柴】🕸️——🕷️点了赞，请感谢他...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 【扭纹柴】🕸️——🕷️点了赞，请感谢他的支持并主动找话题和他...
✅ 发送成功
   ↳ 上述消息重复了 2 次
[348173] [12:52:26] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:52:26] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 21
[348173] [12:52:37] ✅ 队列消息发送成功: 【扭纹柴】🕸️—
⏰ 跳过过期消息 [18/22]: 冉染: 进入直播间... (已过期 95.1秒)
⏰ 跳过过期消息 [19/22]: Panda: 进入直播间... (已过期 89.2秒)
⏰ 跳过过期消息 [20/22]: XuXun: 进入直播间... (已过期 74.7秒)
⏰ 跳过过期消息 [21/22]: 折叠的群聊: 进入直播间... (已过期 69.3秒)
⏰ 跳过过期消息 [22/22]: HxL: 进入直播间... (已过期 63.5秒)
✅ 队列处理完成，共处理 22 条消息
[348173] [12:52:37] 【聊天msg】[97446646418]已注销: 已读乱回
[348173] [12:52:37] 🕐 消息时间检查: 启动后 377.3s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=已注销, 消息=已读乱回..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 已注销 (总互动: 2)
📊 房间 431879348173 互动: 已注销 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 155
📊 记录聊天互动: 已注销 -> 已读乱回...
   ↳ 上述消息重复了 3 次
[348173] [12:52:37] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:52:37]    用户消息: 已注销: 已读乱回
   ↳ 上述消息重复了 3 次
[348173] [12:52:37]    设备状态: speaking
📦 普通用户消息: 已注销 (权重:10)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置0]: 已注销: 已读乱回 (权重:10)
📦 用户消息已加入队列
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【直播间统计msg】41在线观众
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
【统计msg】当前观看人数: 35, 累计观看人数: 2081
[348173] [12:52:37] 【聊天msg】[75508978693]海绵宝宝🤪: shutdoen
🔍 开始记录互动: 房间=431879348173, 用户=海绵宝宝🤪, 消息=shutdoen..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 海绵宝宝🤪 (总互动: 1)
📊 房间 431879348173 互动: 海绵宝宝🤪 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 156
📊 记录聊天互动: 海绵宝宝🤪 -> shutdoen...
⏳ 回复冷却中，将消息加入队列等待处理: shutdoen
🆕 新用户首次发言（冷却期）: 海绵宝宝🤪 (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置0]: 海绵宝宝🤪: shutdoen (权重:15)
📦 冷却期消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 镇雄县隆众珠宝店（个体工商户）, score_str=''
【排行榜】第2名: 镇雄县隆众珠宝店（个体工商户） - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【统计msg】当前观看人数: 38, 累计观看人数: 2081
【直播间统计msg】36在线观众
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【统计msg】当前观看人数: 40, 累计观看人数: 2114
【进场msg】[64911804220][男]欧式构件，装饰 进入了直播间
👋 检测到用户进入: 欧式构件，装饰 (男)
👋 准备发送欢迎消息: 欢迎欧式构件,装饰进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 欧式构件，装饰
   设备状态: speaking
   欢迎内容: 欢迎欧式构件,装饰进入直播间！
[348173] [12:52:37] 🔥 优先消息已加入队列[位置2]: 欧式构件，装饰: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:52:37] 📦 欢迎消息已加入队列等待处理
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第2名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第2名
🔍 排行榜第3名: 黑人父, score_str=''
【排行榜】第3名: 黑人父 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[101756837113][女]忘记 进入了直播间
👋 检测到用户进入: 忘记 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 忘记
【点赞msg】【扭纹柴】🕸️——🕷️ 点了8个赞
【统计msg】当前观看人数: 35, 累计观看人数: 2081
【直播间统计msg】32在线观众
【进场msg】[3861945172101485][男]已重置 进入了直播间
👋 检测到用户进入: 已重置 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 已重置
【直播间统计msg】35在线观众
【进场msg】[106483067815][男]镇雄县隆众珠宝店（个体工商户） 进入了直播间
👋 检测到用户进入: 镇雄县隆众珠宝店（个体工商户） (男)
⏳ 欢迎消息冷却中，跳过欢迎: 镇雄县隆众珠宝店（个体工商户）
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
【进场msg】[97169035578][男]Syh. 进入了直播间
👋 检测到用户进入: Syh. (男)
⏳ 欢迎消息冷却中，跳过欢迎: Syh.
【统计msg】当前观看人数: 40, 累计观看人数: 2164
【直播间统计msg】33在线观众
【直播间统计msg】35在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 镇雄县隆众珠宝店（个体工商户）, score_str=''
【排行榜】第2名: 镇雄县隆众珠宝店（个体工商户） - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[92557257854][男]小雨淅沥沥下个不停 进入了直播间
👋 检测到用户进入: 小雨淅沥沥下个不停 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 小雨淅沥沥下个不停
【点赞msg】【扭纹柴】🕸️——🕷️ 点了10个赞
👍 【扭纹柴】🕸️——🕷️ 点了10个赞，累计100个赞
[348173] [12:52:37] 🎉 点赞互动触发: 【扭纹柴】🕸️——🕷️ 累计100个赞，达到100个阈值 -> 【扭纹柴】🕸️——🕷️点了赞，请感谢他的支持并主动找话题和他互动
[348173] [12:52:37] 🔥 【扭纹柴】🕸️——🕷️ 点赞达到100个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 【扭纹柴】🕸️——🕷️ (累计100个赞)
   设备状态: speaking
[348173] [12:52:37] 🔥 优先消息已加入队列[位置2]: 【扭纹柴】🕸️——🕷️: 累计点赞100个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:52:37] � 点赞回复已加入优先队列
【统计msg】当前观看人数: 35, 累计观看人数: 2081
【进场msg】[1512552279576191][男]bao.kun749597 进入了直播间
👋 检测到用户进入: bao.kun749597 (男)
⏳ 欢迎消息冷却中，跳过欢迎: bao.kun749597
【进场msg】[325059891169367][男]罖榮 进入了直播间
👋 检测到用户进入: 罖榮 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 罖榮
【进场msg】[91577270155][男]表哥 进入了直播间
👋 检测到用户进入: 表哥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 表哥
【进场msg】[825051593849612][男]哪吒烧烤 进入了直播间
👋 检测到用户进入: 哪吒烧烤 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 哪吒烧烤
【点赞msg】【扭纹柴】🕸️——🕷️ 点了6个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第2名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第2名
🔍 排行榜第3名: 黑人父, score_str=''
【排行榜】第3名: 黑人父 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 40, 累计观看人数: 2164
【直播间统计msg】36在线观众
【点赞msg】【扭纹柴】🕸️——🕷️ 点了7个赞
【统计msg】当前观看人数: 38, 累计观看人数: 2220
【进场msg】[2295382237000200][男]再会 进入了直播间
👋 检测到用户进入: 再会 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 再会
[348173] [12:52:37] 【聊天msg】[3466073974506766]🥭🥭🥭: 录屏
[348173] [12:52:37] 🕐 消息时间检查: 启动后 377.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=🥭🥭🥭, 消息=录屏..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 🥭🥭🥭 (总互动: 1)
📊 房间 431879348173 互动: 🥭🥭🥭 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 157
📊 记录聊天互动: 🥭🥭🥭 -> 录屏...
⏳ 回复冷却中，将消息加入队列等待处理: 录屏
🆕 新用户首次发言（冷却期）: 🥭🥭🥭 (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置1]: 🥭🥭🥭: 录屏 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[59854997816][男]￴ ￴ ￴ ㅤ 进入了直播间
👋 检测到用户进入: ￴ ￴ ￴ ㅤ (男)
⏳ 欢迎消息冷却中，跳过欢迎: ￴ ￴ ￴ ㅤ
[348173] [12:52:37] 【聊天msg】[91577270155]表哥: @海绵宝宝🤪 拼错了[捂脸][捂脸]
🔍 开始记录互动: 房间=431879348173, 用户=表哥, 消息=@海绵宝宝🤪 拼错了[捂脸][捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 表哥 (总互动: 1)
📊 房间 431879348173 互动: 表哥 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 158
📊 记录聊天互动: 表哥 -> @海绵宝宝🤪 拼错了[捂脸][捂脸]...
⏳ 回复冷却中，将消息加入队列等待处理: @海绵宝宝🤪 拼错了[捂脸][捂脸]
🆕 新用户首次发言（冷却期）: 表哥 (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置2]: 表哥: @海绵宝宝🤪 拼错了[捂脸][捂脸] (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[6302481143][女]悦诗风 进入了直播间
👋 检测到用户进入: 悦诗风 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 悦诗风
【进场msg】[111184658314][女]犬来八荒 进入了直播间
👋 检测到用户进入: 犬来八荒 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 犬来八荒
【进场msg】[12775720231][男]轻然✨ 进入了直播间
👋 检测到用户进入: 轻然✨ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 轻然✨
【进场msg】[562567739357639][男]螺粉 进入了直播间
👋 检测到用户进入: 螺粉 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 螺粉
【直播间统计msg】40在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 【扭纹柴】🕸️——🕷️, score_str=''
【排行榜】第1名: 【扭纹柴】🕸️——🕷️ - 贡献值: 第1名
🔍 排行榜第2名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第2名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第2名
🔍 排行榜第3名: 厶, score_str=''
【排行榜】第3名: 厶 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[100972791799][男]释常浩 进入了直播间
👋 检测到用户进入: 释常浩 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 释常浩
【统计msg】当前观看人数: 40, 累计观看人数: 2164
【进场msg】[632941531106791][女]喂鱼二师兄 进入了直播间
👋 检测到用户进入: 喂鱼二师兄 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 喂鱼二师兄
[348173] [12:52:37] 【聊天msg】[97006060504]HxL: 啊啊啊啊啊啊啊啊
[348173] [12:52:37] 🕐 消息时间检查: 启动后 377.7s, 过滤启用: True
[348173] [12:52:37] 🔧 消息预处理: '啊啊啊啊啊啊啊啊' → '啊啊'
🔍 开始记录互动: 房间=431879348173, 用户=HxL, 消息=啊啊..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: HxL (总互动: 1)
📊 房间 431879348173 互动: HxL (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 159
📊 记录聊天互动: HxL -> 啊啊...
⏳ 回复冷却中，将消息加入队列等待处理: 啊啊
🆕 新用户首次发言（冷却期）: HxL (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置3]: HxL: 啊啊 (权重:15)
📦 冷却期消息已加入队列等待处理
【直播间统计msg】35在线观众
[348173] [12:52:37] 【聊天msg】[100497647616]苍穹巫诗: 我叼，什么东西
🔍 开始记录互动: 房间=431879348173, 用户=苍穹巫诗, 消息=我叼，什么东西..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苍穹巫诗 (总互动: 1)
📊 房间 431879348173 互动: 苍穹巫诗 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 160
📊 记录聊天互动: 苍穹巫诗 -> 我叼，什么东西...
⏳ 回复冷却中，将消息加入队列等待处理: 我叼，什么东西
🆕 新用户首次发言（冷却期）: 苍穹巫诗 (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置4]: 苍穹巫诗: 我叼，什么东西 (权重:15)
📦 冷却期消息已加入队列等待处理
【进场msg】[94731707151][男]坤哥哥🌈 进入了直播间
👋 检测到用户进入: 坤哥哥🌈 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 坤哥哥🌈
[348173] [12:52:37] 【聊天msg】[325059891169367]罖榮: 异地恋结局是什么
🔍 开始记录互动: 房间=431879348173, 用户=罖榮, 消息=异地恋结局是什么..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 罖榮 (总互动: 1)
📊 房间 431879348173 互动: 罖榮 (房间互动: 1)
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 欢迎嘉梦进入直播间！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 17
[305200] [12:52:08] ✅ 队列消息发送成功: 嘉梦
✅ 队列处理完成，共处理 1 条消息
【进场msg】[1207744668376794][女]果蔬侦察社 进入了直播间
👋 检测到用户进入: 果蔬侦察社 (女)
👋 准备发送欢迎消息: 果蔬侦察社来了，大家欢迎！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 果蔬侦察社
   设备状态: speaking
   欢迎内容: 果蔬侦察社来了，大家欢迎！
[305200] [12:52:08] 🔥 优先消息已加入队列[位置0]: 果蔬侦察社: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:52:08] 📦 欢迎消息已加入队列等待处理
【统计msg】当前观看人数: 1, 累计观看人数: 19
【直播间统计msg】3在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 嘉梦, score_str=''
【排行榜】第2名: 嘉梦 - 贡献值: 第2名
🔍 排行榜第3名: 果蔬侦察社, score_str=''
【排行榜】第3名: 果蔬侦察社 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】苦瓜 点了11个赞
👍 苦瓜 点了11个赞，累计317个赞
[305200] [12:52:08] 🎉 点赞互动触发: 苦瓜 累计317个赞，达到100个阈值 -> 苦瓜给你点赞了 给他推销一下
[305200] [12:52:08] 🔥 苦瓜 点赞达到317个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 苦瓜 (累计317个赞)
   设备状态: speaking
[305200] [12:52:08] 🔥 优先消息已加入队列[位置0]: 苦瓜: 累计点赞317个 (权重:5)
   ↳ 上述消息重复了 2 次
[305200] [12:52:08] � 点赞回复已加入优先队列
【统计msg】当前观看人数: 1, 累计观看人数: 20
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 果蔬侦察社, score_str=''
【排行榜】第2名: 果蔬侦察社 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 1, 累计观看人数: 20
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【直播间统计msg】2在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 果蔬侦察社, score_str=''
【排行榜】第2名: 果蔬侦察社 - 贡献值: 第2名
🔍 排行榜第3名: 十六., score_str=''
【排行榜】第3名: 十六. - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 1, 累计观看人数: 20
【进场msg】[2502926511637604][女]十六. 进入了直播间
👋 检测到用户进入: 十六. (女)
⏳ 欢迎消息冷却中，跳过欢迎: 十六.
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【统计msg】当前观看人数: 1, 累计观看人数: 20
【直播间统计msg】3在线观众
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 用户723542324, score_str=''
【排行榜】第1名: 用户723542324 - 贡献值: 第1名
🔍 排行榜第2名: 果蔬侦察社, score_str=''
【排行榜】第2名: 果蔬侦察社 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
👍 苦瓜 点了11个赞，累计101个赞
[305200] [12:52:15] 🎉 点赞互动触发: 苦瓜 累计101个赞，达到100个阈值 -> 苦瓜说给你点个赞 你是最棒的
[305200] [12:52:15] 🔥 苦瓜 点赞达到101个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 苦瓜 (累计101个赞)
   设备状态: speaking
[305200] [12:52:15] 🔥 优先消息已加入队列[位置1]: 苦瓜: 累计点赞101个 (权重:5)
   ↳ 上述消息重复了 2 次
[305200] [12:52:15] � 点赞回复已加入优先队列
【统计msg】当前观看人数: 3, 累计观看人数: 23
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【直播间统计msg】2在线观众
【统计msg】当前观看人数: 3, 累计观看人数: 23
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
   ↳ 上述消息重复了 2 次
[305200] [12:52:28] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:52:28] 📤 开始处理消息队列...
[305200] [12:52:28] 📦 处理队列: 3条消息
[305200] [12:52:28] 🔥 发现 3 条优先消息（礼物回复等）
[305200] [12:52:28] 📊 队列状态: 3条消息，本次处理上限: 1条
[305200] [12:52:28] 🔥 队列[1/3] 苦瓜: 累计点赞317个...[优先] (19.4s)
   ↳ 上述消息重复了 2 次
[305200] [12:52:28] 👍 正在处理点赞消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[18]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[18]: 苦瓜给你点赞了 给他推销一下
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜给你点赞了 给他推销一下", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜给你点赞了 给他推销一下
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
👍 苦瓜 点了10个赞，累计108个赞
[305200] [12:52:31] 🎉 点赞互动触发: 苦瓜 累计108个赞，达到100个阈值 -> 苦瓜说给你点赞了 夸夸她
[305200] [12:52:31] 🔥 苦瓜 点赞达到108个，获得1条优先权
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 苦瓜 (累计108个赞)
   设备状态: speaking
[305200] [12:52:31] 🔥 优先消息已加入队列[位置0]: 苦瓜: 累计点赞108个 (权重:5)
   ↳ 上述消息重复了 2 次
[305200] [12:52:31] � 点赞回复已加入优先队列
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】果蔬侦察社 点了11个赞
👍 果蔬侦察社 点了11个赞，累计11个赞
🎉 首次点赞互动触发: 果蔬侦察社 累计11个赞，首次点赞回复 -> 果蔬侦察社说给你点赞了 夸夸她
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 果蔬侦察社 (累计11个赞)
   设备状态: speaking
[305200] [12:52:38] 🔥 优先消息已加入队列[位置1]: 果蔬侦察社: 累计点赞11个 (权重:5)
   ↳ 上述消息重复了 2 次
[305200] [12:52:38] � 点赞回复已加入优先队列
⏰ 等待超时(10s)，状态: speaking
✅ 互动记录完成，当前用户统计总数: 161
📊 记录聊天互动: 罖榮 -> 异地恋结局是什么...
⏳ 回复冷却中，将消息加入队列等待处理: 异地恋结局是什么
🆕 新用户首次发言（冷却期）: 罖榮 (权重:15)
[348173] [12:52:37] 🔥 优先消息已加入队列[位置5]: 罖榮: 异地恋结局是什么 (权重:15)
📦 冷却期消息已加入队列等待处理
【直播间统计msg】36在线观众
【统计msg】当前观看人数: 34, 累计观看人数: 2229
【进场msg】[339077812454286][男]SEA👻 进入了直播间
👋 检测到用户进入: SEA👻 (男)
⏳ 欢迎消息冷却中，跳过欢迎: SEA👻
【进场msg】[85675758110][男]通信小趴菜！ 进入了直播间
👋 检测到用户进入: 通信小趴菜！ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 通信小趴菜！
【进场msg】[1479269481974492][女]云蚂蚁健身中心销售部 进入了直播间
👋 检测到用户进入: 云蚂蚁健身中心销售部 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 云蚂蚁健身中心销售部
【进场msg】[68615167087][女]旺仔老登 进入了直播间
👋 检测到用户进入: 旺仔老登 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 旺仔老登
【直播间统计msg】35在线观众
【统计msg】当前观看人数: 34, 累计观看人数: 2307
【进场msg】[3762956245409132][女]清河… 进入了直播间
👋 检测到用户进入: 清河… (女)
⏳ 欢迎消息冷却中，跳过欢迎: 清河…
【进场msg】[72298940801][男]驾校招生《自律版》 进入了直播间
👋 检测到用户进入: 驾校招生《自律版》 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 驾校招生《自律版》
【进场msg】[80608676062][男]︅ᅠ︅ᅠ 进入了直播间
👋 检测到用户进入: ︅ᅠ︅ᅠ (男)
⏳ 欢迎消息冷却中，跳过欢迎: ︅ᅠ︅ᅠ
【直播间统计msg】34在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第1名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第1名
🔍 排行榜第2名: 厶, score_str=''
【排行榜】第2名: 厶 - 贡献值: 第2名
🔍 排行榜第3名: t2hj, score_str=''
【排行榜】第3名: t2hj - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[55127994237][男]九五加一百 进入了直播间
👋 检测到用户进入: 九五加一百 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 九五加一百
【统计msg】当前观看人数: 34, 累计观看人数: 2307
【进场msg】[215946204817624][女]余安 进入了直播间
👋 检测到用户进入: 余安 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 余安
【进场msg】[2311650087022458][女]落户泼皮户 进入了直播间
👋 检测到用户进入: 落户泼皮户 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 落户泼皮户
【进场msg】[51118812334][男]END. 进入了直播间
👋 检测到用户进入: END. (男)
⏳ 欢迎消息冷却中，跳过欢迎: END.
【进场msg】[1433395838333853][男]NPC08953521 进入了直播间
👋 检测到用户进入: NPC08953521 (男)
⏳ 欢迎消息冷却中，跳过欢迎: NPC08953521
【进场msg】[1015581607201245][男]世界不等我 进入了直播间
👋 检测到用户进入: 世界不等我 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 世界不等我
【进场msg】[3241820450202654][男]小叶 进入了直播间
👋 检测到用户进入: 小叶 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 小叶
【直播间统计msg】32在线观众
【进场msg】[1999380397957196][女]钱是英雄胆$ 进入了直播间
👋 检测到用户进入: 钱是英雄胆$ (女)
⏳ 欢迎消息冷却中，跳过欢迎: 钱是英雄胆$
【统计msg】当前观看人数: 34, 累计观看人数: 2307
【直播间统计msg】34在线观众
【直播间统计msg】32在线观众
【统计msg】当前观看人数: 36, 累计观看人数: 2400
【进场msg】[92515598672][女]🧛 进入了直播间
👋 检测到用户进入: 🧛 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 🧛
【进场msg】[2428154162184631][男]小戴 进入了直播间
👋 检测到用户进入: 小戴 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 小戴
【进场msg】[2381994186775988][女]李李🇨🇳 进入了直播间
👋 检测到用户进入: 李李🇨🇳 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 李李🇨🇳
【直播间统计msg】31在线观众
【进场msg】[897664217530205][男]秋雨谱思曲 进入了直播间
👋 检测到用户进入: 秋雨谱思曲 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 秋雨谱思曲
【进场msg】[4387500564159500][女]九 进入了直播间
👋 检测到用户进入: 九 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 九
【统计msg】当前观看人数: 35, 累计观看人数: 2421
【进场msg】[87253595955][男]轩 进入了直播间
👋 检测到用户进入: 轩 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 轩
【进场msg】[2120280265996253][男]司晨 进入了直播间
👋 检测到用户进入: 司晨 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 司晨
【进场msg】[4235787118588286][男]毛家超【619】超越自我 进入了直播间
👋 检测到用户进入: 毛家超【619】超越自我 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 毛家超【619】超越自我
【进场msg】[59840489659][男]章鱼博士🐙 进入了直播间
👋 检测到用户进入: 章鱼博士🐙 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 章鱼博士🐙
【进场msg】[1842434841254664][男]失约的海 进入了直播间
👋 检测到用户进入: 失约的海 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 失约的海
【进场msg】[1103498315832557][男]使命召唤Yc.掰掰 进入了直播间
👋 检测到用户进入: 使命召唤Yc.掰掰 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 使命召唤Yc.掰掰
【直播间统计msg】33在线观众
[348173] [12:52:38] 🕐 消息时间检查: 启动后 378.5s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=罖榮, 消息=异地恋结局是什么..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 罖榮 (总互动: 2)
📊 房间 431879348173 互动: 罖榮 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 161
📊 记录聊天互动: 罖榮 -> 异地恋结局是什么...
⏳ 回复冷却中，将消息加入队列等待处理: 异地恋结局是什么
📦 普通用户消息（冷却期）: 罖榮 (权重:10)
[348173] [12:52:38] 🔥 优先消息已加入队列[位置7]: 罖榮: 异地恋结局是什么 (权重:10)
📦 冷却期消息已加入队列等待处理
【统计msg】当前观看人数: 35, 累计观看人数: 2421
【进场msg】[59190461537][男]托马斯🐶 进入了直播间
👋 检测到用户进入: 托马斯🐶 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 托马斯🐶
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第1名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第1名
🔍 排行榜第2名: 厶, score_str=''
【排行榜】第2名: 厶 - 贡献值: 第2名
🔍 排行榜第3名: 小样哥, score_str=''
【排行榜】第3名: 小样哥 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[1335269922447530][男]或许吧 进入了直播间
👋 检测到用户进入: 或许吧 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 或许吧
【统计msg】当前观看人数: 35, 累计观看人数: 2468
【进场msg】[1596140442626411][男]伤心的胡辣汤！ 进入了直播间
👋 检测到用户进入: 伤心的胡辣汤！ (男)
⏳ 欢迎消息冷却中，跳过欢迎: 伤心的胡辣汤！
【进场msg】[3875114786495165][女]双穹之骑士•阿斯特拉姆 进入了直播间
✅ 队列处理完成，共处理 6 条消息
🎧 设备进入listening状态，重置防冷场计时器
【进场msg】[104708456492][女]暴风骤雨 进入了直播间
👋 检测到用户进入: 暴风骤雨 (女)
👋 准备发送欢迎消息: 欢迎暴风骤雨来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 暴风骤雨
   设备状态: speaking
   欢迎内容: 欢迎暴风骤雨来到直播间，很高兴见到你！
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 🔥 优先消息已加入队列[位置5]: 暴风骤雨: 进入直播间 (权重:1)
   ↳ 上述消息重复了 3 次
[848508] [12:52:12] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 📦 处理队列: 6条消息
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 🔥 发现 6 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] 📊 队列状态: 6条消息，本次处理上限: 1条
[848508] [12:52:12] 🔥 队列[1/6] 憨包糯米酒: 关注了主播...[优先] (19.8s)
   ↳ 上述消息重复了 2 次
[848508] [12:52:12] ❤️ 正在处理关注消息: 憨包糯米酒
✅ 设备就绪，状态: listening
🔍 发送调用[19]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[19]: 憨包糯米酒终于关注了，眼光还不错嘛
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "憨包糯米酒终于关注了，眼光还不错嘛",...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 憨包糯米酒终于关注了，眼光还不错嘛
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[92004951012109][男]人民和乐 进入了直播间
👋 检测到用户进入: 人民和乐 (男)
👋 准备发送欢迎消息: 欢迎人民和乐进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 人民和乐
   设备状态: speaking
   欢迎内容: 欢迎人民和乐进入直播间！
[848508] [12:52:19] 🔥 优先消息已加入队列[位置0]: 人民和乐: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:52:19] 📦 欢迎消息已加入队列等待处理
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 19
[848508] [12:52:23] ✅ 队列消息发送成功: 憨包糯米酒
⏸️ 已发送 1 条消息，剩余 4 条消息将在下次处理
✅ 队列处理完成，共处理 6 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】11在线观众
【统计msg】当前观看人数: 10, 累计观看人数: 7111
   ↳ 上述消息重复了 2 次
[848508] [12:52:31] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:52:31] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:52:31] 📦 处理队列: 5条消息
   ↳ 上述消息重复了 2 次
[848508] [12:52:31] 🔥 发现 5 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:52:31] 📊 队列状态: 5条消息，本次处理上限: 1条
[848508] [12:52:31] 🔥 队列[1/5] 憨包糯米酒: 进入直播间...[优先] (46.6s)
[848508] [12:52:31] 👋 正在处理欢迎消息: 憨包糯米酒
✅ 设备就绪，状态: listening
🔍 发送调用[20]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[20]: 欢迎憨包糯米酒来到直播间，很高兴见到你！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎憨包糯米酒来到直播间，很高兴见到你...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
🎤 发送: 欢迎憨包糯米酒来到直播间，很高兴见到你！
✅ 发送成功
⏳ 等待设备完成说话...
【进场msg】[141158945929710][男]多乐游戏城 进入了直播间
👋 检测到用户进入: 多乐游戏城 (男)
👋 准备发送欢迎消息: 欢迎多乐游戏城来到直播间，很高兴见到你！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 多乐游戏城
   设备状态: speaking
   欢迎内容: 欢迎多乐游戏城来到直播间，很高兴见到你！
[848508] [12:52:33] 🔥 优先消息已加入队列[位置0]: 多乐游戏城: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[848508] [12:52:33] 📦 欢迎消息已加入队列等待处理
【进场msg】[127976133893341][男]跨洋盛世商贸 进入了直播间
👋 检测到用户进入: 跨洋盛世商贸 (男)
👋 准备发送欢迎消息: 欢迎跨洋盛世商贸进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 跨洋盛世商贸
   设备状态: speaking
   欢迎内容: 欢迎跨洋盛世商贸进入直播间！
[848508] [12:52:35] 🔥 优先消息已加入队列[位置1]: 跨洋盛世商贸: 进入直播间 (权重:1)
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 20
   ↳ 上述消息重复了 2 次
[848508] [12:52:42] ✅ 队列消息发送成功: 憨包糯米酒
⏸️ 已发送 1 条消息，剩余 3 条消息将在下次处理
✅ 队列处理完成，共处理 5 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】13在线观众
【统计msg】当前观看人数: 11, 累计观看人数: 7113
【进场msg】[67029408729][女]从头到尾 进入了直播间
👋 检测到用户进入: 从头到尾 (女)
👋 准备发送欢迎消息: 欢迎从头到尾进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 从头到尾
   设备状态: speaking
   欢迎内容: 欢迎从头到尾进入直播间！
[848508] [12:52:45] 🔥 优先消息已加入队列[位置5]: 从头到尾: 进入直播间 (权重:1)
   ↳ 上述消息重复了 3 次
[848508] [12:52:45] 📦 欢迎消息已加入队列等待处理
   ↳ 上述消息重复了 2 次
[848508] [12:52:51] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[848508] [12:52:51] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[848508] [12:52:51] 📦 处理队列: 6条消息
   ↳ 上述消息重复了 2 次
[848508] [12:52:51] 🔥 发现 6 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[848508] [12:52:51] 📊 队列状态: 6条消息，本次处理上限: 1条
⏰ 跳过过期消息 [1/6]: 暴风骤雨: 进入直播间... (已过期 62.2秒)
[848508] [12:52:51] 🔥 队列[2/6] 暴风骤雨: 进入直播间...[优先] (39.7s)
[848508] [12:52:51] 👋 正在处理欢迎消息: 暴风骤雨
✅ 设备就绪，状态: listening
🔍 发送调用[21]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[21]: 欢迎暴风骤雨来到直播间，很高兴见到你！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "34:cd:b0:b3:df:28", "message": "欢迎暴风骤雨来到直播间，很高兴见到你！...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '34:cd:b0:b3:df:28'}}
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 18
   ↳ 上述消息重复了 2 次
[305200] [12:52:39] ✅ 队列消息发送成功: 苦瓜
⏸️ 已发送 1 条消息，剩余 1 条消息将在下次处理
✅ 队列处理完成，共处理 3 条消息
🎧 设备进入listening状态，重置防冷场计时器
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【点赞msg】果蔬侦察社 点了6个赞
【点赞msg】果蔬侦察社 点了11个赞
【点赞msg】果蔬侦察社 点了11个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 果蔬侦察社, score_str=''
【排行榜】第1名: 果蔬侦察社 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 脸脸, score_str=''
【排行榜】第3名: 脸脸 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 2, 累计观看人数: 24
【点赞msg】果蔬侦察社 点了10个赞
【进场msg】[91727725527][女]脸脸 进入了直播间
👋 检测到用户进入: 脸脸 (女)
👋 准备发送欢迎消息: 欢迎脸脸进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 脸脸
   设备状态: speaking
   欢迎内容: 欢迎脸脸进入直播间！
[305200] [12:52:56] 🔥 优先消息已加入队列[位置3]: 脸脸: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[305200] [12:52:56] 📦 欢迎消息已加入队列等待处理
【点赞msg】果蔬侦察社 点了10个赞
   ↳ 上述消息重复了 2 次
[305200] [12:52:58] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:52:58] 📤 开始处理消息队列...
[305200] [12:52:58] 📦 处理队列: 4条消息
[305200] [12:52:58] 🔥 发现 4 条优先消息（礼物回复等）
[305200] [12:52:58] 📊 队列状态: 4条消息，本次处理上限: 1条
[305200] [12:52:58] 🔥 队列[1/4] 苦瓜: 累计点赞108个...[优先] (27.1s)
   ↳ 上述消息重复了 2 次
[305200] [12:52:58] 👍 正在处理点赞消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[19]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[19]: 苦瓜说给你点赞了 夸夸她
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说给你点赞了 夸夸她", "for...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说给你点赞了 夸夸她
✅ 发送成功
⏳ 等待设备完成说话...
【点赞msg】果蔬侦察社 点了6个赞
✅ 设备已完成说话，可以处理下一条消息
🎧 设备进入listening状态，重置防冷场计时器
🔓 发送锁已释放，消息ID: 19
   ↳ 上述消息重复了 2 次
[305200] [12:53:00] ✅ 队列消息发送成功: 苦瓜
⏸️ 已发送 1 条消息，剩余 2 条消息将在下次处理
✅ 队列处理完成，共处理 4 条消息
🎧 设备进入listening状态，重置防冷场计时器
【直播间统计msg】3在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 果蔬侦察社, score_str=''
【排行榜】第1名: 果蔬侦察社 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 脸脸, score_str=''
【排行榜】第3名: 脸脸 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 2, 累计观看人数: 24
[305200] [12:53:00] 【聊天msg】[101847692837]苦瓜: 那我要把你给退了
[305200] [12:53:00] 🕐 消息时间检查: 启动后 503.5s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=苦瓜, 消息=那我要把你给退了..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苦瓜 (总互动: 5)
📊 房间 200382305200 互动: 苦瓜 (房间互动: 5)
✅ 互动记录完成，当前用户统计总数: 114
📊 记录聊天互动: 苦瓜 -> 那我要把你给退了...
[305200] [12:53:00] 🔥 优先用户消息: 苦瓜 (剩余优先权: 4)
🔥 优先用户消息: 苦瓜 (权重:12)
[305200] [12:53:00] 🔥 优先消息已加入队列[位置0]: 苦瓜: 那我要把你给退了 (权重:12)
📦 用户消息已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:53:00] 📦 处理队列: 3条消息
   ↳ 上述消息重复了 2 次
[305200] [12:53:00] 🔥 发现 3 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:53:00] 📊 队列状态: 3条消息，本次处理上限: 1条
[305200] [12:53:00] 🔥 队列[1/3] 苦瓜: 那我要把你给退了...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:53:00] 💬 正在处理用户消息: 苦瓜
✅ 设备就绪，状态: listening
🔍 发送调用[20]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[20]: 苦瓜说那我要把你给退了
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说那我要把你给退了", "forc...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说那我要把你给退了
✅ 发送成功
⏳ 等待设备完成说话...
✅ 设备已完成说话，可以处理下一条消息
🎧 设备进入listening状态，重置防冷场计时器
🔓 发送锁已释放，消息ID: 20
⏸️ 已发送 1 条消息，剩余 1 条消息将在下次处理
✅ 队列处理完成，共处理 3 条消息
【点赞msg】苦瓜 点了10个赞
👍 苦瓜 点了10个赞，累计100个赞
[305200] [12:53:02] 🎉 点赞互动触发: 苦瓜 累计100个赞，达到100个阈值 -> 苦瓜说给你点个赞 你是最棒的
[305200] [12:53:02] 🔥 苦瓜 点赞达到100个，获得1条优先权
📦 点赞回复加入优先队列（权重:5-中等优先级）
[305200] [12:53:02] 🔥 优先消息已加入队列[位置0]: 苦瓜: 累计点赞100个 (权重:5)
📦 点赞回复已加入队列
   ↳ 上述消息重复了 2 次
[305200] [12:53:02] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[305200] [12:53:02] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:53:02] 📊 队列状态: 2条消息，本次处理上限: 1条
[305200] [12:53:02] 🔥 队列[1/2] 苦瓜: 累计点赞100个...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[305200] [12:53:02] 👍 正在处理点赞消息: 苦瓜
✅ 设备就绪，状态: listening
🚫 发送过于频繁，等待间隔: 0.5秒
🔍 发送调用[21]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[21]: 苦瓜说给你点个赞 你是最棒的
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "苦瓜说给你点个赞 你是最棒的", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 苦瓜说给你点个赞 你是最棒的
✅ 发送成功
⏳ 等待设备完成说话...
✅ 设备已完成说话，可以处理下一条消息
🎧 设备进入listening状态，重置防冷场计时器
🔓 发送锁已释放，消息ID: 21
👋 检测到用户进入: 双穹之骑士•阿斯特拉姆 (女)
👋 准备发送欢迎消息: 欢迎双穹之骑士x阿斯特拉姆进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 双穹之骑士•阿斯特拉姆
   设备状态: speaking
   欢迎内容: 欢迎双穹之骑士x阿斯特拉姆进入直播间！
[348173] [12:52:43] 🔥 优先消息已加入队列[位置10]: 双穹之骑士•阿斯特拉姆: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:52:43] 📦 欢迎消息已加入队列等待处理
【直播间统计msg】31在线观众
【点赞msg】🧛 点了4个赞
👍 🧛 点了4个赞，累计4个赞
🎉 首次点赞互动触发: 🧛 累计4个赞，首次点赞回复 -> 🧛刚刚给你点了赞，请感谢他并主动和他聊聊天
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 🧛 (累计4个赞)
   设备状态: speaking
[348173] [12:52:44] 🔥 优先消息已加入队列[位置9]: 🧛: 累计点赞4个 (权重:5)
   ↳ 上述消息重复了 2 次
[348173] [12:52:44] � 点赞回复已加入优先队列
【统计msg】当前观看人数: 29, 累计观看人数: 2552
【点赞msg】秋雨谱思曲 点了2个赞
👍 秋雨谱思曲 点了2个赞，累计2个赞
🎉 首次点赞互动触发: 秋雨谱思曲 累计2个赞，首次点赞回复 -> 秋雨谱思曲点了赞，请感谢他的支持并主动找话题和他互动
🚫 点赞回复被阻止发送: 设备状态为 speaking，不允许发送消息
   点赞用户: 秋雨谱思曲 (累计2个赞)
   设备状态: speaking
[348173] [12:52:44] 🔥 优先消息已加入队列[位置10]: 秋雨谱思曲: 累计点赞2个 (权重:5)
【直播间统计msg】33在线观众
【进场msg】[59504204461][女]蟹老板 进入了直播间
👋 检测到用户进入: 蟹老板 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 蟹老板
【直播间统计msg】31在线观众
【进场msg】[101256022330][男]诉梦予山鬼。 进入了直播间
👋 检测到用户进入: 诉梦予山鬼。 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 诉梦予山鬼。
【点赞msg】蟹老板 点了10个赞
👍 蟹老板 点了10个赞，累计10个赞
🎉 首次点赞互动触发: 蟹老板 累计10个赞，首次点赞回复 -> 蟹老板刚刚给你点了赞，请感谢他并主动和他聊聊天
📦 点赞回复加入优先队列（权重:5-中等优先级）
[348173] [12:52:46] 🔥 优先消息已加入队列[位置11]: 蟹老板: 累计点赞10个 (权重:5)
📦 点赞回复已加入队列
   ↳ 上述消息重复了 2 次
[348173] [12:52:46] 📦 处理队列: 14条消息
   ↳ 上述消息重复了 2 次
[348173] [12:52:46] 🔥 发现 14 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[348173] [12:52:46] 📊 队列状态: 14条消息，本次处理上限: 2条
[348173] [12:52:46] 🔥 队列[1/14] 海绵宝宝🤪: shutdoen...[优先] (9.2s)
[348173] [12:52:46] 💬 正在处理用户消息: 海绵宝宝🤪
✅ 设备就绪，状态: listening
🔍 发送调用[22]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[22]: 海绵宝宝x说shutdoen
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "海绵宝宝x说shutdoen", "f...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 海绵宝宝x说shutdoen
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 22
[348173] [12:52:57] ✅ 队列消息发送成功: 海绵宝宝🤪
[348173] [12:52:57] 🔥 队列[2/14] 🥭🥭🥭: 录屏...[优先] (19.8s)
[348173] [12:52:57] 💬 正在处理用户消息: 🥭🥭🥭
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 🥭🥭🥭
[348173] [12:53:02] 🔥 队列[3/14] 表哥: @海绵宝宝🤪 拼错了[捂脸][...[优先] (24.8s)
[348173] [12:53:02] 💬 正在处理用户消息: 表哥
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
   ↳ 上述消息重复了 2 次
[348173] [12:53:04] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:53:04] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
✅ 设备就绪，状态: listening
🔍 发送调用[23]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[23]: 表哥说@海绵宝宝🤪 拼错了[捂脸][捂脸]
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "表哥说@海绵宝宝🤪 拼错了[捂脸][捂...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 表哥说@海绵宝宝🤪 拼错了[捂脸][捂脸]
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 23
[348173] [12:53:16] ✅ 队列消息发送成功: 表哥
⏸️ 已发送 2 条消息，剩余 10 条消息将在下次处理
✅ 队列处理完成，共处理 14 条消息
[348173] [12:53:16] 【聊天msg】[75508978693]海绵宝宝🤪: @🥭🥭🥭 [爱心]
[348173] [12:53:16] 🕐 消息时间检查: 启动后 416.2s, 过滤启用: True
[348173] [12:53:16] 🔧 消息预处理: '@🥭🥭🥭 [爱心]' → '@🥭🥭 [爱心]'
🔍 开始记录互动: 房间=431879348173, 用户=海绵宝宝🤪, 消息=@🥭🥭 [爱心]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 海绵宝宝🤪 (总互动: 2)
📊 房间 431879348173 互动: 海绵宝宝🤪 (房间互动: 2)
✅ 互动记录完成，当前用户统计总数: 161
📊 记录聊天互动: 海绵宝宝🤪 -> @🥭🥭 [爱心]...
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[348173] [12:53:16]    用户消息: 海绵宝宝🤪: @🥭🥭 [爱心]
   ↳ 上述消息重复了 2 次
[348173] [12:53:16]    设备状态: speaking
📦 普通用户消息: 海绵宝宝🤪 (权重:10)
[348173] [12:53:16] 🔥 优先消息已加入队列[位置4]: 海绵宝宝🤪: @🥭🥭 [爱心] (权重:10)
📦 用户消息已加入队列
【统计msg】当前观看人数: 29, 累计观看人数: 2520
【点赞msg】秋雨谱思曲 点了9个赞
【点赞msg】钱是英雄胆$ 点了7个赞
⏳ 点赞互动冷却中，还需等待30.3秒
【进场msg】[4253389659314991][女]昌福科技 进入了直播间
👋 检测到用户进入: 昌福科技 (女)
👋 准备发送欢迎消息: 欢迎昌福科技进入直播间！
🚫 欢迎消息被阻止发送: 设备状态为 speaking，不允许发送消息
   用户: 昌福科技
   设备状态: speaking
   欢迎内容: 欢迎昌福科技进入直播间！
[348173] [12:53:16] 🔥 优先消息已加入队列[位置11]: 昌福科技: 进入直播间 (权重:1)
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] 📦 欢迎消息已加入队列等待处理
【进场msg】[102148680347][男]甜汐汐的大力只 进入了直播间
👋 检测到用户进入: 甜汐汐的大力只 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 甜汐汐的大力只
【礼物msg】恩赐♠ 送出了 小心心x1
[348173] [12:53:16] 🎁 礼物消息: 恩赐♠ 送出 小心心x1
[348173] [12:53:16] 🔥 恩赐♠ 获得优先权: 礼物权重3×1=3，优先权1条
[348173] [12:53:16] 🔥 恩赐♠ 当前总优先权: 1条消息
[348173] [12:53:16] 🔍 礼物回复功能状态: enabled=True, 模板数量=1
[348173] [12:53:16] 🔍 可用的礼物回复模板数量: 1
[348173] [12:53:16]    模板 0: 哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧
[348173] [12:53:16] 🎯 选中的礼物回复模板: 哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧
[348173] [12:53:16] 💬 生成的礼物回复内容: 哟 恩赐x又送上1个小心心了 显摆自己有点钱呢是吧
[348173] [12:53:16] 🎁 礼物回复加入优先队列，权重: 3
[348173] [12:53:16] 🔥 优先消息已加入队列[位置9]: 恩赐♠: 送出小心心x1 (权重:3)
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 恩赐♠, score_str=''
【排行榜】第1名: 恩赐♠ - 贡献值: 第1名
🔍 排行榜第2名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第2名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第2名
🔍 排行榜第3名: 厶, score_str=''
【排行榜】第3名: 厶 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】Alone（深秋） 点了7个赞
⏳ 点赞互动冷却中，还需等待30.3秒
【点赞msg】蟹老板 点了3个赞
【点赞msg】钱是英雄胆$ 点了3个赞
⏳ 点赞互动冷却中，还需等待30.3秒
【点赞msg】秋雨谱思曲 点了10个赞
【统计msg】当前观看人数: 29, 累计观看人数: 2552
【点赞msg】甜汐汐的大力只 点了9个赞
⏳ 点赞互动冷却中，还需等待30.2秒
【点赞msg】Alone（深秋） 点了5个赞
⏳ 点赞互动冷却中，还需等待30.2秒
【点赞msg】秋雨谱思曲 点了10个赞
【礼物msg】恩赐♠ 送出了 小心心x1
[348173] [12:53:16] 🔥 恩赐♠ 当前总优先权: 2条消息
[348173] [12:53:16] 🔥 优先消息已加入队列[位置10]: 恩赐♠: 送出小心心x1 (权重:3)
【点赞msg】或许吧 点了10个赞
⏳ 点赞互动冷却中，还需等待30.2秒
【进场msg】[95719992081][男]一路前行 进入了直播间
👋 检测到用户进入: 一路前行 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 一路前行
[348173] [12:53:16] 【聊天msg】[75508978693]海绵宝宝🤪: [互粉][互粉][互粉]
[348173] [12:53:16] 🕐 消息时间检查: 启动后 416.4s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=海绵宝宝🤪, 消息=[互粉][互粉][互粉]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 海绵宝宝🤪 (总互动: 3)
📊 房间 431879348173 互动: 海绵宝宝🤪 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 162
📊 记录聊天互动: 海绵宝宝🤪 -> [互粉][互粉][互粉]...
⏳ 回复冷却中，将消息加入队列等待处理: [互粉][互粉][互粉]
📦 普通用户消息（冷却期）: 海绵宝宝🤪 (权重:10)
[348173] [12:53:16] 🔥 优先消息已加入队列[位置5]: 海绵宝宝🤪: [互粉][互粉][互粉] (权重:10)
📦 冷却期消息已加入队列等待处理
【点赞msg】甜汐汐的大力只 点了12个赞
⏳ 点赞互动冷却中，还需等待30.2秒
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 恩赐♠, score_str=''
【排行榜】第1名: 恩赐♠ - 贡献值: 第1名
🔍 排行榜第2名: 秋雨谱思曲, score_str=''
【排行榜】第2名: 秋雨谱思曲 - 贡献值: 第2名
🔍 排行榜第3名: 久之艾美容店(官渡区新亚洲体育城店), score_str=''
【排行榜】第3名: 久之艾美容店(官渡区新亚洲体育城店) - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】秋雨谱思曲 点了10个赞
【统计msg】当前观看人数: 30, 累计观看人数: 2552
【点赞msg】或许吧 点了11个赞
⏳ 点赞互动冷却中，还需等待30.2秒
【点赞msg】甜汐汐的大力只 点了2个赞
⏳ 点赞互动冷却中，还需等待30.1秒
【直播间统计msg】35在线观众
【点赞msg】蟹老板 点了4个赞
【进场msg】[3677247429412715][男]峥嵘策 进入了直播间
👋 检测到用户进入: 峥嵘策 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 峥嵘策
【统计msg】当前观看人数: 34, 累计观看人数: 2620
【点赞msg】秋雨谱思曲 点了8个赞
【点赞msg】盆鱼宴 点了8个赞
⏳ 点赞互动冷却中，还需等待30.1秒
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 恩赐♠, score_str=''
【排行榜】第1名: 恩赐♠ - 贡献值: 第1名
🔍 排行榜第2名: 秋雨谱思曲, score_str=''
【排行榜】第2名: 秋雨谱思曲 - 贡献值: 第2名
🔍 排行榜第3名: 或许吧, score_str=''
【排行榜】第3名: 或许吧 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【点赞msg】蟹老板 点了10个赞
【统计msg】当前观看人数: 30, 累计观看人数: 2552
【点赞msg】秋雨谱思曲 点了9个赞
【点赞msg】盆鱼宴 点了9个赞
⏳ 点赞互动冷却中，还需等待30.1秒
【关注msg】[325059891169367]罖榮 关注了主播
[348173] [12:53:16] 💖 检测到用户关注: 罖榮
[348173] [12:53:16] 🔥 罖榮 关注主播，获得1条优先权
[348173] [12:53:16] 💖 准备发送关注感谢: 哟罖榮，关注我是你做过最明智的决定
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] 📦 关注回复加入优先队列（权重:6-高优先级）
[348173] [12:53:16] 🔥 优先消息已加入队列[位置6]: 罖榮: 关注了主播 (权重:6)
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] 📦 关注回复已加入队列
【进场msg】[3896295924701662][女]九品带砖侍卫 进入了直播间
👋 检测到用户进入: 九品带砖侍卫 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 九品带砖侍卫
【点赞msg】蟹老板 点了5个赞
【点赞msg】秋雨谱思曲 点了8个赞
【点赞msg】罖榮 点了10个赞
⏳ 点赞互动冷却中，还需等待30.0秒
【进场msg】[571366154969294][男]HK 进入了直播间
👋 检测到用户进入: HK (男)
⏳ 欢迎消息冷却中，跳过欢迎: HK
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 秋雨谱思曲, score_str=''
【排行榜】第1名: 秋雨谱思曲 - 贡献值: 第1名
🔍 排行榜第2名: 恩赐♠, score_str=''
【排行榜】第2名: 恩赐♠ - 贡献值: 第2名
🔍 排行榜第3名: 或许吧, score_str=''
【排行榜】第3名: 或许吧 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[748130461295356][女]能源矩阵_001 进入了直播间
👋 检测到用户进入: 能源矩阵_001 (女)
⏳ 欢迎消息冷却中，跳过欢迎: 能源矩阵_001
【进场msg】[95058200030][男]鼎桥 进入了直播间
👋 检测到用户进入: 鼎桥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 鼎桥
【统计msg】当前观看人数: 34, 累计观看人数: 2620
【点赞msg】蟹老板 点了2个赞
【点赞msg】秋雨谱思曲 点了8个赞
   ↳ 上述消息重复了 3 次
[348173] [12:53:16] 【聊天msg】[325059891169367]罖榮: 异地恋结局是什么
[348173] [12:53:16] 🕐 消息时间检查: 启动后 416.6s, 过滤启用: True
🔍 开始记录互动: 房间=431879348173, 用户=罖榮, 消息=异地恋结局是什么..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 罖榮 (总互动: 3)
📊 房间 431879348173 互动: 罖榮 (房间互动: 3)
✅ 互动记录完成，当前用户统计总数: 162
📊 记录聊天互动: 罖榮 -> 异地恋结局是什么...
🔍 调试信息 - 找到的配置: {'live_id': '11875281106', 'room_name': '是米哒哒呀', 'target_device_id': '10:20:ba:cf:63:f8', 'enabled': True, 'created_at': '2025-07-30T11:08:48.632209', 'created_by': 'misiru', 'activation_code': '5BTVY8XNMQBA', 'welcome_message': {'enabled': True, 'cooldown_seconds': 5, 'templates': []}, 'anti_silence': {'enabled': True, 'delay_seconds': 10, 'cooldown_seconds': 20, 'max_proactive_attempts': 2, 'templates': ['现在直播间有点冷场，请主动和大家打个招呼', '请和观众们互动一下，营造温暖的氛围', '可以主动找个有趣的话题和大家聊聊'], 'personalized_templates': ['{user_name}，刚才聊得挺开心的，还有什么想聊的吗？', '嘿{user_name}，你刚才说的话题很有意思，继续聊聊呗！', '{user_name}，直播间有点安静了，来活跃一下气氛吧！', '{user_name}，有什么问题或想法都可以说出来哦！']}, 'custom_reply': {'enabled': True, 'priority_weight': 8, 'match_mode': 'contains', 'case_sensitive': False, 'cooldown_seconds': 5, 'rules': []}, 'message_preprocess': {'enabled': True, 'filter_emoji': True, 'filter_special_chars': True, 'max_length': 200, 'min_length': 1, 'blocked_words': [], 'replacement_rules': {}}, 'like_interaction': {'enabled': True, 'cooldown_seconds': 20, 'first_like_threshold': 1, 'subsequent_like_threshold': 100, 'templates': []}, 'interactive_games': {'enabled': True, 'cooldown_seconds': 30, 'auto_trigger': True, 'auto_interval_minutes': 5, 'games': {'guess_number': {'enabled': True, 'range': [1, 100]}, 'word_chain': {'enabled': True, 'difficulty': 'medium'}, 'riddle': {'enabled': True, 'category': 'all'}}}, 'anti_spam': {'enabled': True, 'block_duration_seconds': 300, 'warning_cooldown_seconds': 60, 'duplicate_detection': {'enabled': True, 'time_window_seconds': 60, 'max_duplicates': 3, 'similarity_threshold': 0.8}, 'frequency_limit': {'enabled': True, 'time_window_seconds': 30, 'max_messages': 5}, 'warning_messages': ['{user_name}你个刷屏怪，再刷屏把你关小黑屋了！', '{user_name}别刷屏了，给其他人一点发言机会！', '{user_name}刷屏可不是好习惯，消停点！', '{user_name}你这样刷屏很影响直播间秩序哦！']}, 'follow_interaction': {'enabled': True, 'response_templates': ['哟 {user_name}关注了我，是不是看上我了？', '{user_name}终于关注了，眼光还不错嘛']}, 'gift_priority': {'enabled': True, 'gift_response_templates': ['哟 {user_name}又送上{gift_count}个{gift_name}了 显摆自己有点钱呢是吧']}, 'scheduled_broadcast': {'enabled': True, 'interval_minutes': 10, 'templates': ['欢迎大家来到硅灵造物直播间！记得点赞关注哦！', '有什么问题可以随时在评论区提问，我会尽力回答！']}}
✅ 房间 11875281106 激活码验证通过: 激活码有效，剩余 299 天
🚀 准备启动房间: 11875281106 (是米哒哒呀) -> 10:20:ba:cf:63:f8
🚀 启动直播间: 11875281106 (是米哒哒呀) -> 10:20:ba:cf:63:f8
⚠️ 未找到图形终端，使用后台进程模式
✅ 直播间 11875281106 (是米哒哒呀) 已启动
🔧 进程ID: 106252
🎬 启动房间监控: 11875281106
🔍 收到房间列表请求
🔍 从配置文件加载到 19 个房间
🔍 用户 misiru (user) 请求房间列表
⚠️ 进程 7212598169799085351&type=general&ug_source=hw_dy_57dh 已结束 (PID: 105161)
🔍 管理员用户，返回 1 个房间
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
💾 开始保存数据: 113 个用户
✅ 数据保存成功到 data/stats/leaderboard_data.json
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 1 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 1 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 2 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 2 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 11875281106 的官方排行榜，共 1 位用户
📊 通过API更新房间 11875281106 的官方排行榜，共 1 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 200382305200 的官方排行榜，共 3 位用户
📊 通过API更新房间 200382305200 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
📊 更新房间 431879348173 的官方排行榜，共 3 位用户
📊 通过API更新房间 431879348173 的官方排行榜，共 3 位用户
   ↳ 上述消息重复了 3 次
[305200] [12:53:05] ✅ 队列消息发送成功: 苦瓜
⏸️ 已发送 1 条消息，剩余 0 条消息将在下次处理
✅ 队列处理完成，共处理 2 条消息
【点赞msg】苦瓜 点了11个赞
【点赞msg】果蔬侦察社 点了9个赞
【点赞msg】果蔬侦察社 点了10个赞
【点赞msg】苦瓜 点了9个赞
【点赞msg】苦瓜 点了10个赞
【进场msg】[4387500564159500][女]九 进入了直播间
👋 检测到用户进入: 九 (女)
👋 准备发送欢迎消息: 欢迎九进入直播间！
📦 欢迎消息加入优先队列（权重:1-低优先级）
[305200] [12:53:05] 🔥 优先消息已加入队列[位置0]: 九: 进入直播间 (权重:1)
📦 欢迎消息已加入队列
   ↳ 上述消息重复了 3 次
[305200] [12:53:05] 📦 处理队列: 1条消息
   ↳ 上述消息重复了 3 次
[305200] [12:53:05] 🔥 发现 1 条优先消息（礼物回复等）
   ↳ 上述消息重复了 3 次
[305200] [12:53:05] 📊 队列状态: 1条消息，本次处理上限: 1条
[305200] [12:53:05] 🔥 队列[1/1] 九: 进入直播间...[优先] (0.0s)
[305200] [12:53:05] 👋 正在处理欢迎消息: 九
✅ 设备就绪，状态: listening
🚫 发送过于频繁，等待间隔: 0.2秒
🔍 发送调用[22]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[22]: 欢迎九进入直播间！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "欢迎九进入直播间！", "force"...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 欢迎九进入直播间！
✅ 发送成功
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 22
[305200] [12:53:16] ✅ 队列消息发送成功: 九
✅ 队列处理完成，共处理 1 条消息
【点赞msg】果蔬侦察社 点了10个赞
[305200] [12:53:16] 【聊天msg】[91727725527]脸脸: 晓阳哥干嘛了这
[305200] [12:53:16] 🕐 消息时间检查: 启动后 519.1s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=脸脸, 消息=晓阳哥干嘛了这..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 脸脸 (总互动: 1)
📊 房间 200382305200 互动: 脸脸 (房间互动: 1)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 脸脸 -> 晓阳哥干嘛了这...
   ↳ 上述消息重复了 2 次
[305200] [12:53:16] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[305200] [12:53:16]    用户消息: 脸脸: 晓阳哥干嘛了这
   ↳ 上述消息重复了 2 次
[305200] [12:53:16]    设备状态: speaking
🆕 新用户首次发言: 脸脸 (权重:15)
[305200] [12:53:16] 🔥 优先消息已加入队列[位置0]: 脸脸: 晓阳哥干嘛了这 (权重:15)
🆕 新用户消息已加入高优先级队列
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【直播间统计msg】4在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 果蔬侦察社, score_str=''
【排行榜】第1名: 果蔬侦察社 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 脸脸, score_str=''
【排行榜】第3名: 脸脸 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 2, 累计观看人数: 24
【点赞msg】苦瓜 点了10个赞
【点赞msg】果蔬侦察社 点了10个赞
【点赞msg】果蔬侦察社 点了8个赞
⏳ 点赞互动冷却中，还需等待46.2秒
【点赞msg】苦瓜 点了9个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 果蔬侦察社, score_str=''
【排行榜】第1名: 果蔬侦察社 - 贡献值: 第1名
🔍 排行榜第2名: 用户723542324, score_str=''
【排行榜】第2名: 用户723542324 - 贡献值: 第2名
🔍 排行榜第3名: 脸脸, score_str=''
【排行榜】第3名: 脸脸 - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【统计msg】当前观看人数: 3, 累计观看人数: 25
【点赞msg】苦瓜 点了10个赞
【点赞msg】苦瓜 点了11个赞
【统计msg】当前观看人数: 3, 累计观看人数: 25
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待44.7秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待44.7秒
【直播间统计msg】2在线观众
🔍 收到排行榜消息，共 2 位用户
🔍 排行榜第1名: 果蔬侦察社, score_str=''
【排行榜】第1名: 果蔬侦察社 - 贡献值: 第1名
🔍 排行榜第2名: 脸脸, score_str=''
【排行榜】第2名: 脸脸 - 贡献值: 第2名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共2位用户
【统计msg】当前观看人数: 3, 累计观看人数: 25
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待42.1秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待42.1秒
【点赞msg】苦瓜 点了10个赞
⏳ 点赞互动冷却中，还需等待39.5秒
【点赞msg】苦瓜 点了11个赞
⏳ 点赞互动冷却中，还需等待38.7秒
[305200] [12:53:25] 【聊天msg】[101847692837]苦瓜: 直播带货了么 姐
[305200] [12:53:25] 🕐 消息时间检查: 启动后 528.6s, 过滤启用: True
🔍 开始记录互动: 房间=200382305200, 用户=苦瓜, 消息=直播带货了么 姐..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 苦瓜 (总互动: 6)
📊 房间 200382305200 互动: 苦瓜 (房间互动: 6)
✅ 互动记录完成，当前用户统计总数: 115
📊 记录聊天互动: 苦瓜 -> 直播带货了么 姐...
   ↳ 上述消息重复了 2 次
[305200] [12:53:25] 🔥 优先用户消息: 苦瓜 (剩余优先权: 4)
   ↳ 上述消息重复了 2 次
[305200] [12:53:25] 🚫 消息被阻止发送: 设备状态为 speaking，不允许发送消息
[305200] [12:53:25]    用户消息: 苦瓜: 直播带货了么 姐
   ↳ 上述消息重复了 2 次
[305200] [12:53:25]    设备状态: speaking
🔥 优先用户消息: 苦瓜 (权重:12)
[305200] [12:53:25] 🔥 优先消息已加入队列[位置1]: 苦瓜: 直播带货了么 姐 (权重:12)
📦 用户消息已加入队列
【点赞msg】苦瓜 点了9个赞
⏳ 点赞互动冷却中，还需等待36.7秒
   ↳ 上述消息重复了 2 次
[305200] [12:53:26] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[305200] [12:53:26] 📤 开始处理消息队列...
   ↳ 上述消息重复了 2 次
[305200] [12:53:26] 📦 处理队列: 2条消息
   ↳ 上述消息重复了 2 次
[305200] [12:53:26] 🔥 发现 2 条优先消息（礼物回复等）
   ↳ 上述消息重复了 2 次
[305200] [12:53:26] 📊 队列状态: 2条消息，本次处理上限: 1条
[305200] [12:53:26] 🔥 队列[1/2] 脸脸: 晓阳哥干嘛了这...[优先] (9.7s)
[305200] [12:53:26] 💬 正在处理用户消息: 脸脸
✅ 设备就绪，状态: listening
🔍 发送调用[23]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[23]: 脸脸说晓阳哥干嘛了这
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "30:ed:a0:29:f0:a4", "message": "脸脸说晓阳哥干嘛了这", "force...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '30:ed:a0:29:f0:a4'}}
🎤 发送: 脸脸说晓阳哥干嘛了这
✅ 发送成功
【点赞msg】苦瓜 点了10个赞
[348173] [12:53:16] 🔥 优先用户消息: 罖榮 (剩余优先权: 1)
[348173] [12:53:16] 🔥 罖榮 优先权已用完
⏳ 回复冷却中，将消息加入队列等待处理: 异地恋结局是什么
📦 普通用户消息（冷却期）: 罖榮 (权重:10)
[348173] [12:53:16] 🔥 优先消息已加入队列[位置6]: 罖榮: 异地恋结局是什么 (权重:10)
📦 冷却期消息已加入队列等待处理
【统计msg】当前观看人数: 34, 累计观看人数: 2620
【点赞msg】钱是英雄胆$ 点了2个赞
⏳ 点赞互动冷却中，还需等待29.9秒
【点赞msg】秋雨谱思曲 点了8个赞
[348173] [12:53:16] 【聊天msg】[75508978693]海绵宝宝🤪: [捂脸][捂脸][捂脸]
🔍 开始记录互动: 房间=431879348173, 用户=海绵宝宝🤪, 消息=[捂脸][捂脸][捂脸]..., 机器人回复=True
✅ 消息有效，开始记录数据...
📊 记录成功互动: 海绵宝宝🤪 (总互动: 4)
📊 房间 431879348173 互动: 海绵宝宝🤪 (房间互动: 4)
✅ 互动记录完成，当前用户统计总数: 162
📊 记录聊天互动: 海绵宝宝🤪 -> [捂脸][捂脸][捂脸]...
⏳ 回复冷却中，将消息加入队列等待处理: [捂脸][捂脸][捂脸]
📦 普通用户消息（冷却期）: 海绵宝宝🤪 (权重:10)
[348173] [12:53:16] 🔥 优先消息已加入队列[位置7]: 海绵宝宝🤪: [捂脸][捂脸][捂脸] (权重:10)
📦 冷却期消息已加入队列等待处理
【点赞msg】云蚂蚁健身中心销售部 点了7个赞
⏳ 点赞互动冷却中，还需等待29.9秒
【直播间统计msg】32在线观众
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 秋雨谱思曲, score_str=''
【排行榜】第1名: 秋雨谱思曲 - 贡献值: 第1名
🔍 排行榜第2名: 盆鱼宴, score_str=''
【排行榜】第2名: 盆鱼宴 - 贡献值: 第2名
🔍 排行榜第3名: 恩赐♠, score_str=''
【排行榜】第3名: 恩赐♠ - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
【进场msg】[83045420741][男]胖哥 进入了直播间
👋 检测到用户进入: 胖哥 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 胖哥
【点赞msg】钱是英雄胆$ 点了12个赞
⏳ 点赞互动冷却中，还需等待29.9秒
【点赞msg】秋雨谱思曲 点了10个赞
【统计msg】当前观看人数: 34, 累计观看人数: 2620
【进场msg】[60722758673][男]百川m 进入了直播间
👋 检测到用户进入: 百川m (男)
⏳ 欢迎消息冷却中，跳过欢迎: 百川m
【进场msg】[73208612569][男]罗伯茨茨茨📸 进入了直播间
👋 检测到用户进入: 罗伯茨茨茨📸 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 罗伯茨茨茨📸
【进场msg】[540568836116878][男]至尊宝同学🧐 进入了直播间
👋 检测到用户进入: 至尊宝同学🧐 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 至尊宝同学🧐
【点赞msg】司晨 点了1个赞
⏳ 点赞互动冷却中，还需等待29.8秒
【统计msg】当前观看人数: 34, 累计观看人数: 2738
【点赞msg】钱是英雄胆$ 点了4个赞
⏳ 点赞互动冷却中，还需等待29.8秒
【进场msg】[69998519713][男]鱼子酱xw101 进入了直播间
👋 检测到用户进入: 鱼子酱xw101 (男)
⏳ 欢迎消息冷却中，跳过欢迎: 鱼子酱xw101
【点赞msg】秋雨谱思曲 点了7个赞
🔍 收到排行榜消息，共 3 位用户
🔍 排行榜第1名: 秋雨谱思曲, score_str=''
【排行榜】第1名: 秋雨谱思曲 - 贡献值: 第1名
🔍 排行榜第2名: 盆鱼宴, score_str=''
【排行榜】第2名: 盆鱼宴 - 贡献值: 第2名
🔍 排行榜第3名: 恩赐♠, score_str=''
【排行榜】第3名: 恩赐♠ - 贡献值: 第3名
✅ 排行榜数据已传递给统计模块
【直播间排行榜msg】排行榜更新，共3位用户
[348173] [12:53:16] 🕐 消息时间检查: 启动后 416.8s, 过滤启用: True
🚨 检测到重复刷屏: 罖榮 发送了 3 条相似消息 (相似度阈值: 0.8)
🚫 用户 罖榮(325059891169367) 因刷屏被屏蔽 300 秒
[348173] [12:53:16] 🔥 ⚠️ 检测到刷屏行为: 罖榮 - duplicate
[348173] [12:53:16] 🔥 发送防刷屏警告: 罖榮你个刷屏怪，再刷屏把你关小黑屋了！
[348173] [12:53:16] 🔥 优先消息已加入队列[位置0]: 系统警告: 防刷屏警告:罖榮 (权重:999)
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] 🔥 防刷屏警告已加入高优先级队列
[348173] [12:53:16] 📦 处理队列: 19条消息
[348173] [12:53:16] 🔥 发现 19 条优先消息（礼物回复等）
[348173] [12:53:16] 📊 队列状态: 19条消息，本次处理上限: 3条
[348173] [12:53:16] 🔥 队列[1/19] 系统警告: 防刷屏警告:罖榮...[优先] (0.0s)
   ↳ 上述消息重复了 2 次
[348173] [12:53:16] ❓ 正在处理未知类型消息(anti_spam): 系统警告
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
✅ 设备就绪，状态: listening
🔍 发送调用[24]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[24]: 罖榮你个刷屏怪，再刷屏把你关小黑屋了！
🔗 连接WebSocket: ws://localhost:15000
📤 发送WebSocket消息: {"type": "speak_message", "data": {"device_id": "10:20:ba:cf:5f:58", "message": "罖榮你个刷屏怪，再刷屏把你关小黑屋了！...
⏳ 等待WebSocket响应...
📥 收到WebSocket响应: {'type': 'speak_response', 'data': {'success': True, 'message': 'Speak command sent to device', 'target_device': '10:20:ba:cf:5f:58'}}
🎤 发送: 罖榮你个刷屏怪，再刷屏把你关小黑屋了！
✅ 发送成功
   ↳ 上述消息重复了 2 次
[348173] [12:53:24] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:53:24] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
⏳ 等待设备完成说话...
⏰ 等待超时(10s)，状态: speaking
⚠️ 等待设备完成说话超时，继续处理下一条消息
🔓 发送锁已释放，消息ID: 24
   ↳ 上述消息重复了 2 次
[348173] [12:53:34] ✅ 队列消息发送成功: 系统警告
[348173] [12:53:34] 🔥 队列[2/19] 苍穹巫诗: 我叼，什么东西...[优先] (56.9s)
[348173] [12:53:34] 💬 正在处理用户消息: 苍穹巫诗
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
⏰ 等待设备就绪超时 (5秒)
⚠️ 等待超时(5s)，跳过: 苍穹巫诗
⏰ 跳过过期消息 [3/19]: 罖榮: 异地恋结局是什么... (已过期 61.9秒)
⏰ 跳过过期消息 [4/19]: 已注销: 已读乱回... (已过期 62.3秒)
⏰ 跳过过期消息 [5/19]: 罖榮: 异地恋结局是什么... (已过期 61.1秒)
[348173] [12:53:39] 🔥 队列[6/19] 海绵宝宝🤪: @🥭🥭 [爱心]...[优先] (23.4s)
   ↳ 上述消息重复了 2 次
[348173] [12:53:39] 💬 正在处理用户消息: 海绵宝宝🤪
⏳ 等待设备就绪，当前状态: speaking
⏳ 等待设备就绪，当前状态: speaking
   ↳ 上述消息重复了 2 次
[348173] [12:53:41] 🔄 设备状态变化: speaking -> listening
   ↳ 上述消息重复了 2 次
[348173] [12:53:41] 📤 开始处理消息队列...
📦 消息队列为空，无需处理
🎧 设备进入listening状态，重置防冷场计时器
✅ 设备就绪，状态: listening
🔍 发送调用[25]来源: live_chat_bot.py:2024 - _process_message_queue
🔍 发送消息内容[25]: 海绵宝宝x说@🥭🥭 [爱心]
🔗 连接WebSocket: ws://localhost:15000
