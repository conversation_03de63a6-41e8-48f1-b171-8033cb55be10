#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程管理控制脚本
提供命令行接口来管理多直播间机器人进程
"""

import argparse
import json
import sys
import time
import subprocess
import os
import threading
from datetime import datetime
import platform

class ProcessManager:
    """简化的进程管理器"""

    def __init__(self):
        self.processes = {}
        self.config_file = "rooms_config.json"

    def get_all_processes_status(self):
        """获取所有进程状态（返回JSON可序列化的数据）"""
        status = {}
        for live_id, info in self.processes.items():
            # 创建不包含Popen对象的状态信息
            status[live_id] = {
                'room_name': info.get('room_name', ''),
                'target_device_id': info.get('target_device_id', ''),
                'is_running': info.get('is_running', False),
                'pid': info.get('pid'),
                'start_time': info.get('start_time'),
                'running_time_seconds': info.get('running_time_seconds', 0),
                'message_count': info.get('message_count', 0)
            }
        return status

    def start_live_process(self, live_id, target_device_id, room_name):
        """启动直播间进程"""
        try:
            # 参数验证和清理
            if not live_id:
                raise ValueError("live_id 不能为空")
            if not target_device_id:
                raise ValueError("target_device_id 不能为空")
            if not room_name:
                room_name = f"房间{live_id}"
                print(f"⚠️ room_name 为空，使用默认名称: {room_name}")

            # 清理房间名称中的特殊字符，避免在窗口标题中出现问题
            safe_room_name = str(room_name).replace('"', '').replace("'", "").replace('\\', '').replace('/', '')
            print(f"🚀 启动直播间: {live_id} ({safe_room_name}) -> {target_device_id}")
            # 创建配置文件
            config = {
                "live_settings": {
                    "live_id": live_id
                },
                "esp32_settings": {
                    "device_id": target_device_id
                },
                "websocket_settings": {
                    "url": "ws://localhost:15000"
                },
                "reply_settings": {
                    "enabled": True,
                    "template": "{user_name}说{content}",
                    "cooldown_seconds": 2
                },
                "filter_settings": {
                    "min_message_length": 1,
                    "max_message_length": 100,
                    "blocked_keywords": [],
                    "blocked_users": []
                },
                "anti_spam": {
                    "enabled": True,
                    "duplicate_detection": {
                        "enabled": True,
                        "time_window_seconds": 60,
                        "max_duplicates": 3,
                        "similarity_threshold": 0.8
                    },
                    "frequency_limit": {
                        "enabled": True,
                        "time_window_seconds": 30,
                        "max_messages": 5
                    },
                    "warning_messages": [
                        "{user_name}你个刷屏怪，再刷屏把你关小黑屋了！",
                        "{user_name}别刷屏了，给其他人一点发言机会！",
                        "{user_name}刷屏可不是好习惯，消停点！",
                        "{user_name}你这样刷屏很影响直播间秩序哦！"
                    ],
                    "block_duration_seconds": 300,
                    "warning_cooldown_seconds": 60
                },
                "status_check": {
                    "enabled": True,
                    "interval_seconds": 10
                },
                "message_queue": {
                    "enabled": True,
                    "max_size": 50,
                    "max_age_seconds": 60
                },
                "force_send": {
                    "enabled": True,
                    "timeout_seconds": 60,
                    "user_only": True
                },
                "status_monitor": {
                    "enabled": True,
                    "interval_seconds": 1
                },
                "logging": {
                    "level": "INFO",
                    "filter_http_requests": True,
                    "filter_heartbeat": True,
                    "filter_device_status": False,
                    "merge_duplicate_logs": True,
                    "show_timestamps": True,
                    "highlight_important": True
                }
            }

            config_filename = f"config_{live_id}.json"
            with open(config_filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 启动进程，使用独立终端窗口
            process = self._start_process_with_separate_window(
                live_id, safe_room_name, config_filename
            )

            if process:
                self.processes[live_id] = {
                    'process': process,
                    'room_name': safe_room_name,
                    'target_device_id': target_device_id,
                    'is_running': True,
                    'pid': process.pid,
                    'start_time': time.time(),
                    'running_time_seconds': 0
                }

                print(f"✅ 直播间 {live_id} ({safe_room_name}) 已启动")
                print(f"🔧 进程ID: {process.pid}")
            else:
                print(f"❌ 启动直播间 {live_id} 失败")

            return True

        except ValueError as e:
            print(f"❌ 参数错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 启动进程失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _start_process_with_separate_window(self, live_id, room_name, config_filename):
        """在独立窗口中启动进程"""
        try:
            # 检测操作系统
            system = platform.system().lower()

            # 构建启动命令
            python_cmd = [sys.executable, "live_chat_bot.py", config_filename]

            if system == "windows":
                # Windows: 使用 cmd 新窗口
                cmd = [
                    "cmd", "/c", "start",
                    f"直播间{live_id}_{room_name}",  # 窗口标题
                    "cmd", "/k",  # 保持窗口打开
                    f"python live_chat_bot.py {config_filename}"
                ]

                # 启动新的cmd窗口
                process = subprocess.Popen(
                    cmd,
                    cwd=os.getcwd(),
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )

            elif system == "darwin":  # macOS
                # macOS: 使用 Terminal.app
                applescript = f'''
                tell application "Terminal"
                    do script "cd '{os.getcwd()}' && python live_chat_bot.py {config_filename}"
                    set custom title of front window to "直播间{live_id}_{room_name}"
                end tell
                '''

                subprocess.run(["osascript", "-e", applescript])

                # 直接启动进程（因为AppleScript已经启动了Terminal）
                process = subprocess.Popen(python_cmd, cwd=os.getcwd())

            else:  # Linux
                # Linux: 尝试使用不同的终端
                terminals = [
                    ["gnome-terminal", "--title", f"直播间{live_id}_{room_name}", "--", "bash", "-c"],
                    ["xterm", "-title", f"直播间{live_id}_{room_name}", "-e", "bash", "-c"],
                    ["konsole", "--title", f"直播间{live_id}_{room_name}", "-e", "bash", "-c"],
                    ["xfce4-terminal", "--title", f"直播间{live_id}_{room_name}", "-e", "bash", "-c"]
                ]

                process = None
                for terminal_cmd in terminals:
                    try:
                        full_cmd = terminal_cmd + [f"python live_chat_bot.py {config_filename}; read -p '按Enter键关闭...'"]
                        process = subprocess.Popen(full_cmd, cwd=os.getcwd())
                        break
                    except FileNotFoundError:
                        continue

                if not process:
                    # 如果没有找到图形终端，使用后台进程
                    print(f"⚠️ 未找到图形终端，使用后台进程模式")
                    process = subprocess.Popen(python_cmd, cwd=os.getcwd())

            return process

        except Exception as e:
            print(f"❌ 启动独立窗口失败: {e}")
            # 回退到普通模式
            try:
                process = subprocess.Popen(python_cmd, cwd=os.getcwd())
                print(f"⚠️ 使用后台进程模式启动")
                return process
            except Exception as e2:
                print(f"❌ 后备启动方式也失败: {e2}")
                return None

    def stop_live_process(self, live_id):
        """停止直播间进程"""
        if live_id in self.processes:
            try:
                process_info = self.processes[live_id]
                process = process_info['process']
                process.terminate()
                process.wait(timeout=5)

                # 清理配置文件
                config_filename = f"config_{live_id}.json"
                if os.path.exists(config_filename):
                    os.remove(config_filename)

                del self.processes[live_id]
                return True

            except Exception as e:
                print(f"❌ 停止进程失败: {e}")
                return False
        return False

    def restart_live_process(self, live_id):
        """重启直播间进程"""
        if live_id in self.processes:
            process_info = self.processes[live_id]
            room_name = process_info.get('room_name', f'房间{live_id}')
            target_device_id = process_info.get('target_device_id', 'unknown_device')

            self.stop_live_process(live_id)
            time.sleep(1)
            return self.start_live_process(live_id, target_device_id, room_name)
        return False

    def stop_all_processes(self):
        """停止所有进程"""
        live_ids = list(self.processes.keys())
        for live_id in live_ids:
            self.stop_live_process(live_id)

    def update_process_status(self):
        """更新进程状态"""
        for live_id, info in list(self.processes.items()):
            process = info['process']
            if process.poll() is not None:
                # 进程已结束
                info['is_running'] = False
                print(f"⚠️ 进程 {live_id} 已结束 (PID: {process.pid})")
            else:
                # 进程仍在运行
                info['is_running'] = True
                info['running_time_seconds'] = int(time.time() - info['start_time'])

def show_status(pm):
    """显示所有进程状态"""
    processes = pm.get_all_processes_status()

    if not processes:
        print("📋 当前没有运行的进程")
        return

    print(f"📋 当前运行 {len(processes)} 个直播间进程:")
    print("-" * 80)
    print(f"{'直播间ID':<15} {'进程名称':<20} {'目标设备':<20} {'状态':<10} {'PID':<8} {'运行时间'}")
    print("-" * 80)
    
    for live_id, info in processes.items():
        status_text = "运行中" if info['is_running'] else "已停止"
        running_time = format_time(info.get('running_time_seconds', 0))

        print(f"{live_id:<15} {info['room_name']:<20} {info['target_device_id']:<20} "
              f"{status_text:<10} {info.get('pid', 'N/A'):<8} {running_time}")

    print("-" * 80)
    print(f"💡 每个直播间在独立的终端窗口中运行")
    print(f"💡 可以直接在对应的终端窗口查看实时日志")

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        return f"{seconds//60}m{seconds%60}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}h{minutes}m"

def start_process(pm, live_id, target_device_id, room_name):
    """启动进程"""
    print(f"🚀 启动直播间进程: {live_id}")
    
    success = pm.start_live_process(live_id, target_device_id, room_name)
    
    if success:
        print(f"✅ 进程启动成功: {room_name}")
    else:
        print(f"❌ 进程启动失败: {live_id}")
        sys.exit(1)

def stop_process(pm, live_id):
    """停止进程"""
    print(f"🛑 停止直播间进程: {live_id}")
    
    success = pm.stop_live_process(live_id)
    
    if success:
        print(f"✅ 进程停止成功: {live_id}")
    else:
        print(f"❌ 进程停止失败: {live_id}")
        sys.exit(1)

def restart_process(pm, live_id):
    """重启进程"""
    print(f"🔄 重启直播间进程: {live_id}")
    
    success = pm.restart_live_process(live_id)
    
    if success:
        print(f"✅ 进程重启成功: {live_id}")
    else:
        print(f"❌ 进程重启失败: {live_id}")
        sys.exit(1)

def stop_all_processes(pm):
    """停止所有进程"""
    processes = pm.get_all_processes_status()
    
    if not processes:
        print("📋 当前没有运行的进程")
        return
    
    print(f"🛑 停止所有 {len(processes)} 个进程...")
    
    pm.stop_all_processes()
    print("✅ 所有进程已停止")

def load_config_and_start_all(pm):
    """从配置文件加载并启动所有直播间"""
    try:
        with open('rooms_config.json', 'r', encoding='utf-8') as f:
            rooms = json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件 rooms_config.json 不存在")
        sys.exit(1)
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误")
        sys.exit(1)
    
    live_rooms = rooms
    
    if not live_rooms:
        print("⚠️ 配置文件中没有直播间配置")
        return
    
    print(f"🚀 从配置文件启动 {len(live_rooms)} 个直播间...")
    
    success_count = 0
    for live_id, room_config in live_rooms.items():
        # 兼容新旧配置格式
        enabled = (room_config.get('enabled', True) or
                  room_config.get('room_info', {}).get('enabled', True))

        room_name = (room_config.get('room_name') or
                    room_config.get('room_info', {}).get('room_name') or
                    f'房间{live_id}')

        if not enabled:
            print(f"⏭️ 跳过已禁用的直播间: {room_name}")
            continue

        target_device_id = (room_config.get('target_device_id') or
                           room_config.get('device_id') or
                           room_config.get('esp32_settings', {}).get('device_id'))
        
        print(f"📱 启动: {room_name} ({live_id}) -> {target_device_id}")
        
        if pm.start_live_process(live_id, target_device_id, room_name):
            success_count += 1
            print(f"✅ 启动成功")
        else:
            print(f"❌ 启动失败")
        
        # 间隔1秒启动下一个，避免同时启动太多进程
        time.sleep(1)
    
    print(f"🎯 启动完成: {success_count}/{len(live_rooms)} 个进程启动成功")

def monitor_processes(pm):
    """监控进程状态"""
    print("👀 开始监控进程状态 (按 Ctrl+C 退出)")
    print("-" * 50)
    
    try:
        while True:
            processes = pm.get_all_processes_status()
            
            # 清屏
            print("\033[2J\033[H", end="")
            
            print(f"📊 进程监控 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
            
            if processes:
                print(f"{'直播间ID':<15} {'进程名称':<20} {'状态':<10} {'PID':<8} {'消息数':<8} {'运行时间'}")
                print("-" * 80)
                
                for live_id, info in processes.items():
                    status_text = "🟢运行中" if info['is_running'] else "🔴已停止"
                    running_time = format_time(info.get('running_time_seconds', 0))
                    message_count = info.get('message_count', 0)
                    
                    print(f"{live_id:<15} {info['room_name']:<20} {status_text:<10} "
                          f"{info.get('pid', 'N/A'):<8} {message_count:<8} {running_time}")
            else:
                print("📋 当前没有运行的进程")
            
            time.sleep(5)  # 每5秒刷新一次
            
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

def main():
    parser = argparse.ArgumentParser(description='多直播间机器人进程管理器')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 状态命令
    subparsers.add_parser('status', help='显示所有进程状态')
    
    # 启动命令
    start_parser = subparsers.add_parser('start', help='启动直播间进程')
    start_parser.add_argument('live_id', help='直播间ID')
    start_parser.add_argument('target_device_id', help='目标设备ID')
    start_parser.add_argument('--room_name', default='', help='直播间名称')
    
    # 停止命令
    stop_parser = subparsers.add_parser('stop', help='停止直播间进程')
    stop_parser.add_argument('live_id', help='直播间ID')
    
    # 重启命令
    restart_parser = subparsers.add_parser('restart', help='重启直播间进程')
    restart_parser.add_argument('live_id', help='直播间ID')
    
    # 停止所有命令
    subparsers.add_parser('stop-all', help='停止所有进程')
    
    # 从配置启动所有
    subparsers.add_parser('start-all', help='从配置文件启动所有直播间')
    
    # 监控命令
    subparsers.add_parser('monitor', help='实时监控进程状态')

    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 获取进程管理器
    pm = ProcessManager()
    
    try:
        if args.command == 'status':
            show_status(pm)
        
        elif args.command == 'start':
            room_name = args.room_name or f"直播间_{args.live_id}"
            start_process(pm, args.live_id, args.target_device_id, room_name)
        
        elif args.command == 'stop':
            stop_process(pm, args.live_id)
        
        elif args.command == 'restart':
            restart_process(pm, args.live_id)
        
        elif args.command == 'stop-all':
            stop_all_processes(pm)
        
        elif args.command == 'start-all':
            load_config_and_start_all(pm)
        
        elif args.command == 'monitor':
            monitor_processes(pm)

    except KeyboardInterrupt:
        print("\n👋 操作已取消")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()