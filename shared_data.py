#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享数据模块
WebSocket服务器和Flask服务器之间的数据共享
"""

import json
import os
from datetime import datetime
from threading import Lock

# 全局数据存储
connected_devices = {}
device_configs = {}
rooms = {}

# 线程锁
data_lock = Lock()

def load_configs():
    """加载配置文件"""
    global device_configs, rooms
    
    with data_lock:
        try:
            # 加载设备配置
            if os.path.exists('config/devices/devices.json'):
                with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                    device_configs = json.load(f)
                print(f"✅ 加载了 {len(device_configs)} 个设备配置")
            elif os.path.exists('devices_config.json'):
                with open('devices_config.json', 'r', encoding='utf-8') as f:
                    device_configs = json.load(f)
                print(f"✅ 加载了 {len(device_configs)} 个设备配置（旧格式）")
            
            # 加载房间配置
            if os.path.exists('rooms_config.json'):
                with open('rooms_config.json', 'r', encoding='utf-8') as f:
                    rooms = json.load(f)
                print(f"✅ 加载了 {len(rooms)} 个房间配置")
                
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")

def save_device_configs():
    """保存设备配置"""
    with data_lock:
        try:
            # 确保目录存在
            os.makedirs('config/devices', exist_ok=True)

            # 保存到新的配置路径
            with open('config/devices/devices.json', 'w', encoding='utf-8') as f:
                json.dump(device_configs, f, ensure_ascii=False, indent=2)
            print(f"💾 设备配置已保存，共 {len(device_configs)} 个设备")
        except Exception as e:
            print(f"❌ 保存设备配置失败: {e}")

def save_rooms_config():
    """保存房间配置"""
    with data_lock:
        try:
            with open('rooms_config.json', 'w', encoding='utf-8') as f:
                json.dump(rooms, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存房间配置失败: {e}")

def register_device(device_id, websocket, device_info):
    """注册设备"""
    print(f"🔄 进入register_device函数，设备ID: {device_id}")

    try:
        with data_lock:
            print(f"🔒 获取到数据锁")

            # 保存连接信息
            connected_devices[device_id] = {
                'websocket': websocket,
                'device_name': device_info.get('device_name', device_id),
                'device_type': device_info.get('device_type', 'esp32'),
                'capabilities': device_info.get('capabilities', ''),
                'connect_time': datetime.now().isoformat(),
                'device_state': 'idle',
                'client_id': device_info.get('client_id', '')
            }
            print(f"💾 连接信息已保存")

            # 保存到配置文件
            device_configs[device_id] = {
                'device_id': device_id,
                'device_name': device_info.get('device_name', device_id),
                'device_type': device_info.get('device_type', 'esp32'),
                'capabilities': device_info.get('capabilities', ''),
                'created_at': datetime.now().isoformat(),
                'last_seen': datetime.now().isoformat(),
                'enabled': True,
                'auto_registered': True
            }
            print(f"📋 配置信息已保存")

            print(f"🔄 开始保存配置文件...")
            save_device_configs()
            print(f"📱 设备已注册到管理器: {device_id}")

    except Exception as e:
        print(f"❌ register_device内部错误: {e}")
        import traceback
        traceback.print_exc()

def unregister_device(websocket):
    """注销设备"""
    with data_lock:
        for device_id, device_info in list(connected_devices.items()):
            if device_info.get('websocket') == websocket:
                del connected_devices[device_id]
                print(f"📱 设备断开: {device_id}")
                break

def get_devices_list():
    """获取设备列表"""
    with data_lock:
        devices = []
        
        # 添加所有配置中的设备
        for device_id, config in device_configs.items():
            is_online = device_id in connected_devices
            
            if is_online:
                # 在线设备
                device_info = connected_devices[device_id]
                devices.append({
                    'device_id': device_id,
                    'device_name': device_info.get('device_name', config.get('device_name', device_id)),
                    'device_type': device_info.get('device_type', config.get('device_type', 'esp32')),
                    'device_state': device_info.get('device_state', 'unknown'),
                    'capabilities': device_info.get('capabilities', config.get('capabilities', '')),
                    'online': True,
                    'connect_time': device_info.get('connect_time'),
                    'auto_registered': config.get('auto_registered', False)
                })
            else:
                # 离线设备
                devices.append({
                    'device_id': device_id,
                    'device_name': config.get('device_name', device_id),
                    'device_type': config.get('device_type', 'esp32'),
                    'device_state': 'offline',
                    'capabilities': config.get('capabilities', ''),
                    'online': False,
                    'last_seen': config.get('last_seen', ''),
                    'auto_registered': config.get('auto_registered', False)
                })
                
        return devices

def get_status():
    """获取系统状态"""
    with data_lock:
        online_devices = len(connected_devices)
        total_devices = len(device_configs)
        
        return {
            'server': 'ESP32 WebSocket Server',
            'connected_devices': online_devices,
            'total_devices': total_devices,
            'offline_devices': total_devices - online_devices,
            'timestamp': datetime.now().isoformat(),
            'status': 'running'
        }

def update_device_status(device_id, device_state):
    """更新设备状态"""
    with data_lock:
        if device_id in connected_devices:
            connected_devices[device_id]['device_state'] = device_state
            connected_devices[device_id]['last_update'] = datetime.now().isoformat()

def send_tts_to_device(device_id, message):
    """发送TTS到设备"""
    with data_lock:
        if device_id in connected_devices:
            websocket = connected_devices[device_id]['websocket']
            return websocket, message
        return None, None

# 初始化
load_configs()
