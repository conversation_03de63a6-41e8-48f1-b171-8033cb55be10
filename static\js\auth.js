/**
 * 认证相关的JavaScript函数
 */

// 检查用户是否已登录
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        return false;
    }
    return true;
}

// 检查管理员权限
async function checkAdminPermission() {
    try {
        const token = localStorage.getItem('auth_token');
        if (!token) {
            window.location.href = '/login';
            return false;
        }

        const response = await fetch('/api/auth/verify', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.user.role === 'admin') {
                return true;
            } else {
                alert('访问拒绝：需要管理员权限');
                window.location.href = '/';
                return false;
            }
        } else {
            alert('身份验证失败');
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
            return false;
        }
    } catch (error) {
        console.error('验证管理员权限失败:', error);
        alert('验证权限时发生错误');
        window.location.href = '/login';
        return false;
    }
}

// 获取当前用户信息
async function getCurrentUserInfo() {
    try {
        const token = localStorage.getItem('auth_token');
        if (!token) {
            return null;
        }

        const response = await fetch('/api/user/info', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.ok) {
            const data = await response.json();
            return data.success ? data.user : null;
        }
        return null;
    } catch (error) {
        console.error('获取用户信息失败:', error);
        return null;
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('auth_token');
    window.location.href = '/login';
}

// 检查并重定向到登录页面
function requireAuth() {
    if (!checkAuth()) {
        window.location.href = '/login';
        return false;
    }
    return true;
}