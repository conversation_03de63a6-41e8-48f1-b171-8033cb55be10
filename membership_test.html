<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员码API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        input {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔍 会员码API测试工具</h1>
    <p>用于诊断会员码系统的网页端问题</p>

    <div class="test-section">
        <h3>1. 基础连接测试</h3>
        <button onclick="testConnection()">测试服务器连接</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 认证测试</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="testLogin()">登录测试</button>
        <button onclick="testAuth()">验证当前Token</button>
        <div id="authResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 会员码API测试</h3>
        <button onclick="testListCodes()">获取会员码列表</button>
        <button onclick="testCreateCode()">创建测试会员码</button>
        <input type="text" id="codeToDelete" placeholder="要删除的会员码">
        <button onclick="testDeleteCode()">删除指定会员码</button>
        <div id="codesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 调试信息</h3>
        <button onclick="showDebugInfo()">显示调试信息</button>
        <button onclick="clearResults()">清空结果</button>
        <div id="debugResult" class="result"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('auth_token');

        function log(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testConnection() {
            log('测试服务器连接');
            try {
                const response = await fetch('/', { method: 'GET' });
                const text = await response.text();
                showResult('connectionResult', 
                    `✅ 连接成功\n状态码: ${response.status}\n响应长度: ${text.length} 字符`);
            } catch (error) {
                showResult('connectionResult', 
                    `❌ 连接失败\n错误: ${error.message}`, true);
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log(`尝试登录: ${username}`);
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                log(`登录响应: ${JSON.stringify(data)}`);

                if (data.success) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showResult('authResult', 
                        `✅ 登录成功\n用户: ${data.user.username}\n角色: ${data.user.role}\nToken: ${authToken.substring(0, 20)}...`);
                } else {
                    showResult('authResult', 
                        `❌ 登录失败\n错误: ${data.message}`, true);
                }
            } catch (error) {
                showResult('authResult', 
                    `❌ 登录请求失败\n错误: ${error.message}`, true);
            }
        }

        async function testAuth() {
            if (!authToken) {
                showResult('authResult', '❌ 没有Token，请先登录', true);
                return;
            }

            log('验证Token');
            try {
                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();
                log(`验证响应: ${JSON.stringify(data)}`);

                if (data.success) {
                    showResult('authResult', 
                        `✅ Token有效\n用户: ${data.user.username}\n角色: ${data.user.role}`);
                } else {
                    showResult('authResult', 
                        `❌ Token无效\n错误: ${data.message}`, true);
                }
            } catch (error) {
                showResult('authResult', 
                    `❌ 验证请求失败\n错误: ${error.message}`, true);
            }
        }

        async function testListCodes() {
            if (!authToken) {
                showResult('codesResult', '❌ 请先登录', true);
                return;
            }

            log('获取会员码列表');
            try {
                const response = await fetch('/api/invitation-codes', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();
                log(`会员码列表响应: ${JSON.stringify(data)}`);

                if (data.success) {
                    const codesInfo = data.codes.map(code => 
                        `${code.code} - ${code.status} - ${code.note || '无备注'}`
                    ).join('\n');
                    
                    showResult('codesResult', 
                        `✅ 获取成功\n总数: ${data.total}\n\n会员码列表:\n${codesInfo}`);
                } else {
                    showResult('codesResult', 
                        `❌ 获取失败\n错误: ${data.message}`, true);
                }
            } catch (error) {
                showResult('codesResult', 
                    `❌ 请求失败\n错误: ${error.message}`, true);
            }
        }

        async function testCreateCode() {
            if (!authToken) {
                showResult('codesResult', '❌ 请先登录', true);
                return;
            }

            log('创建测试会员码');
            try {
                const testData = {
                    valid_days: 7,
                    max_uses: 1,
                    note: '测试会员码备注信息'
                };

                log(`发送数据: ${JSON.stringify(testData)}`);

                const response = await fetch('/api/invitation-codes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();
                log(`创建响应: ${JSON.stringify(data)}`);

                if (data.success) {
                    document.getElementById('codeToDelete').value = data.code;
                    const noteInfo = data.code_info && data.code_info.note ?
                        `\n备注: ${data.code_info.note}` : '\n备注: 无';
                    showResult('codesResult',
                        `✅ 创建成功\n新会员码: ${data.code}${noteInfo}\n已自动填入删除框`);
                } else {
                    showResult('codesResult',
                        `❌ 创建失败\n错误: ${data.message}`, true);
                }
            } catch (error) {
                showResult('codesResult',
                    `❌ 创建请求失败\n错误: ${error.message}`, true);
            }
        }

        async function testDeleteCode() {
            const codeToDelete = document.getElementById('codeToDelete').value.trim();
            if (!codeToDelete) {
                showResult('codesResult', '❌ 请输入要删除的会员码', true);
                return;
            }

            if (!authToken) {
                showResult('codesResult', '❌ 请先登录', true);
                return;
            }

            log(`删除会员码: ${codeToDelete}`);
            try {
                const response = await fetch('/api/invitation-codes/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ codes: [codeToDelete] })
                });

                const data = await response.json();
                log(`删除响应: ${JSON.stringify(data)}`);

                if (data.success) {
                    showResult('codesResult', 
                        `✅ 删除成功\n${data.message}\n删除数量: ${data.deleted_count}`);
                    document.getElementById('codeToDelete').value = '';
                } else {
                    showResult('codesResult', 
                        `❌ 删除失败\n错误: ${data.message}`, true);
                }
            } catch (error) {
                showResult('codesResult', 
                    `❌ 删除请求失败\n错误: ${error.message}`, true);
            }
        }

        function showDebugInfo() {
            const info = {
                'URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Local Storage Token': authToken ? `${authToken.substring(0, 20)}...` : '无',
                'Cookies': document.cookie || '无',
                'Timestamp': new Date().toISOString()
            };

            const debugText = Object.entries(info)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');

            showResult('debugResult', debugText);
        }

        function clearResults() {
            ['connectionResult', 'authResult', 'codesResult', 'debugResult'].forEach(id => {
                document.getElementById(id).textContent = '';
                document.getElementById(id).className = 'result';
            });
        }

        // 页面加载时显示当前状态
        window.onload = function() {
            if (authToken) {
                showResult('authResult', `当前Token: ${authToken.substring(0, 20)}...`);
            }
        };
    </script>
</body>
</html>
