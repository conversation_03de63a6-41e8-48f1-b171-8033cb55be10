#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版WebSocket服务器
解决连接错误和消息处理问题
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局变量
connected_clients = set()
connected_devices = {}

async def handle_client(websocket, path):
    """处理客户端连接"""
    client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    logger.info(f"新连接: {client_addr} (路径: {path})")
    
    # 添加到连接列表
    connected_clients.add(websocket)
    
    try:
        # 发送欢迎消息
        welcome_msg = {
            "type": "connection_established",
            "data": {
                "client_id": client_addr,
                "timestamp": datetime.now().isoformat(),
                "server": "Fixed WebSocket Server v1.0"
            }
        }
        
        await websocket.send(json.dumps(welcome_msg, ensure_ascii=False))
        logger.info(f"发送欢迎消息到: {client_addr}")
        
        # 消息处理循环
        async for message in websocket:
            try:
                logger.info(f"收到消息 [{client_addr}]: {message[:100]}...")
                
                # 尝试解析JSON消息
                if message.startswith('{'):
                    try:
                        msg_data = json.loads(message)
                        msg_type = msg_data.get('type', 'unknown')
                        msg_payload = msg_data.get('data', {})
                        
                        logger.info(f"解析消息类型: {msg_type}")
                        
                        # 处理不同类型的消息
                        if msg_type == 'device_register':
                            await handle_device_register(websocket, client_addr, msg_payload)
                        elif msg_type == 'device_status':
                            await handle_device_status(websocket, client_addr, msg_payload)
                        elif msg_type == 'speak_message':
                            await handle_speak_message(websocket, client_addr, msg_payload)
                        elif msg_type == 'test_ping':
                            await handle_test_ping(websocket, client_addr, msg_payload)
                        else:
                            logger.warning(f"未知消息类型: {msg_type}")
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}")
                        await send_error_response(websocket, "Invalid JSON format")
                        
                else:
                    # 非JSON消息
                    if message.strip().isdigit() and len(message.strip()) <= 3:
                        # 忽略简单数字消息
                        continue
                    else:
                        logger.warning(f"非JSON消息: {message}")
                        
            except Exception as e:
                logger.error(f"处理消息时出错: {e}")
                # 不中断连接，继续处理下一条消息
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"连接正常关闭: {client_addr}")
    except Exception as e:
        logger.error(f"连接处理异常: {client_addr} - {e}")
    finally:
        # 清理连接
        connected_clients.discard(websocket)
        # 清理设备注册
        devices_to_remove = []
        for device_id, device_info in connected_devices.items():
            if device_info.get('websocket') == websocket:
                devices_to_remove.append(device_id)
        
        for device_id in devices_to_remove:
            del connected_devices[device_id]
            logger.info(f"清理设备注册: {device_id}")
        
        logger.info(f"连接清理完成: {client_addr}")

async def handle_device_register(websocket, client_addr, data):
    """处理设备注册"""
    device_id = data.get('device_id', 'unknown')
    device_name = data.get('device_name', device_id)
    device_type = data.get('device_type', 'unknown')
    capabilities = data.get('capabilities', '')
    
    logger.info(f"设备注册: {device_id} ({device_name})")
    
    # 保存设备信息
    connected_devices[device_id] = {
        'device_name': device_name,
        'device_type': device_type,
        'capabilities': capabilities,
        'websocket': websocket,
        'client_addr': client_addr,
        'registered_at': datetime.now().isoformat()
    }
    
    # 发送注册成功响应
    response = {
        "type": "device_register_response",
        "data": {
            "success": True,
            "message": "设备注册成功",
            "device_id": device_id
        }
    }
    
    await websocket.send(json.dumps(response, ensure_ascii=False))
    logger.info(f"设备注册成功: {device_id}")

async def handle_device_status(websocket, client_addr, data):
    """处理设备状态更新"""
    device_id = data.get('device_id', 'unknown')
    status = data.get('status', 'unknown')
    
    logger.info(f"设备状态更新: {device_id} -> {status}")
    
    # 更新设备状态
    if device_id in connected_devices:
        connected_devices[device_id]['status'] = status
        connected_devices[device_id]['last_update'] = datetime.now().isoformat()

async def handle_speak_message(websocket, client_addr, data):
    """处理语音消息"""
    target_device_id = data.get('target_device_id', '')
    message = data.get('message', '')
    
    logger.info(f"语音消息: {message[:50]}... -> {target_device_id}")
    
    # 查找目标设备
    if target_device_id in connected_devices:
        target_device = connected_devices[target_device_id]
        target_websocket = target_device['websocket']
        
        # 转发消息到目标设备
        forward_msg = {
            "type": "speak_message",
            "data": data
        }
        
        try:
            await target_websocket.send(json.dumps(forward_msg, ensure_ascii=False))
            
            # 发送成功响应
            response = {
                "type": "speak_response",
                "data": {
                    "success": True,
                    "message": "消息已发送到设备",
                    "target_device": target_device_id
                }
            }
            await websocket.send(json.dumps(response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"转发消息失败: {e}")
            # 发送失败响应
            response = {
                "type": "speak_response",
                "data": {
                    "success": False,
                    "message": f"转发失败: {str(e)}"
                }
            }
            await websocket.send(json.dumps(response, ensure_ascii=False))
    else:
        # 设备不存在
        response = {
            "type": "speak_response",
            "data": {
                "success": False,
                "message": f"设备 {target_device_id} 未找到"
            }
        }
        await websocket.send(json.dumps(response, ensure_ascii=False))

async def handle_test_ping(websocket, client_addr, data):
    """处理测试ping消息"""
    logger.info(f"收到测试ping: {client_addr}")
    
    # 发送pong响应
    response = {
        "type": "test_pong",
        "data": {
            "message": "pong",
            "timestamp": datetime.now().isoformat(),
            "original_data": data
        }
    }
    
    await websocket.send(json.dumps(response, ensure_ascii=False))

async def send_error_response(websocket, error_message):
    """发送错误响应"""
    response = {
        "type": "error",
        "data": {
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
    }
    
    try:
        await websocket.send(json.dumps(response, ensure_ascii=False))
    except Exception as e:
        logger.error(f"发送错误响应失败: {e}")

async def main():
    """主函数"""
    host = "0.0.0.0"
    port = 15000
    
    logger.info("启动修复版WebSocket服务器...")
    logger.info(f"监听地址: {host}:{port}")
    
    try:
        # 启动服务器
        async with websockets.serve(
            handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10,
            close_timeout=10,
            max_size=2**20,
            max_queue=32
        ):
            logger.info(f"✅ WebSocket服务器启动成功: {host}:{port}")
            logger.info("服务器配置: ping_interval=20s, ping_timeout=10s, max_size=1MB")
            logger.info("等待客户端连接...")
            
            # 保持服务器运行
            await asyncio.Future()  # 永远等待
            
    except OSError as e:
        logger.error(f"无法启动服务器: {e}")
        logger.error(f"请检查端口 {port} 是否被占用")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器被用户停止")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        import traceback
        traceback.print_exc()
