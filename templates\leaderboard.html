<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播间排行榜 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 全局字体设置 */
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 导航栏样式 */
        .navbar-brand-github {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            text-decoration: none;
            color: var(--color-fg-default);
            font-weight: 600;
            font-size: 16px;
        }

        .douyin-logo {
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--color-canvas-overlay);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        .time-display {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 14px;
        }

        /* GitHub风格排行榜界面 */
        .leaderboard-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: var(--base-size-24);
        }

        .header-section {
            text-align: center;
            margin-bottom: var(--base-size-32);
        }

        .header-title {
            font-size: 32px;
            font-weight: 600;
            color: var(--color-fg-default);
            margin-bottom: var(--base-size-8);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--base-size-8);
        }

        .header-subtitle {
            color: var(--color-fg-muted);
            font-size: 18px;
            font-weight: 400;
        }

        .back-button {
            margin-bottom: var(--base-size-24);
        }

        .leaderboards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--base-size-24);
        }

        .leaderboard-card {
            background-color: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            overflow: hidden;
        }

        .leaderboard-header {
            background-color: var(--color-canvas-subtle);
            padding: var(--base-size-16);
            border-bottom: 1px solid var(--color-border-default);
        }

        .leaderboard-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-fg-default);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
        .leaderboard-body {
            padding: 0;
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--base-size-12) var(--base-size-16);
            border-bottom: 1px solid var(--color-border-default);
            transition: background-color 0.2s ease;
        }

        .leaderboard-item:hover {
            background-color: var(--color-canvas-subtle);
        }

        .leaderboard-item:last-child {
            border-bottom: none;
        }

        .rank-info {
            display: flex;
            align-items: center;
            gap: var(--base-size-12);
        }

        .rank-badge {
            width: 28px;
            height: 28px;
            border-radius: var(--borderRadius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            color: var(--color-fg-on-emphasis);
        }

        .rank-1 {
            background-color: #fbbf24;
        }

        .rank-2 {
            background-color: #9ca3af;
        }

        .rank-3 {
            background-color: #cd7c2f;
        }

        .rank-default {
            background-color: var(--color-accent-emphasis);
        }

        .room-info h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--color-fg-default);
        }

        .room-info small {
            color: var(--color-fg-muted);
            font-size: 12px;
            font-weight: 400;
        }

        .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-accent-fg);
            font-family: var(--fontStack-monospace);
        }

        .metric-change {
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: var(--borderRadius-small);
        }

        .change-positive {
            background-color: var(--color-success-subtle);
            color: var(--color-success-fg);
        }

        .change-negative {
            background-color: var(--color-danger-subtle);
            color: var(--color-danger-fg);
        }

        .stats-summary {
            background-color: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            padding: var(--base-size-16);
            margin-bottom: var(--base-size-24);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--base-size-16);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: var(--color-fg-default);
            margin-bottom: var(--base-size-4);
            font-family: var(--fontStack-monospace);
        }

        .stat-label {
            font-size: 12px;
            font-weight: 500;
            color: var(--color-fg-muted);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .leaderboards-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .leaderboard-container {
                padding: var(--base-size-16);
            }
        }
    </style>
</head>
<body>
    <!-- GitHub风格导航栏 -->
    <nav class="github-navbar">
        <div class="github-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <a class="navbar-brand-github" href="/">
                    <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                    <span>AI机器人自动化直播控制台</span>
                </a>

                <!-- 导航链接 -->
                <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                    <a href="/" class="nav-link">
                        <i class="bi bi-house-fill"></i>
                        <span>首页</span>
                    </a>
                    <a href="/leaderboard" class="nav-link active">
                        <i class="bi bi-trophy-fill"></i>
                        <span>直播间排行榜</span>
                    </a>

                    <!-- 管理员专用链接 -->
                    <a href="/users" class="nav-link admin-only" style="display: none;">
                        <i class="bi bi-people-fill"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="/invitation-codes" class="nav-link admin-only" style="display: none;">
                        <i class="bi bi-key-fill"></i>
                        <span>邀请码</span>
                    </a>
                </div>

                <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                    <div class="time-display">
                        <i class="bi bi-clock"></i>
                        <span id="currentTime"></span>
                    </div>

                    <!-- 个人中心下拉菜单 -->
                    <div class="dropdown" id="userDropdown">
                        <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                            <i class="bi bi-person-circle"></i>
                            <span id="currentUsername">用户</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdownMenu">
                            <a href="/profile" class="dropdown-item">
                                <i class="bi bi-person-gear"></i>
                                个人设置
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="leaderboard-container" style="margin-top: var(--base-size-24);">

        <!-- 头部区域 -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="bi bi-trophy-fill"></i>
                直播间排行榜
            </h1>
            <p class="header-subtitle">实时显示当前正在开播的直播间在线观众数量排行</p>
        </div>

        <!-- 刷新按钮 -->
        <div class="github-card" style="margin-bottom: var(--base-size-24);">
            <div class="github-card-body">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="color: var(--color-fg-muted);">
                        <i class="bi bi-broadcast"></i>
                        正在开播的直播间排行榜
                    </div>
                    <button class="btn-github btn-secondary" onclick="loadRoomsRanking()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新排行榜
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-summary">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalLiveRooms">-</div>
                    <div class="stat-label">正在开播</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalViewers">-</div>
                    <div class="stat-label">总观众数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgViewers">-</div>
                    <div class="stat-label">平均观众</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="lastUpdate">-</div>
                    <div class="stat-label">最后更新</div>
                </div>
            </div>
        </div>

        <!-- 直播间排行榜 -->
        <div class="github-card">
            <div class="github-card-header">
                <div class="github-card-title">
                    <i class="bi bi-broadcast"></i>
                    直播间在线观众排行榜
                </div>
                <div class="github-card-subtitle">按当前在线观众数量排序</div>
            </div>
            <div class="github-card-body">
                <div id="roomsRanking">
                    <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                        <i class="bi bi-broadcast" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                        <p>正在加载直播间排行榜...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadRoomsRanking();
            // 每30秒自动刷新一次
            setInterval(loadRoomsRanking, 30000);
        });

        // 加载直播间排行榜
        async function loadRoomsRanking() {
            try {
                const response = await fetch('/api/leaderboard/rooms-ranking');
                const data = await response.json();

                if (data.success) {
                    updateRoomsRanking(data.rooms);
                    updateStats(data);
                } else {
                    console.error('加载直播间排行榜失败:', data.message);
                    showEmptyState();
                }
            } catch (error) {
                console.error('加载直播间排行榜失败:', error);
                showEmptyState();
            }
        }

        // 更新直播间排行榜显示
        function updateRoomsRanking(rooms) {
            const container = document.getElementById('roomsRanking');

            if (!rooms || rooms.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                        <i class="bi bi-broadcast" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                        <p>当前没有正在开播的直播间</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = rooms.map((room, index) => `
                <div class="leaderboard-item" style="display: flex; align-items: center; padding: var(--base-size-12); border-bottom: 1px solid var(--color-border-default);">
                    <div class="rank-badge ${getRankClass(index + 1)}" style="margin-right: var(--base-size-12);">
                        ${index + 1}
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--color-fg-default);">
                            ${room.room_name}
                        </div>
                        <div style="font-size: 12px; color: var(--color-fg-muted);">
                            房间ID: ${room.room_id}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-weight: 600; color: var(--color-accent-fg);">
                            <i class="bi bi-eye-fill"></i>
                            ${room.viewer_count.toLocaleString()}
                        </div>
                        <div style="font-size: 12px; color: var(--color-fg-muted);">
                            观众
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示空状态
        function showEmptyState() {
            const container = document.getElementById('roomsRanking');
            container.innerHTML = `
                <div style="text-align: center; padding: var(--base-size-32); color: var(--color-fg-muted);">
                    <i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: var(--base-size-8);"></i>
                    <p>加载失败，请稍后重试</p>
                </div>
            `;
        }

        // 更新统计数据
        function updateStats(data) {
            const rooms = data.rooms || [];
            const totalLiveRooms = rooms.length;
            const totalViewers = rooms.reduce((sum, room) => sum + room.viewer_count, 0);
            const avgViewers = totalLiveRooms > 0 ? Math.round(totalViewers / totalLiveRooms) : 0;
            const lastUpdate = new Date().toLocaleTimeString('zh-CN');

            document.getElementById('totalLiveRooms').textContent = totalLiveRooms;
            document.getElementById('totalViewers').textContent = totalViewers.toLocaleString();
            document.getElementById('avgViewers').textContent = avgViewers.toLocaleString();
            document.getElementById('lastUpdate').textContent = lastUpdate;
        }



        // 获取排名样式类
        function getRankClass(rank) {
            switch (rank) {
                case 1: return 'rank-1';
                case 2: return 'rank-2';
                case 3: return 'rank-3';
                default: return 'rank-default';
            }
        }

        // 导航栏相关函数
        function toggleUserDropdown() {
            const menu = document.getElementById('userDropdownMenu');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const menu = document.getElementById('userDropdownMenu');

            if (!dropdown.contains(event.target)) {
                menu.classList.remove('show');
            }
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即更新一次

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }

        // 检查管理员权限并显示管理员链接
        async function checkAdminLinks() {
            try {
                const token = localStorage.getItem('auth_token');
                if (!token) return;

                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user.role === 'admin') {
                        document.querySelectorAll('.admin-only').forEach(el => {
                            el.style.display = 'flex';
                        });
                    }
                }
            } catch (error) {
                console.error('检查管理员权限失败:', error);
            }
        }

        // 页面加载时检查管理员权限
        checkAdminLinks();
    </script>
</body>
</html>
