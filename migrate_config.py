#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件迁移脚本
将旧的配置文件结构迁移到新的统一结构
"""

import json
import os
import shutil
from datetime import datetime

def migrate_configurations():
    """迁移配置文件"""
    print("🔄 开始配置文件迁移...")
    
    # 1. 备份原始文件
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'rooms_config.json',
        'devices_config.json',
        'runtime_data.json',
        'leaderboard_data.json',
        'users.json'
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(backup_dir, file))
            print(f"📁 已备份: {file}")
    
    # 2. 创建新的目录结构
    dirs_to_create = [
        'config',
        'config/rooms',
        'config/devices',
        'data',
        'data/runtime',
        'data/logs',
        'data/stats'
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"📂 创建目录: {dir_path}")
    
    # 3. 迁移房间配置
    if os.path.exists('rooms_config.json'):
        with open('rooms_config.json', 'r', encoding='utf-8') as f:
            rooms_data = json.load(f)
        
        for live_id, room_config in rooms_data.items():
            # 转换为新格式
            new_config = {
                "live_settings": {
                    "live_id": live_id
                },
                "esp32_settings": {
                    "device_id": room_config.get('target_device_id', '')
                },
                "room_info": {
                    "room_name": room_config.get('room_name', ''),
                    "enabled": room_config.get('enabled', True),
                    "created_at": room_config.get('created_at', ''),
                    "created_by": room_config.get('created_by', 'system')
                }
            }
            
            # 复制其他配置
            for key, value in room_config.items():
                if key not in ['live_id', 'target_device_id', 'room_name', 'enabled', 'created_at', 'created_by']:
                    new_config[key] = value
            
            # 保存新格式配置
            config_path = f'config/rooms/{live_id}.json'
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(new_config, f, ensure_ascii=False, indent=2)
            print(f"✅ 迁移房间配置: {live_id}")
    
    # 4. 迁移设备配置
    if os.path.exists('devices_config.json'):
        shutil.copy2('devices_config.json', 'config/devices/devices.json')
        print("✅ 迁移设备配置")
    
    # 5. 迁移数据文件
    data_files = {
        'runtime_data.json': 'data/runtime/runtime_data.json',
        'leaderboard_data.json': 'data/stats/leaderboard_data.json',
        'users.json': 'data/runtime/users.json'
    }
    
    for old_path, new_path in data_files.items():
        if os.path.exists(old_path):
            shutil.copy2(old_path, new_path)
            print(f"✅ 迁移数据文件: {old_path} -> {new_path}")
    
    print(f"\n🎉 配置迁移完成！备份文件保存在: {backup_dir}")
    print("📝 请检查新的配置文件并删除旧文件")

if __name__ == "__main__":
    migrate_configurations()
