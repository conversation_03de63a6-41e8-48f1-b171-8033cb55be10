#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux环境下PyInstaller打包脚本
用于将抖音直播聊天机器人系统打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def create_spec_files():
    """创建PyInstaller规格文件"""
    
    # 主聊天机器人规格文件
    live_chat_bot_spec = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['live_chat_bot.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('templates', 'templates'),
        ('protobuf', 'protobuf'),
        ('data', 'data'),
        ('sign.js', '.'),
        ('runtime_data.json', '.'),
        ('liveMan.py', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        # 核心网络和异步库
        'websockets',
        'websocket',
        'requests',
        'urllib3',
        'ssl',
        'socket',
        'asyncio',
        'threading',
        'concurrent.futures',

        # Flask Web框架
        'flask',
        'werkzeug',
        'jinja2',
        'markupsafe',
        'itsdangerous',
        'click',
        'blinker',

        # 基础Python库
        'json',
        'time',
        'datetime',
        'logging',
        're',
        'os',
        'sys',
        'traceback',
        'queue',
        'collections',
        'functools',
        'hashlib',
        'base64',
        'uuid',
        'random',
        'math',
        'copy',
        'weakref',
        'gc',
        'signal',
        'atexit',
        'platform',
        'subprocess',
        'multiprocessing',
        'secrets',
        'string',
        'codecs',
        'gzip',
        'urllib.parse',
        'contextlib',
        'unittest.mock',

        # 直播间数据获取相关
        'liveMan',
        'protobuf',
        'protobuf.douyin',
        'py_mini_racer',
        'betterproto',

        # 项目自定义模块
        'user_management',
        'leaderboard_stats',
        'activation_codes',
        'manage_processes',
        'shared_data',
        'config_manager',

        # 其他可能需要的库
        'difflib',
        'typing',
        'pathlib',
        'importlib',
        'importlib.metadata',
        'packaging',
        'setuptools',
        'wheel',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='live_chat_bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

    # Flask Web服务器规格文件
    flask_server_spec = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['flask_web_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('templates', 'templates'),
        ('protobuf', 'protobuf'),
        ('data', 'data'),
        ('sign.js', '.'),
        ('runtime_data.json', '.'),
        ('liveMan.py', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        # Flask Web框架
        'flask',
        'werkzeug',
        'jinja2',
        'markupsafe',
        'itsdangerous',
        'click',
        'blinker',

        # 网络和通信
        'requests',
        'websockets',
        'websocket',
        'urllib3',
        'ssl',
        'socket',

        # 基础Python库
        'json',
        'os',
        'logging',
        'threading',
        'time',
        'datetime',
        'functools',
        'hashlib',
        'secrets',
        'string',
        'asyncio',
        'concurrent.futures',
        'multiprocessing',
        'subprocess',
        'platform',
        'sys',
        're',
        'traceback',
        'queue',
        'collections',
        'base64',
        'uuid',
        'random',
        'math',
        'copy',
        'codecs',
        'gzip',
        'urllib.parse',
        'contextlib',
        'unittest.mock',

        # 直播间数据获取相关
        'liveMan',
        'protobuf',
        'protobuf.douyin',
        'py_mini_racer',
        'betterproto',

        # 项目自定义模块
        'user_management',
        'leaderboard_stats',
        'activation_codes',
        'manage_processes',
        'shared_data',
        'config_manager',

        # 其他可能需要的库
        'difflib',
        'typing',
        'pathlib',
        'importlib',
        'importlib.metadata',
        'packaging',
        'setuptools',
        'wheel',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='flask_web_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

    # WebSocket服务器规格文件
    websocket_server_spec = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['test_websocket_simple.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('data', 'data'),
        ('runtime_data.json', '.'),
        ('liveMan.py', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        # WebSocket和网络
        'websockets',
        'websocket',
        'asyncio',
        'socket',
        'ssl',
        'concurrent.futures',
        'requests',
        'urllib3',

        # 基础Python库
        'json',
        'threading',
        'time',
        'datetime',
        'logging',
        'os',
        'sys',
        'traceback',
        'functools',
        'collections',
        'queue',
        'hashlib',
        'base64',
        'uuid',
        'random',
        'math',
        'copy',
        'secrets',
        'string',
        'codecs',
        'gzip',
        'urllib.parse',
        'contextlib',
        'unittest.mock',
        'subprocess',
        'platform',
        're',

        # 直播间数据获取相关
        'liveMan',
        'protobuf',
        'protobuf.douyin',
        'py_mini_racer',
        'betterproto',

        # 项目自定义模块
        'user_management',
        'leaderboard_stats',
        'activation_codes',
        'manage_processes',
        'shared_data',
        'config_manager',

        # 其他可能需要的库
        'difflib',
        'typing',
        'pathlib',
        'importlib',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='websocket_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

    # 写入规格文件
    with open('live_chat_bot.spec', 'w', encoding='utf-8') as f:
        f.write(live_chat_bot_spec.strip())
    
    with open('flask_web_server.spec', 'w', encoding='utf-8') as f:
        f.write(flask_server_spec.strip())
    
    with open('websocket_server.spec', 'w', encoding='utf-8') as f:
        f.write(websocket_server_spec.strip())
    
    print("✅ 规格文件创建完成")

def build_executables():
    """构建可执行文件"""
    
    # 创建输出目录
    dist_dir = Path("dist_linux")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir()
    
    # 构建各个组件
    components = [
        ('live_chat_bot.spec', 'live_chat_bot'),
        ('flask_web_server.spec', 'flask_web_server'), 
        ('websocket_server.spec', 'websocket_server')
    ]
    
    for spec_file, exe_name in components:
        print(f"🔨 正在构建 {exe_name}...")
        try:
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--distpath", str(dist_dir), spec_file]
            subprocess.check_call(cmd)
            print(f"✅ {exe_name} 构建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {exe_name} 构建失败: {e}")
            return False
    
    return True

def copy_additional_files():
    """复制额外的配置文件和资源"""

    dist_dir = Path("dist_linux")

    # 需要复制的文件和目录
    items_to_copy = [
        'config',
        'templates',
        'protobuf',
        'data',
        'requirements.txt',
        'README.md',
        'DEPLOYMENT.md',
        'SECURITY.md'
    ]

    # 关键配置文件
    critical_files = [
        'sign.js',
        'runtime_data.json'
    ]

    for item in items_to_copy:
        src = Path(item)
        if src.exists():
            if src.is_dir():
                dst = dist_dir / item
                if dst.exists():
                    shutil.rmtree(dst)
                shutil.copytree(src, dst)
                print(f"📁 复制目录: {item}")
            else:
                shutil.copy2(src, dist_dir / item)
                print(f"📄 复制文件: {item}")

    # 复制关键配置文件
    for item in critical_files:
        src = Path(item)
        if src.exists():
            shutil.copy2(src, dist_dir / item)
            print(f"📄 复制关键文件: {item}")
        else:
            print(f"⚠️ 关键文件不存在: {item}")

    # 确保数据目录结构完整
    ensure_data_structure_linux(dist_dir)

def ensure_data_structure_linux(dist_dir):
    """确保数据目录结构完整"""

    # 创建必要的数据目录
    data_dirs = [
        'data/runtime',
        'data/logs',
        'data/stats',
        'config/devices',
        'config/rooms'
    ]

    for dir_path in data_dirs:
        full_path = dist_dir / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 确保目录存在: {dir_path}")

    # 创建必要的空文件（如果不存在）
    essential_files = [
        'data/runtime/users.json',
        'config/devices/devices.json'
    ]

    for file_path in essential_files:
        full_path = dist_dir / file_path
        if not full_path.exists():
            # 创建默认内容
            if file_path.endswith('users.json'):
                default_content = '{}'
            elif file_path.endswith('devices.json'):
                default_content = '{"devices": {}}'
            else:
                default_content = '{}'

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(default_content)
            print(f"📄 创建默认文件: {file_path}")

def create_startup_script():
    """创建启动脚本"""
    
    startup_script = """#!/bin/bash
# 抖音直播聊天机器人系统启动脚本

echo "🚀 启动抖音直播聊天机器人系统..."

# 清理可能存在的旧进程
echo "🧹 清理旧进程..."
pkill -f websocket_server 2>/dev/null || true
pkill -f flask_web_server 2>/dev/null || true
pkill -f live_chat_bot 2>/dev/null || true
sleep 1

# 检查端口是否被占用
check_port() {
    if command -v lsof >/dev/null 2>&1; then
        if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo "⚠️  端口 $1 已被占用，尝试清理..."
            # 尝试杀死占用端口的进程
            PID=$(lsof -Pi :$1 -sTCP:LISTEN -t 2>/dev/null)
            if [ ! -z "$PID" ]; then
                kill -9 $PID 2>/dev/null || true
                sleep 1
            fi
            # 再次检查
            if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
                echo "❌ 端口 $1 仍被占用，请手动清理"
                return 1
            else
                echo "✅ 端口 $1 已清理"
            fi
        fi
    else
        echo "⚠️  lsof 命令不可用，跳过端口检查"
    fi
    return 0
}

# 检查必要端口
echo "🔍 检查端口状态..."
if ! check_port 15000; then
    echo "❌ WebSocket端口15000清理失败"
    exit 1
fi

if ! check_port 15008; then
    echo "❌ Web服务端口15008清理失败"
    exit 1
fi

# 启动WebSocket服务器
echo "📡 启动WebSocket服务器..."
if ./websocket_server &; then
    WEBSOCKET_PID=$!
    echo "✅ WebSocket服务器已启动 (PID: $WEBSOCKET_PID)"
else
    echo "❌ WebSocket服务器启动失败"
    exit 1
fi

# 等待WebSocket服务器启动
sleep 2

# 启动Flask Web服务器
echo "🌐 启动Web管理界面..."
if ./flask_web_server &; then
    FLASK_PID=$!
    echo "✅ Flask Web服务器已启动 (PID: $FLASK_PID)"
else
    echo "❌ Flask Web服务器启动失败"
    # 清理已启动的WebSocket服务器
    kill $WEBSOCKET_PID 2>/dev/null || true
    exit 1
fi

# 等待服务启动
sleep 3

echo "✅ 系统启动完成!"
echo "📡 WebSocket服务器: ws://localhost:15000"
echo "🌐 Web管理界面: http://localhost:15008"
echo ""
echo "💡 使用说明:"
echo "1. 打开浏览器访问 http://localhost:15008"
echo "2. 在Web界面中添加直播间和设备"
echo "3. 启动直播间机器人"
echo ""

# 清理函数
cleanup() {
    echo "🛑 正在停止服务..."

    # 停止Flask服务器
    if [ ! -z "$FLASK_PID" ]; then
        echo "🔄 正在停止Flask服务器 (PID: $FLASK_PID)..."
        kill $FLASK_PID 2>/dev/null || true
        sleep 1
    fi

    # 停止WebSocket服务器
    if [ ! -z "$WEBSOCKET_PID" ]; then
        echo "🔄 正在停止WebSocket服务器 (PID: $WEBSOCKET_PID)..."
        kill $WEBSOCKET_PID 2>/dev/null || true
        sleep 1
    fi

    # 强制清理残留进程
    pkill -f websocket_server 2>/dev/null || true
    pkill -f flask_web_server 2>/dev/null || true

    echo "👋 系统已停止"
    exit 0
}

# 注册信号处理器
trap cleanup INT TERM

# 保持脚本运行，等待用户中断
echo "⏹️  按Ctrl+C停止所有服务"
while true; do
    # 检查进程是否还在运行
    if ! kill -0 $WEBSOCKET_PID 2>/dev/null; then
        echo "❌ WebSocket服务器意外停止"
        cleanup
    fi

    if ! kill -0 $FLASK_PID 2>/dev/null; then
        echo "❌ Flask服务器意外停止"
        cleanup
    fi

    sleep 1
done
"""
    
    script_path = Path("dist_linux/start_system.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(startup_script.strip())
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print("✅ 启动脚本创建完成")

def main():
    """主函数"""
    print("🔨 开始构建Linux可执行文件...")
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 创建规格文件
    create_spec_files()
    
    # 构建可执行文件
    if not build_executables():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建启动脚本
    create_startup_script()
    
    print("\n🎉 构建完成!")
    print("📦 输出目录: dist_linux/")
    print("🚀 启动命令: cd dist_linux && ./start_system.sh")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
