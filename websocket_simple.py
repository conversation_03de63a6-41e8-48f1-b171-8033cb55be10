#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket服务器测试
解决事件循环问题
"""

import asyncio
import websockets
import json
import os
from datetime import datetime

# 全局设备存储
connected_devices = {}
device_configs = {}
web_clients = set()  # 存储连接的Web客户端

# 直播间和设备的映射关系
room_device_mapping = {}
# 设备使用状态记录（仅用于日志和监控）
device_usage = {}

def load_room_device_mapping():
    """加载直播间和设备的映射关系"""
    global room_device_mapping
    try:
        if os.path.exists('rooms_config.json'):
            with open('rooms_config.json', 'r', encoding='utf-8') as f:
                rooms_config = json.load(f)
                
                # 构建直播间和设备的映射关系
                for live_id, room_info in rooms_config.items():
                    if room_info.get('enabled', False):
                        device_id = room_info.get('target_device_id')
                        if device_id:
                            room_device_mapping[live_id] = device_id
                            print(f"[SUCCESS] 加载直播间映射: 直播间 {live_id} -> 设备 {device_id}")
            
            print(f"[SUCCESS] 加载了 {len(room_device_mapping)} 个直播间设备映射")
    except Exception as e:
        print(f"[ERROR] 加载直播间设备映射失败: {e}")

async def broadcast_to_web_clients(message):
    """向所有Web客户端广播消息"""
    global web_clients
    if web_clients:
        disconnected_clients = set()
        for client in web_clients:
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                print(f"广播消息失败: {e}")
                disconnected_clients.add(client)

        # 移除断开连接的客户端
        web_clients -= disconnected_clients

async def handle_client(websocket, path):
    """处理客户端连接"""
    global web_clients, connected_devices, device_configs

    client_id = "unknown"
    try:
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        print(f"[CONNECT] 新连接: {client_id} (路径: {path})")
    except Exception as e:
        print(f"[ERROR] 无法获取客户端地址: {e}")
        return

    # 标记是否为ESP32设备
    is_esp32_device = False
    device_id_for_cleanup = None

    try:
        # 发送连接确认消息
        welcome_message = json.dumps({
            "type": "connection_established",
            "data": {
                "client_id": client_id,
                "timestamp": datetime.now().isoformat(),
                "server": "ESP32 WebSocket Server v2.0"
            }
        }, ensure_ascii=False)

        await websocket.send(welcome_message)
        print(f"[WELCOME] 发送欢迎消息到: {client_id}")

        # 开始消息循环
        async for message in websocket:
            # 过滤无用的消息日志
            if message.strip() in ['2', '1', '0'] or (isinstance(message, str) and len(message.strip()) <= 2 and message.strip().isdigit()):
                # 跳过简单的数字消息，不打印日志
                continue

            # 检查是否是标准JSON格式消息
            if isinstance(message, str) and message.startswith('{'):
                try:
                    # 解析标准JSON消息：{"type": "event_name", "data": {...}}
                    message_data = json.loads(message)

                    if 'type' in message_data:
                        event_name = message_data['type']
                        event_data = message_data.get('data', {})

                        # 如果不是ESP32设备消息，将其添加到Web客户端列表
                        if not is_esp32_device and event_name not in ['device_register', 'device_status', 'wake_response', 'speak_response']:
                            web_clients.add(websocket)

                        if event_name == 'device_register':
                            # 标记为ESP32设备
                            is_esp32_device = True

                            # 处理设备注册
                            device_id = event_data.get('device_id')
                            device_name = event_data.get('device_name', device_id)
                            device_type = event_data.get('device_type', 'esp32')
                            capabilities = event_data.get('capabilities', '')

                            print("=" * 50)
                            print("[DEBUG] ESP32设备注册处理器被调用了！")
                            print(f"[INFO] 客户端ID: {client_id}")
                            print(f"[INFO] 设备ID: {device_id}")
                            print(f"[INFO] 设备名称: {device_name}")
                            print(f"[INFO] 设备类型: {device_type}")
                            print(f"[INFO] 设备功能: {capabilities}")
                            print("=" * 50)

                            # 直接在本地注册设备，不使用shared_data
                            device_info = {
                                'device_name': device_name,
                                'device_type': device_type,
                                'capabilities': capabilities,
                                'client_id': client_id,
                                'ip': websocket.remote_address[0]
                            }

                            # 保存到本地连接列表
                            connected_devices[device_id] = {
                                'websocket': websocket,
                                'device_info': device_info,
                                'connect_time': datetime.now().isoformat(),
                                'device_state': 'idle'
                            }

                            # 保存到配置文件
                            device_configs[device_id] = {
                                'device_id': device_id,
                                'device_name': device_name,
                                'device_type': device_type,
                                'capabilities': capabilities,
                                'created_at': datetime.now().isoformat(),
                                'last_seen': datetime.now().isoformat(),
                                'enabled': True,
                                'auto_registered': True
                            }

                            # 保存配置文件
                            save_device_configs()

                            # 保存运行时数据
                            save_runtime_data()

                            # 发送注册成功响应
                            response = json.dumps({
                                "type": "device_register_response",
                                "data": {"success": True, "message": "Device registered successfully"}
                            }, ensure_ascii=False)
                            await websocket.send(response)
                            print(f"[SEND] 发送响应: {response}")
                            print(f"[SUCCESS] 设备注册成功: {device_id}")
                            print(f"[INFO] 当前连接设备: {list(connected_devices.keys())}")

                            # 广播设备注册事件到Web客户端
                            try:
                                register_broadcast = json.dumps({
                                    "type": "device_register_response",
                                    "data": {
                                        "device_id": device_id,
                                        "device_name": device_name,
                                        "device_type": device_type,
                                        "success": True
                                    }
                                }, ensure_ascii=False)
                                await broadcast_to_web_clients(register_broadcast)
                            except Exception as e:
                                print(f"[ERROR] 广播设备注册失败: {e}")
                            print("=" * 50)
                        
                        elif event_name == 'device_status':
                            device_id = event_data.get('device_id')
                            device_state = event_data.get('device_state', 'unknown')
                            timestamp = event_data.get('timestamp', 0)
                            can_speak = event_data.get('can_speak', False)
                            voice_detected = event_data.get('voice_detected', False)
                            
                            # 获取状态显示符号
                            state_symbol = {
                                'idle': '[IDLE]',
                                'listening': '[LISTEN]',
                                'speaking': '[SPEAK]',
                                'connecting': '[CONN]',
                                'starting': '[START]',
                                'activating': '[ACTIVE]',
                                'wifi_configuring': '[WIFI]',
                                'unknown': '[UNKNOWN]'
                            }.get(device_state, '[UNKNOWN]')
                            
                            # 更新设备状态
                            if device_id in connected_devices:
                                old_state = connected_devices[device_id].get('device_state', 'unknown')
                                connected_devices[device_id]['device_state'] = device_state
                                connected_devices[device_id]['can_speak'] = can_speak
                                connected_devices[device_id]['voice_detected'] = voice_detected
                                connected_devices[device_id]['last_update'] = datetime.now().isoformat()

                                # 只在状态变化时打印简洁日志
                                if old_state != device_state:
                                    speak_status = "[OK]" if can_speak else "[NO]"
                                    voice_status = "[VOICE]" if voice_detected else ""
                                    print(f"[DEVICE] {device_id[-8:]} {old_state} -> {state_symbol}{device_state} {speak_status}{voice_status}")

                                # 保存运行时数据
                                save_runtime_data()

                                # 广播状态更新到Web客户端
                                try:
                                    status_update = json.dumps({
                                        "type": "device_status_update",
                                        "data": {
                                            "device_id": device_id,
                                            "device_name": device_name,
                                            "device_state": device_state,
                                            "can_speak": can_speak,
                                            "voice_detected": voice_detected,
                                            "timestamp": timestamp
                                        }
                                    }, ensure_ascii=False)
                                    await broadcast_to_web_clients(status_update)
                                except Exception as e:
                                    print(f"[ERROR] 广播失败: {e}")
                            else:
                                print(f"[WARN] 设备 {device_id[-8:]} 未注册")
                        
                        elif event_name == 'wake_device':
                            # 处理唤醒设备指令
                            target_device_id = event_data.get('device_id', '')
                            message = event_data.get('message', '')
                            force = event_data.get('force', False)
                            
                            print("=" * 50)
                            print("[WAKE] 收到设备唤醒指令！")
                            print(f"[INFO] 目标设备: {target_device_id}")
                            print(f"[INFO] 消息内容: {message}")
                            print(f"[INFO] 强制模式: {force}")
                            print("=" * 50)
                            
                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发消息
                                target_websocket = connected_devices[target_device_id]['websocket']
                                
                                # 构造转发消息（移除device_id字段，因为设备知道自己的ID）
                                forward_message = json.dumps({
                                    "type": "wake_device",
                                    "data": {
                                        'message': message,
                                        'force': force
                                    }
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"[SUCCESS] 消息已转发到设备: {target_device_id}")
                                    print(f"[SEND] 转发内容: {forward_message}")
                                    
                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "wake_response",
                                        "data": {"success": True, "message": "Wake command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"[SEND] 发送成功响应: {response}")
                                    
                                except Exception as e:
                                    print(f"[ERROR] 转发消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "wake_response",
                                        "data": {"success": False, "message": f"Failed to send wake command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"[ERROR] 目标设备未找到或未连接: {target_device_id}")
                                print(f"[INFO] 当前连接设备: {list(connected_devices.keys())}")
                                
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "wake_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)
                        
                        elif event_name == 'speak_message':
                            # 处理说话指令
                            target_device_id = event_data.get('device_id', '')
                            message = event_data.get('message', '')
                            force = event_data.get('force', False)
                            live_id = event_data.get('live_id', '')  # 获取来源直播间ID
                            
                            # 简化日志：只显示关键信息
                            print(f"[SPEAK] 发送: {message[:30]}{'...' if len(message) > 30 else ''}")
                            
                            # 验证直播间和设备的映射关系
                            if live_id and live_id in room_device_mapping:
                                expected_device_id = room_device_mapping[live_id]
                                if target_device_id != expected_device_id:
                                    print(f"[ERROR] 直播间 {live_id} 尝试发送消息到非映射设备 {target_device_id}，预期设备为 {expected_device_id}")
                                    # 向发送方回复错误
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": False, "message": f"设备映射错误: 直播间 {live_id} 应该使用设备 {expected_device_id}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    continue

                            if target_device_id and target_device_id in connected_devices:
                                device_data = connected_devices[target_device_id]

                                # 找到目标设备，转发消息
                                if 'websocket' in device_data:
                                    target_websocket = device_data['websocket']
                                else:
                                    print(f"[ERROR] 设备数据中缺少websocket字段: {device_data}")
                                    # 向发送方回复错误
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": False, "message": "Device websocket connection not found"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    continue
                                
                                # 更新设备使用状态（仅用于日志和监控）
                                device_usage[target_device_id] = {
                                    'live_id': live_id,
                                    'last_used': datetime.now().isoformat(),
                                    'message': message
                                }
                                
                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "speak_message",
                                    "data": {
                                        'message': message,
                                        'force': force,
                                        'live_id': live_id  # 添加来源直播间ID
                                    }
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"[SUCCESS] 发送成功 [直播间 {live_id} -> 设备 {target_device_id}]")

                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": True, "message": "Speak command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    
                                except Exception as e:
                                    print(f"[ERROR] 转发说话消息失败: {e}")
                                    
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": False, "message": f"Failed to send speak command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"[ERROR] 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "speak_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)
                        
                        elif event_name == 'reconnect_server':
                            # 处理重连服务器指令
                            target_device_id = event_data.get('device_id', '')
                            
                            print("=" * 50)
                            print("[RECONNECT] 收到设备重连指令！")
                            print(f"[INFO] 目标设备: {target_device_id}")
                            print("=" * 50)
                            
                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发重连消息
                                target_websocket = connected_devices[target_device_id]['websocket']
                                
                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "reconnect_server",
                                    "data": {}
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"[SUCCESS] 重连消息已转发到设备: {target_device_id}")
                                    print(f"[SEND] 转发内容: {forward_message}")
                                    
                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "reconnect_response",
                                        "data": {"success": True, "message": "Reconnect command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"[SEND] 发送成功响应: {response}")
                                    
                                except Exception as e:
                                    print(f"[ERROR] 转发重连消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "reconnect_response",
                                        "data": {"success": False, "message": f"Failed to send reconnect command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"[ERROR] 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "reconnect_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)

                        elif event_name == 'reboot_device':
                            # 处理设备重启指令
                            target_device_id = event_data.get('device_id', '')

                            print("=" * 50)
                            print("[REBOOT] 收到设备重启指令！")
                            print(f"[INFO] 目标设备: {target_device_id}")
                            print("=" * 50)

                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发消息
                                target_websocket = connected_devices[target_device_id]['websocket']

                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "reboot_device",
                                    "data": {}
                                }, ensure_ascii=False)

                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"[SUCCESS] 重启消息已转发到设备: {target_device_id}")
                                    print(f"[SEND] 转发内容: {forward_message}")

                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "reboot_response",
                                        "data": {
                                            "success": True,
                                            "message": "Reboot command sent to device",
                                            "target_device": target_device_id
                                        }
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"[SEND] 发送成功响应: {response}")

                                except Exception as e:
                                    print(f"[ERROR] 转发重启消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "reboot_response",
                                        "data": {
                                            "success": False,
                                            "message": f"Failed to send reboot command: {str(e)}"
                                        }
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"[ERROR] 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "reboot_response",
                                    "data": {
                                        "success": False,
                                        "message": f"Device not found or not connected: {target_device_id}",
                                        "connected_devices": list(connected_devices.keys())
                                    }
                                }, ensure_ascii=False)
                                await websocket.send(response)

                        elif event_name == 'get_all_devices':
                            # 处理设备状态查询请求（可能来自Web客户端或其他客户端）
                            print(f"[INFO] 收到设备状态查询请求 from {client_id}")

                            # 如果不是ESP32设备，将其添加到Web客户端列表
                            if not is_esp32_device:
                                web_clients.add(websocket)
                                print(f"[WEB] Web客户端已添加: {client_id}")

                            devices_info = []
                            for device_id, info in connected_devices.items():
                                device_info = {
                                    'device_id': device_id,
                                    'device_name': info['device_info'].get('device_name', 'Unknown'),
                                    'device_state': info.get('device_state', 'unknown'),
                                    'can_speak': info.get('can_speak', False),
                                    'voice_detected': info.get('voice_detected', False),
                                    'last_update': info.get('last_update', 'Never'),
                                    'connected': True
                                }
                                devices_info.append(device_info)

                            # 发送设备列表响应
                            response = json.dumps({
                                "type": "device_list_response",
                                "data": {
                                    "devices": devices_info,
                                    "total_count": len(devices_info),
                                    "timestamp": datetime.now().isoformat()
                                }
                            }, ensure_ascii=False)

                            await websocket.send(response)
                            print(f"[SEND] 已发送设备列表响应: {len(devices_info)} 台设备")

                        elif event_name == 'wake_response':
                            # 处理设备唤醒响应（来自ESP32设备）
                            device_id = event_data.get('device_id', 'unknown')
                            success = event_data.get('success', False)
                            message = event_data.get('message', '')
                            print(f"[DEVICE] 设备 {device_id} 唤醒响应: {'成功' if success else '失败'} - {message}")

                        elif event_name == 'speak_response':
                            # 处理设备说话响应（来自ESP32设备）
                            device_id = event_data.get('device_id', 'unknown')
                            success = event_data.get('success', False)
                            message = event_data.get('message', '')
                            # 简化响应日志
                            if not success:
                                print(f"[DEVICE] 设备 {device_id} 说话失败: {message}")
                            # 成功时不显示日志，减少冗余

                        else:
                            print(f"[WARN] 未知的ESP32事件: {event_name}")
                    else:
                        pass

                except Exception as e:
                    print(f"[ERROR] JSON解析或消息处理失败: {client_id} - {e}")
                    # 继续处理下一条消息，不中断连接
            else:
                # 只对非数字的未知消息格式打印日志
                if not (isinstance(message, str) and message.strip().isdigit() and len(message.strip()) <= 3):
                    print(f"[WARN] 未知消息格式: {message}")
                # 对于简单数字消息，完全忽略
                
    except websockets.exceptions.ConnectionClosed:
        print(f"[DISCONNECT] 连接正常断开: {client_id}")
    except websockets.exceptions.InvalidMessage as e:
        print(f"[ERROR] 无效的WebSocket消息: {client_id} - {e}")
    except websockets.exceptions.ProtocolError as e:
        print(f"[ERROR] WebSocket协议错误: {client_id} - {e}")
    except EOFError as e:
        print(f"[ERROR] 连接意外关闭: {client_id} - {e}")
    except ConnectionResetError as e:
        print(f"[ERROR] 连接被重置: {client_id} - {e}")
    except Exception as e:
        print(f"[ERROR] 处理连接时发生未知错误: {client_id} - {e}")
        import traceback
        print(f"[TRACE] 异常详情: {traceback.format_exc()}")
    finally:
        # 清理断开的连接
        if is_esp32_device:
            # 清理ESP32设备
            cleanup_device(websocket)
        else:
            # 移除Web客户端
            web_clients.discard(websocket)

def load_device_configs():
    """加载设备配置"""
    global device_configs
    try:
        if os.path.exists('config/devices/devices.json'):
            with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                device_configs = json.load(f)
            print(f"[SUCCESS] 加载了 {len(device_configs)} 个设备配置")
        elif os.path.exists('devices_config.json'):
            with open('devices_config.json', 'r', encoding='utf-8') as f:
                device_configs = json.load(f)
            print(f"[SUCCESS] 加载了 {len(device_configs)} 个设备配置（旧格式）")
    except Exception as e:
        print(f"[ERROR] 加载配置失败: {e}")

def save_device_configs():
    """保存设备配置"""
    try:
        # 确保目录存在
        os.makedirs('config/devices', exist_ok=True)

        # 保存到新的配置路径
        with open('config/devices/devices.json', 'w', encoding='utf-8') as f:
            json.dump(device_configs, f, ensure_ascii=False, indent=2)
        print(f"[SAVE] 设备配置已保存，共 {len(device_configs)} 个设备")
    except Exception as e:
        print(f"[ERROR] 保存配置失败: {e}")

def save_runtime_data():
    """保存运行时数据供Flask服务器使用"""
    try:
        # 创建用于保存的数据副本，不修改原始的connected_devices
        connected_devices_for_save = {}
        for device_id, device_info in connected_devices.items():
            connected_devices_for_save[device_id] = {
                'device_name': device_info['device_info'].get('device_name', device_id),
                'device_type': device_info['device_info'].get('device_type', 'esp32'),
                'device_state': device_info.get('device_state', 'idle'),
                'capabilities': device_info['device_info'].get('capabilities', ''),
                'connect_time': device_info.get('connect_time'),
                'client_id': device_info['device_info'].get('client_id', '')
            }

        # 确保device_configs包含所有配置的设备，而不仅仅是连接过的设备
        # 从配置文件重新加载所有设备配置
        all_device_configs = {}
        try:
            if os.path.exists('config/devices/devices.json'):
                with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                    all_device_configs = json.load(f)
            elif os.path.exists('devices_config.json'):
                with open('devices_config.json', 'r', encoding='utf-8') as f:
                    all_device_configs = json.load(f)
        except Exception as e:
            print(f"[WARN] 重新加载设备配置失败: {e}")
            all_device_configs = device_configs  # 使用当前的device_configs作为备用

        runtime_data = {
            'connected_devices': connected_devices_for_save,  # 使用副本，不影响原始数据
            'device_configs': all_device_configs,  # 使用完整的设备配置
            'status': get_status(),
            'devices_list': get_devices_list(),
            'timestamp': datetime.now().isoformat()
        }

        with open('runtime_data.json', 'w', encoding='utf-8') as f:
            json.dump(runtime_data, f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"[ERROR] 保存运行时数据失败: {e}")

def cleanup_device(websocket):
    """清理断开的设备"""
    global connected_devices
    for device_id, device_info in list(connected_devices.items()):
        if device_info.get('websocket') == websocket:
            del connected_devices[device_id]
            print(f"[DEVICE] 设备断开: {device_id}")

            # 保存运行时数据
            save_runtime_data()
            break

def get_devices_list():
    """获取设备列表"""
    devices = []

    # 添加所有配置中的设备
    for device_id, config in device_configs.items():
        is_online = device_id in connected_devices

        if is_online:
            # 在线设备
            device_info = connected_devices[device_id]
            devices.append({
                'device_id': device_id,
                'device_name': device_info['device_info'].get('device_name', config.get('device_name', device_id)),
                'device_type': device_info['device_info'].get('device_type', config.get('device_type', 'esp32')),
                'device_state': device_info.get('device_state', 'unknown'),
                'capabilities': device_info['device_info'].get('capabilities', config.get('capabilities', '')),
                'online': True,
                'connect_time': device_info.get('connect_time'),
                'auto_registered': config.get('auto_registered', False)
            })
        else:
            # 离线设备
            devices.append({
                'device_id': device_id,
                'device_name': config.get('device_name', device_id),
                'device_type': config.get('device_type', 'esp32'),
                'device_state': 'offline',
                'capabilities': config.get('capabilities', ''),
                'online': False,
                'last_seen': config.get('last_seen', ''),
                'auto_registered': config.get('auto_registered', False)
            })

    return devices

def get_status():
    """获取系统状态"""
    online_devices = len(connected_devices)

    # 获取真实的设备总数（从配置文件）
    total_devices = len(device_configs)
    try:
        if os.path.exists('config/devices/devices.json'):
            with open('config/devices/devices.json', 'r', encoding='utf-8') as f:
                all_configs = json.load(f)
                total_devices = len(all_configs)
        elif os.path.exists('devices_config.json'):
            with open('devices_config.json', 'r', encoding='utf-8') as f:
                all_configs = json.load(f)
                total_devices = len(all_configs)
    except Exception as e:
        print(f"[WARN] 获取设备总数失败: {e}")

    return {
        'server': 'ESP32 WebSocket Server',
        'connected_devices': online_devices,
        'total_devices': total_devices,
        'offline_devices': total_devices - online_devices,
        'timestamp': datetime.now().isoformat(),
        'status': 'running'
    }

async def main():
    """主函数"""
    host = '0.0.0.0'
    port = 15000

    # 加载配置
    load_device_configs()
    
    # 加载直播间和设备的映射关系
    load_room_device_mapping()

    print("[START] 启动标准WebSocket服务器")
    print("=" * 50)
    print(f"[INFO] ESP32连接地址: ws://*************:{port}")
    print("[INFO] 专门处理ESP32设备连接和消息")
    print("[INFO] 支持标准JSON格式消息：{\"type\":\"event_name\",\"data\":{...}}")
    print()
    print("[INFO] 支持的事件类型:")
    print("  - device_register: 设备注册")
    print("  - device_status: 设备状态更新")
    print("  - wake_device: 唤醒设备")
    print("  - speak_message: 让设备说话")
    print("  - reconnect_server: 重连服务器")
    print()
    print("[TEST] 测试命令:")
    print("  python test_wake_device.py")
    print("=" * 50)

    # 启动WebSocket服务器
    try:
        # 创建带错误处理的客户端处理器
        async def safe_handle_client(websocket, path):
            try:
                await handle_client(websocket, path)
            except websockets.exceptions.InvalidMessage as e:
                print(f"[ERROR] 无效WebSocket消息 - 可能是非WebSocket连接尝试: {e}")
            except websockets.exceptions.ProtocolError as e:
                print(f"[ERROR] WebSocket协议错误: {e}")
            except EOFError as e:
                print(f"[ERROR] 连接意外关闭: {e}")
            except ConnectionResetError as e:
                print(f"[ERROR] 连接被重置: {e}")
            except Exception as e:
                print(f"[ERROR] 客户端处理异常: {e}")

        # 创建服务器，添加更多配置选项
        server = await websockets.serve(
            safe_handle_client,
            host,
            port,
            ping_interval=20,  # 每20秒发送ping
            ping_timeout=10,   # ping超时时间
            close_timeout=10,  # 关闭超时时间
            max_size=2**20,    # 最大消息大小 1MB
            max_queue=32       # 最大队列大小
        )

        print(f"[SERVER] WebSocket服务器启动在 {host}:{port}")
        print("[MONITOR] 实时状态监控已启用 - 设备状态变化将立即显示")
        print("[CONFIG] 连接配置: ping_interval=20s, ping_timeout=10s, max_size=1MB")

        # 保持服务器运行
        await server.wait_closed()

    except OSError as e:
        print(f"[ERROR] 无法启动WebSocket服务器: {e}")
        print(f"[HINT] 请检查端口 {port} 是否被占用")
    except Exception as e:
        print(f"[ERROR] 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[STOP] 服务器已停止")
    except Exception as e:
        print(f"\n[ERROR] 程序异常退出: {e}")
        import traceback
        traceback.print_exc()
