#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排行榜统计模块
收集和统计用户互动数据，生成排行榜
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any
import os
import hashlib

class LeaderboardStats:
    """排行榜统计器"""
    
    def __init__(self, data_file: str = "data/stats/leaderboard_data.json"):
        self.data_file = data_file
        self.data_lock = threading.Lock()
        
        # 用户统计数据
        self.user_stats = {}
        
        # 礼物权重配置（与live_chat_bot.py保持一致）
        self.gift_weights = {
            "玫瑰": 3,      # 基础礼物
            "小心心": 3,    # 基础礼物
            "跑车": 10,     # 便宜礼物
            "火箭": 50,     # 中等礼物
            "嘉年华": 100,  # 贵重礼物
            "默认": 3       # 基础礼物
        }
        
        # 称号配置
        self.interaction_titles = {
            1: "王者·嘴强传说",
            2: "星耀·八爪鱼打字员", 
            3: "黄金·人间BB机"
        }
        
        self.gift_titles = {
            1: "公司老板",
            2: "公司总经理",
            3: "公司总监"
        }

        # 官方排行榜数据存储
        self.official_ranks = {}  # {room_id: [rank_data]}

        # 配置的房间列表
        self.configured_rooms = set()
        self.load_rooms_config()

        # 缓存相关变量
        self.cache_timeout = 120  # 2分钟缓存过期时间
        self.last_load_time = 0
        self.file_hash = None
        self.cache_valid = False
        self.load_count = 0  # 加载次数统计

        # 加载历史数据
        self.load_data()

        # 启动定期保存线程
        self.start_auto_save()

    def load_rooms_config(self):
        """加载房间配置，只统计配置文件中的房间（支持新旧配置格式）"""
        try:
            # 优先使用新的配置结构
            if os.path.exists('config/rooms'):
                rooms = {}
                for filename in os.listdir('config/rooms'):
                    if filename.endswith('.json'):
                        live_id = filename[:-5]  # 移除.json后缀
                        try:
                            with open(f'config/rooms/{filename}', 'r', encoding='utf-8') as f:
                                room_config = json.load(f)
                                rooms[live_id] = room_config
                        except Exception as e:
                            print(f"[错误] 排行榜模块加载房间配置失败 {filename}: {e}")

                self.configured_rooms = set(rooms.keys())
                if self.configured_rooms:
                    print(f"[统计] 排行榜模块从新配置结构加载了 {len(self.configured_rooms)} 个房间")
                else:
                    print("[统计] 排行榜将统计所有房间（新配置目录为空）")

            # 兼容旧的配置结构
            elif os.path.exists('rooms_config.json'):
                with open('rooms_config.json', 'r', encoding='utf-8') as f:
                    rooms_config = json.load(f)
                    self.configured_rooms = set(rooms_config.keys())
                    print(f"[统计] 排行榜模块从旧配置文件加载了 {len(self.configured_rooms)} 个房间")
            else:
                print("[统计] 排行榜将统计所有房间（未找到房间配置）")
                self.configured_rooms = set()
        except Exception as e:
            print(f"[错误] 排行榜模块加载房间配置失败: {e}")
            self.configured_rooms = set()

    def reload_rooms_config(self):
        """重新加载房间配置"""
        self.load_rooms_config()

    def is_room_configured(self, room_id: str) -> bool:
        """检查房间是否在配置中"""
        # 如果没有配置房间，则统计所有房间
        if not self.configured_rooms:
            return True
        # 如果有配置房间，则只统计配置中的房间
        return room_id in self.configured_rooms

    def is_valid_message(self, message: str) -> bool:
        """检查消息是否有效（过滤刷屏、无意义消息）"""
        if not message or len(message.strip()) == 0:
            return False

        message = message.strip()

        # 过滤过短的消息
        if len(message) < 2:
            return False

        # 过滤纯数字或纯符号
        if message.isdigit() or all(not c.isalnum() for c in message):
            return False

        # 过滤重复字符（如：aaaaaa, 111111）
        if len(set(message)) == 1 and len(message) > 3:
            return False

        # 过滤常见的刷屏内容
        spam_patterns = [
            '666', '777', '888', '999',
            '哈哈哈哈哈', '呵呵呵呵呵',
            '。。。。。', '？？？？？',
            '！！！！！', '~~~',
            '加油加油', '支持支持',
        ]

        for pattern in spam_patterns:
            if pattern in message and len(message) <= len(pattern) + 2:
                return False

        return True

    def is_valid_interaction_message(self, message: str) -> bool:
        """检查互动消息是否有效（比普通消息检查更宽松）"""
        if not message or len(message.strip()) == 0:
            return False

        message = message.strip()

        # 互动消息允许更短的内容
        if len(message) < 1:
            return False

        # 只过滤明显的刷屏内容，保留更多互动可能性
        spam_patterns = [
            '666666', '777777', '888888', '999999',  # 连续6个以上数字
            '哈哈哈哈哈哈', '呵呵呵呵呵呵',  # 连续6个以上重复字符
        ]

        for pattern in spam_patterns:
            if pattern in message:
                return False

        # 允许短消息，如"好"、"是"、"对"等互动回应
        return True

    def load_data(self, force_reload=False):
        """加载历史数据（带缓存优化）"""
        current_time = time.time()

        # 检查是否需要重新加载
        if not force_reload and self.cache_valid and (current_time - self.last_load_time) < self.cache_timeout:
            return  # 使用缓存数据

        # 检查文件是否有变化
        if not force_reload and os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'rb') as f:
                    file_content = f.read()
                    current_hash = hashlib.md5(file_content).hexdigest()

                if self.file_hash == current_hash and self.cache_valid:
                    self.last_load_time = current_time
                    return  # 文件未变化，使用缓存

                self.file_hash = current_hash
            except Exception:
                pass  # 如果读取失败，继续正常加载流程

        try:
            self.load_count += 1
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.user_stats = data.get('user_stats', {})

                    # 只在首次加载或强制重载时显示详细日志
                    if self.load_count <= 1 or force_reload:
                        print(f"[统计] 排行榜数据已加载，共 {len(self.user_stats)} 个用户")
                        # 调试：显示加载的用户数据
                        if len(self.user_stats) > 0:
                            for uid, udata in list(self.user_stats.items())[:3]:  # 只显示前3个
                                print(f"   用户: {udata.get('user_name', uid)} - 互动: {udata.get('successful_interactions', 0)}")
            else:
                print("[统计] 排行榜数据文件不存在，创建新的统计")
                self.user_stats = {}

            # 更新缓存状态
            self.cache_valid = True
            self.last_load_time = current_time

        except Exception as e:
            print(f"[错误] 加载排行榜数据失败: {e}")
            import traceback
            traceback.print_exc()
            self.user_stats = {}
            self.cache_valid = False
    
    def save_data(self):
        """保存数据到文件"""
        try:
            # 减少保存日志的频率
            if not hasattr(self, '_last_save_log_time'):
                self._last_save_log_time = 0

            if time.time() - self._last_save_log_time > 300:  # 每5分钟最多显示一次保存日志
                print(f"[保存] 开始保存数据: {len(self.user_stats)} 个用户")
                self._last_save_log_time = time.time()

            with self.data_lock:
                data = {
                    'user_stats': self.user_stats,
                    'last_update': datetime.now().isoformat(),
                    'total_users': len(self.user_stats)
                }

                with open(self.data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                # 更新文件哈希值，保持缓存有效性
                with open(self.data_file, 'rb') as f:
                    file_content = f.read()
                    self.file_hash = hashlib.md5(file_content).hexdigest()

                self.last_load_time = time.time()
                self.cache_valid = True

                if time.time() - self._last_save_log_time < 10:  # 只在显示了开始保存日志后10秒内显示成功日志
                    print(f"[成功] 数据保存成功到 {self.data_file}")

        except Exception as e:
            print(f"[错误] 保存排行榜数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def start_auto_save(self):
        """启动自动保存线程"""
        def auto_save():
            while True:
                time.sleep(60)  # 每分钟保存一次
                self.save_data()
        
        save_thread = threading.Thread(target=auto_save, daemon=True)
        save_thread.start()
        print("[统计] 排行榜自动保存线程已启动")
    
    def record_chat_interaction(self, user_id: str, user_name: str, room_id: str,
                               original_message: str, bot_replied: bool = False):
        """记录聊天互动（只记录配置房间的有效消息）"""
        print(f"[搜索] 开始记录互动: 房间={room_id}, 用户={user_name}, 消息={original_message[:20]}..., 机器人回复={bot_replied}")

        # 检查房间是否在配置中
        if not self.is_room_configured(room_id):
            print(f"[跳过] 房间 {room_id} 不在配置中，跳过互动记录")
            print(f"[搜索] 配置的房间: {list(self.configured_rooms)}")
            return

        # 对于互动统计，使用更宽松的消息检查
        if not self.is_valid_interaction_message(original_message):
            print(f"[跳过] 消息无效，跳过互动记录: {original_message[:20]}...")
            return

        print(f"[成功] 消息有效，开始记录数据...")

        with self.data_lock:
            if user_id not in self.user_stats:
                self.user_stats[user_id] = {
                    'user_name': user_name,
                    'total_messages': 0,
                    'successful_interactions': 0,  # 机器人回复的消息数
                    'total_gift_value': 0,
                    'gift_details': {},  # 各种礼物的详细统计
                    'rooms': {},  # 各房间的活动统计
                    'first_seen': datetime.now().isoformat(),
                    'last_seen': datetime.now().isoformat()
                }
            
            user_data = self.user_stats[user_id]
            user_data['user_name'] = user_name  # 更新用户名
            user_data['total_messages'] += 1
            user_data['last_seen'] = datetime.now().isoformat()
            
            # 如果机器人回复了，记录为成功互动
            if bot_replied:
                user_data['successful_interactions'] += 1
                print(f"[统计] 记录成功互动: {user_name} (总互动: {user_data['successful_interactions']})")

            # 房间统计
            if room_id not in user_data['rooms']:
                user_data['rooms'][room_id] = {
                    'messages': 0,
                    'interactions': 0,
                    'gifts': 0
                }

            user_data['rooms'][room_id]['messages'] += 1
            if bot_replied:
                user_data['rooms'][room_id]['interactions'] += 1
                print(f"[统计] 房间 {room_id} 互动: {user_name} (房间互动: {user_data['rooms'][room_id]['interactions']})")

            print(f"[成功] 互动记录完成，当前用户统计总数: {len(self.user_stats)}")
    
    def record_gift(self, user_id: str, user_name: str, room_id: str,
                   gift_name: str, gift_count: int = 1):
        """记录礼物"""
        with self.data_lock:
            if user_id not in self.user_stats:
                self.user_stats[user_id] = {
                    'user_name': user_name,
                    'total_messages': 0,
                    'successful_interactions': 0,
                    'total_gift_value': 0,
                    'gift_details': {},
                    'rooms': {},
                    'first_seen': datetime.now().isoformat(),
                    'last_seen': datetime.now().isoformat()
                }
            
            user_data = self.user_stats[user_id]
            user_data['user_name'] = user_name
            user_data['last_seen'] = datetime.now().isoformat()
            
            # 计算礼物价值
            gift_weight = self.gift_weights.get(gift_name, self.gift_weights.get("默认", 1))
            gift_value = gift_weight * gift_count
            user_data['total_gift_value'] += gift_value
            
            # 礼物详细统计
            if gift_name not in user_data['gift_details']:
                user_data['gift_details'][gift_name] = {
                    'count': 0,
                    'total_value': 0
                }
            
            user_data['gift_details'][gift_name]['count'] += gift_count
            user_data['gift_details'][gift_name]['total_value'] += gift_value
            
            # 房间统计
            if room_id not in user_data['rooms']:
                user_data['rooms'][room_id] = {
                    'messages': 0,
                    'interactions': 0,
                    'gifts': 0
                }
            
            user_data['rooms'][room_id]['gifts'] += gift_value
    
    def get_interaction_leaderboard(self, limit: int = 10, room_id: str = None) -> List[Dict[str, Any]]:
        """获取互动排行榜（按成功对话次数）"""
        # 使用缓存优化的数据加载
        self.load_data()

        # 减少日志频率，只在特定条件下显示
        if self.load_count <= 2 or (hasattr(self, '_last_log_time') and time.time() - self._last_log_time > 300):  # 只在前2次或每5分钟显示一次
            print(f"[搜索] 查询互动排行榜: room_id={room_id}, 配置房间数={len(self.configured_rooms)}, 用户总数={len(self.user_stats)}")
            self._last_log_time = time.time()

        with self.data_lock:
            if room_id:
                # 检查房间是否在配置中
                if not self.is_room_configured(room_id):
                    print(f"[警告] 房间 {room_id} 不在配置中，返回空排行榜")
                    return []

                # 按特定房间的互动次数排序
                filtered_users = []
                for user_id, user_data in self.user_stats.items():
                    room_interactions = user_data.get('rooms', {}).get(room_id, {}).get('interactions', 0)
                    if room_interactions > 0:
                        filtered_users.append((user_id, user_data, room_interactions))

                sorted_users = sorted(filtered_users, key=lambda x: x[2], reverse=True)

                # 减少房间排行榜日志频率
                if not hasattr(self, '_last_room_stats_log_time'):
                    self._last_room_stats_log_time = {}

                if room_id not in self._last_room_stats_log_time or time.time() - self._last_room_stats_log_time.get(room_id, 0) > 120:  # 每2分钟最多显示一次
                    print(f"[统计] 房间 {room_id} 互动排行榜: {len(filtered_users)} 位用户有互动记录")
                    self._last_room_stats_log_time[room_id] = time.time()
            else:
                # 按总互动次数排序（只统计配置房间的数据）
                filtered_users = []
                for user_id, user_data in self.user_stats.items():
                    # 计算配置房间的总互动数
                    total_configured_interactions = 0
                    for r_id, room_data in user_data.get('rooms', {}).items():
                        if self.is_room_configured(r_id):
                            total_configured_interactions += room_data.get('interactions', 0)

                    if total_configured_interactions > 0:
                        filtered_users.append((user_id, user_data, total_configured_interactions))

                sorted_users = sorted(filtered_users, key=lambda x: x[2], reverse=True)

                # 减少全局排行榜日志频率
                if not hasattr(self, '_last_global_stats_log_time'):
                    self._last_global_stats_log_time = 0

                if time.time() - self._last_global_stats_log_time > 120:  # 每2分钟最多显示一次
                    print(f"[统计] 全局互动排行榜: {len(filtered_users)} 位用户有互动记录")
                    self._last_global_stats_log_time = time.time()
            
            leaderboard = []
            if room_id:
                # 特定房间排行榜
                for rank, (user_id, user_data, room_interactions) in enumerate(sorted_users[:limit], 1):
                    title = self.interaction_titles.get(rank, "")
                    room_messages = user_data.get('rooms', {}).get(room_id, {}).get('messages', 0)

                    leaderboard.append({
                        'rank': rank,
                        'user_id': user_id,
                        'user_name': user_data['user_name'],
                        'successful_interactions': room_interactions,
                        'total_messages': room_messages,
                        'interaction_rate': round(
                            room_interactions / max(room_messages, 1) * 100, 1
                        ),
                        'title': title,
                        'last_seen': user_data['last_seen']
                    })
            else:
                # 全局排行榜（使用配置房间的统计数据）
                for rank, (user_id, user_data, total_configured_interactions) in enumerate(sorted_users[:limit], 1):
                    title = self.interaction_titles.get(rank, "")

                    # 计算配置房间的总消息数
                    total_configured_messages = 0
                    for r_id, room_data in user_data.get('rooms', {}).items():
                        if self.is_room_configured(r_id):
                            total_configured_messages += room_data.get('messages', 0)

                    leaderboard.append({
                        'rank': rank,
                        'user_id': user_id,
                        'user_name': user_data['user_name'],
                        'successful_interactions': total_configured_interactions,
                        'total_messages': total_configured_messages,
                        'interaction_rate': round(
                            total_configured_interactions / max(total_configured_messages, 1) * 100, 1
                        ),
                        'title': title,
                        'last_seen': user_data['last_seen']
                    })
            
            return leaderboard
    
    def get_gift_leaderboard(self, limit: int = 10, room_id: str = None) -> List[Dict[str, Any]]:
        """获取礼物排行榜（按礼物贡献值）"""
        with self.data_lock:
            if room_id:
                # 检查房间是否在配置中
                if not self.is_room_configured(room_id):
                    print(f"[警告] 房间 {room_id} 不在配置中，返回空礼物排行榜")
                    return []

                # 按特定房间的礼物价值排序
                filtered_users = []
                for user_id, user_data in self.user_stats.items():
                    room_gifts = user_data.get('rooms', {}).get(room_id, {}).get('gifts', 0)
                    if room_gifts > 0:
                        filtered_users.append((user_id, user_data, room_gifts))

                sorted_users = sorted(filtered_users, key=lambda x: x[2], reverse=True)
                print(f"[统计] 房间 {room_id} 礼物排行榜: {len(filtered_users)} 位用户有送礼记录")
            else:
                # 按配置房间的总礼物价值排序
                filtered_users = []
                for user_id, user_data in self.user_stats.items():
                    # 计算配置房间的总礼物价值
                    total_configured_gifts = 0
                    for r_id, room_data in user_data.get('rooms', {}).items():
                        if self.is_room_configured(r_id):
                            total_configured_gifts += room_data.get('gifts', 0)

                    if total_configured_gifts > 0:
                        filtered_users.append((user_id, user_data, total_configured_gifts))

                sorted_users = sorted(filtered_users, key=lambda x: x[2], reverse=True)
                print(f"[统计] 全局礼物排行榜: {len(filtered_users)} 位用户有送礼记录")
            
            leaderboard = []
            if room_id:
                # 特定房间礼物排行榜
                for rank, (user_id, user_data, room_gifts) in enumerate(sorted_users[:limit], 1):
                    if room_gifts == 0:
                        continue  # 跳过没有送礼物的用户

                    title = self.gift_titles.get(rank, "")

                    # 找出最喜欢送的礼物（全局）
                    favorite_gift = ""
                    if user_data['gift_details']:
                        favorite_gift = max(
                            user_data['gift_details'].items(),
                            key=lambda x: x[1]['count']
                        )[0]

                    leaderboard.append({
                        'rank': rank,
                        'user_id': user_id,
                        'user_name': user_data['user_name'],
                        'total_gift_value': room_gifts,
                        'gift_details': user_data['gift_details'],
                        'favorite_gift': favorite_gift,
                        'title': title,
                        'last_seen': user_data['last_seen']
                    })
            else:
                # 全局礼物排行榜（使用配置房间的统计数据）
                for rank, (user_id, user_data, total_configured_gifts) in enumerate(sorted_users[:limit], 1):
                    if total_configured_gifts == 0:
                        continue  # 跳过没有送礼物的用户

                    title = self.gift_titles.get(rank, "")

                    # 找出最喜欢送的礼物
                    favorite_gift = ""
                    if user_data['gift_details']:
                        favorite_gift = max(
                            user_data['gift_details'].items(),
                            key=lambda x: x[1]['count']
                        )[0]

                    leaderboard.append({
                        'rank': rank,
                        'user_id': user_id,
                        'user_name': user_data['user_name'],
                        'total_gift_value': total_configured_gifts,
                        'gift_details': user_data['gift_details'],
                        'favorite_gift': favorite_gift,
                        'title': title,
                        'last_seen': user_data['last_seen']
                    })
            
            return leaderboard

    def get_rooms(self) -> List[Dict[str, Any]]:
        """获取所有房间列表（固定显示指定房间211953907443）"""
        fixed_room_id = "211953907443"
        
        with self.data_lock:
            # 从实际数据中获取房间统计
            rooms = {}
            for user_data in self.user_stats.values():
                for room_id in user_data.get('rooms', {}):
                    if room_id not in rooms:
                        rooms[room_id] = {
                            'room_id': room_id,
                            'users': 0,
                            'messages': 0,
                            'interactions': 0,
                            'gifts': 0
                        }

                    room_data = user_data['rooms'][room_id]
                    rooms[room_id]['users'] += 1
                    rooms[room_id]['messages'] += room_data.get('messages', 0)
                    rooms[room_id]['interactions'] += room_data.get('interactions', 0)
                    rooms[room_id]['gifts'] += room_data.get('gifts', 0)
            
            # 只返回指定的房间数据
            if fixed_room_id in rooms:
                return [rooms[fixed_room_id]]
            else:
                # 如果指定房间没有数据，返回默认的空房间
                return [{
                    'room_id': fixed_room_id,
                    'users': 0,
                    'messages': 0,
                    'interactions': 0,
                    'gifts': 0
                }]

    def update_official_rank(self, room_id: str, rank_data: List[Dict[str, Any]]):
        """更新官方排行榜数据"""
        with self.data_lock:
            self.official_ranks[room_id] = rank_data
            print(f"[统计] 更新房间 {room_id} 的官方排行榜，共 {len(rank_data)} 位用户")

    def get_official_rank(self, room_id: str = None) -> List[Dict[str, Any]]:
        """获取官方排行榜数据"""
        with self.data_lock:
            if room_id:
                return self.official_ranks.get(room_id, [])
            else:
                # 返回所有房间的官方排行榜汇总
                all_ranks = []
                for room_ranks in self.official_ranks.values():
                    all_ranks.extend(room_ranks)
                return all_ranks

    def get_stats_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        with self.data_lock:
            total_users = len(self.user_stats)
            total_messages = sum(user['total_messages'] for user in self.user_stats.values())
            total_interactions = sum(user['successful_interactions'] for user in self.user_stats.values())
            total_gift_value = sum(user['total_gift_value'] for user in self.user_stats.values())
            
            return {
                'total_users': total_users,
                'total_messages': total_messages,
                'total_interactions': total_interactions,
                'total_gift_value': total_gift_value,
                'interaction_rate': round(total_interactions / max(total_messages, 1) * 100, 1),
                'last_update': datetime.now().isoformat(),
                'cache_info': {
                    'cache_valid': self.cache_valid,
                    'load_count': self.load_count,
                    'cache_age_seconds': time.time() - self.last_load_time if self.last_load_time > 0 else 0,
                    'cache_timeout': self.cache_timeout
                }
            }

    def clear_cache(self):
        """清除缓存，强制下次重新加载"""
        self.cache_valid = False
        self.file_hash = None
        print("[刷新] 排行榜缓存已清除")

# 全局统计实例
leaderboard_stats = LeaderboardStats()
