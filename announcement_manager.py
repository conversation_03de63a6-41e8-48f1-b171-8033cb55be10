#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公告管理模块
处理公告的创建、编辑、删除和用户阅读状态跟踪
"""

import os
import json
import threading
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

class AnnouncementManager:
    """公告管理器"""
    
    def __init__(self, 
                 announcements_file: str = "data/announcements.json",
                 read_status_file: str = "data/announcement_read_status.json"):
        self.announcements_file = announcements_file
        self.read_status_file = read_status_file
        self.announcements_data = {}
        self.read_status_data = {}
        self.lock = threading.Lock()
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.announcements_file), exist_ok=True)
        os.makedirs(os.path.dirname(self.read_status_file), exist_ok=True)
        
        self.load_data()
    
    def load_data(self):
        """加载公告数据和阅读状态"""
        try:
            # 加载公告数据
            if os.path.exists(self.announcements_file):
                with open(self.announcements_file, 'r', encoding='utf-8') as f:
                    self.announcements_data = json.load(f)
                print(f"✅ 加载了 {len(self.announcements_data)} 个公告")
            else:
                self.announcements_data = {}
                print("📝 公告文件不存在，创建新的公告数据")
            
            # 加载阅读状态数据
            if os.path.exists(self.read_status_file):
                with open(self.read_status_file, 'r', encoding='utf-8') as f:
                    self.read_status_data = json.load(f)
                print(f"✅ 加载了阅读状态数据")
            else:
                self.read_status_data = {}
                print("📝 阅读状态文件不存在，创建新的阅读状态数据")
                
        except Exception as e:
            print(f"❌ 加载公告数据失败: {e}")
            self.announcements_data = {}
            self.read_status_data = {}
    
    def save_announcements(self):
        """保存公告数据"""
        try:
            with self.lock:
                temp_file = f"{self.announcements_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self.announcements_data, f, ensure_ascii=False, indent=2)
                
                if os.path.exists(self.announcements_file):
                    os.remove(self.announcements_file)
                os.rename(temp_file, self.announcements_file)
                return True
        except Exception as e:
            print(f"❌ 保存公告数据失败: {e}")
            return False
    
    def save_read_status(self):
        """保存阅读状态数据"""
        try:
            with self.lock:
                temp_file = f"{self.read_status_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self.read_status_data, f, ensure_ascii=False, indent=2)
                
                if os.path.exists(self.read_status_file):
                    os.remove(self.read_status_file)
                os.rename(temp_file, self.read_status_file)
                return True
        except Exception as e:
            print(f"❌ 保存阅读状态数据失败: {e}")
            return False
    
    def create_announcement(self, title: str, content: str, created_by: str, 
                          status: str = "draft") -> Dict[str, Any]:
        """创建公告"""
        try:
            announcement_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            announcement = {
                "id": announcement_id,
                "title": title,
                "content": content,
                "created_by": created_by,
                "created_at": current_time,
                "updated_at": current_time,
                "status": status,  # draft, published, archived
                "published_at": current_time if status == "published" else None
            }
            
            self.announcements_data[announcement_id] = announcement
            
            if self.save_announcements():
                print(f"✅ 创建公告成功: {title}")
                return {"success": True, "announcement": announcement}
            else:
                return {"success": False, "message": "保存公告失败"}
                
        except Exception as e:
            print(f"❌ 创建公告异常: {e}")
            return {"success": False, "message": str(e)}
    
    def update_announcement(self, announcement_id: str, title: str = None, 
                          content: str = None, status: str = None) -> Dict[str, Any]:
        """更新公告"""
        try:
            if announcement_id not in self.announcements_data:
                return {"success": False, "message": "公告不存在"}
            
            announcement = self.announcements_data[announcement_id]
            
            if title is not None:
                announcement["title"] = title
            if content is not None:
                announcement["content"] = content
            if status is not None:
                announcement["status"] = status
                if status == "published" and not announcement.get("published_at"):
                    announcement["published_at"] = datetime.now().isoformat()
            
            announcement["updated_at"] = datetime.now().isoformat()
            
            if self.save_announcements():
                print(f"✅ 更新公告成功: {announcement['title']}")
                return {"success": True, "announcement": announcement}
            else:
                return {"success": False, "message": "保存公告失败"}
                
        except Exception as e:
            print(f"❌ 更新公告异常: {e}")
            return {"success": False, "message": str(e)}
    
    def delete_announcement(self, announcement_id: str) -> Dict[str, Any]:
        """删除公告"""
        try:
            if announcement_id not in self.announcements_data:
                return {"success": False, "message": "公告不存在"}
            
            title = self.announcements_data[announcement_id]["title"]
            del self.announcements_data[announcement_id]
            
            # 同时删除相关的阅读状态
            for username in list(self.read_status_data.keys()):
                if announcement_id in self.read_status_data[username]:
                    del self.read_status_data[username][announcement_id]
            
            if self.save_announcements() and self.save_read_status():
                print(f"✅ 删除公告成功: {title}")
                return {"success": True, "message": "公告删除成功"}
            else:
                return {"success": False, "message": "删除公告失败"}
                
        except Exception as e:
            print(f"❌ 删除公告异常: {e}")
            return {"success": False, "message": str(e)}
    
    def get_announcements(self, status: str = None, include_drafts: bool = False) -> List[Dict]:
        """获取公告列表"""
        try:
            announcements = []
            
            for announcement in self.announcements_data.values():
                # 状态筛选
                if status and announcement["status"] != status:
                    continue
                
                # 是否包含草稿
                if not include_drafts and announcement["status"] == "draft":
                    continue
                
                announcements.append(announcement.copy())
            
            # 按发布时间倒序排列
            announcements.sort(key=lambda x: x.get("published_at") or x["created_at"], reverse=True)
            return announcements
            
        except Exception as e:
            print(f"❌ 获取公告列表异常: {e}")
            return []
    
    def get_announcement(self, announcement_id: str) -> Optional[Dict]:
        """获取单个公告"""
        return self.announcements_data.get(announcement_id)
    
    def mark_as_read(self, username: str, announcement_id: str) -> bool:
        """标记公告为已读"""
        try:
            if username not in self.read_status_data:
                self.read_status_data[username] = {}
            
            self.read_status_data[username][announcement_id] = {
                "read_at": datetime.now().isoformat()
            }
            
            return self.save_read_status()
            
        except Exception as e:
            print(f"❌ 标记已读异常: {e}")
            return False
    
    def get_unread_count(self, username: str) -> int:
        """获取用户未读公告数量"""
        try:
            published_announcements = self.get_announcements(status="published")
            user_read_status = self.read_status_data.get(username, {})
            
            unread_count = 0
            for announcement in published_announcements:
                if announcement["id"] not in user_read_status:
                    unread_count += 1
            
            return unread_count
            
        except Exception as e:
            print(f"❌ 获取未读数量异常: {e}")
            return 0
    
    def get_unread_announcements(self, username: str) -> List[Dict]:
        """获取用户未读公告列表"""
        try:
            published_announcements = self.get_announcements(status="published")
            user_read_status = self.read_status_data.get(username, {})
            
            unread_announcements = []
            for announcement in published_announcements:
                if announcement["id"] not in user_read_status:
                    unread_announcements.append(announcement)
            
            return unread_announcements
            
        except Exception as e:
            print(f"❌ 获取未读公告异常: {e}")
            return []

# 全局公告管理器实例
_announcement_manager = None

def get_announcement_manager():
    """获取公告管理器单例"""
    global _announcement_manager
    if _announcement_manager is None:
        _announcement_manager = AnnouncementManager()
    return _announcement_manager

# 全局实例
announcement_manager = get_announcement_manager()
