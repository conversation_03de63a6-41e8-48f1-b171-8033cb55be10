<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态窗口居中测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .test-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .test-container h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .test-container p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        /* 模态窗口样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .modal-header h2 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            color: #999;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close:hover {
            color: #dc3545;
            background-color: #f8d7da;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }

        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: left;
        }

        .info-box h4 {
            color: #0066cc;
            margin-bottom: 8px;
        }

        .info-box p {
            color: #004499;
            margin: 0;
            font-size: 14px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.9);
            }
            to { 
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        屏幕: <span id="screenSize"></span>
    </div>

    <div class="test-container">
        <h1><i class="bi bi-bullseye"></i> 模态窗口居中测试</h1>
        <p>测试模态窗口在不同屏幕尺寸下的居中效果。窗口将始终保持在屏幕正中央。</p>
        
        <button class="btn" onclick="showModal('small')">
            <i class="bi bi-window"></i> 小窗口
        </button>
        
        <button class="btn btn-success" onclick="showModal('medium')">
            <i class="bi bi-window-plus"></i> 中等窗口
        </button>
        
        <button class="btn btn-warning" onclick="showModal('large')">
            <i class="bi bi-window-fullscreen"></i> 大窗口
        </button>

        <div class="info-box">
            <h4><i class="bi bi-info-circle"></i> 测试说明</h4>
            <p>• 窗口使用 <code>position: absolute</code> + <code>transform: translate(-50%, -50%)</code> 实现完美居中</p>
            <p>• 支持响应式设计，在不同屏幕尺寸下都能正确居中</p>
            <p>• 添加了 <code>max-height: 90vh</code> 和 <code>overflow-y: auto</code> 防止内容溢出</p>
            <p>• 动画效果从中心缩放，视觉效果更自然</p>
        </div>
    </div>

    <!-- 模态窗口 -->
    <div id="testModal" class="modal" style="display: none;">
        <div class="modal-content" id="modalContent">
            <div class="modal-header">
                <h2><i class="bi bi-gear"></i> <span id="modalTitle">测试窗口</span></h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="modalBody">
                    <p>这是一个测试模态窗口，用于验证居中效果。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal()">
                    <i class="bi bi-x-circle"></i> 关闭
                </button>
                <button class="btn btn-success" onclick="addContent()">
                    <i class="bi bi-plus-circle"></i> 添加内容
                </button>
            </div>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            const screenSize = `${window.innerWidth} × ${window.innerHeight}`;
            document.getElementById('screenSize').textContent = screenSize;
        }

        function showModal(size) {
            const modal = document.getElementById('testModal');
            const modalContent = document.getElementById('modalContent');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');
            
            // 重置样式
            modalContent.style.width = '';
            modalContent.style.maxWidth = '';
            
            switch(size) {
                case 'small':
                    modalContent.style.maxWidth = '400px';
                    modalTitle.textContent = '小窗口测试';
                    modalBody.innerHTML = `
                        <p>这是一个小尺寸的模态窗口。</p>
                        <p>窗口宽度限制为 400px。</p>
                        <p>测试在小屏幕设备上的显示效果。</p>
                    `;
                    break;
                case 'medium':
                    modalContent.style.maxWidth = '600px';
                    modalTitle.textContent = '中等窗口测试';
                    modalBody.innerHTML = `
                        <p>这是一个中等尺寸的模态窗口。</p>
                        <p>窗口宽度限制为 600px，这是默认的最大宽度。</p>
                        <p>适合大多数表单和内容展示。</p>
                        <div style="height: 200px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 16px 0;">
                            <span style="color: #666;">模拟内容区域</span>
                        </div>
                    `;
                    break;
                case 'large':
                    modalContent.style.maxWidth = '800px';
                    modalTitle.textContent = '大窗口测试';
                    modalBody.innerHTML = `
                        <p>这是一个大尺寸的模态窗口。</p>
                        <p>窗口宽度限制为 800px，适合复杂的表单或大量内容。</p>
                        <p>测试大内容的滚动效果：</p>
                        ${Array.from({length: 20}, (_, i) => `<p>这是第 ${i + 1} 行测试内容，用于测试长内容的滚动效果。</p>`).join('')}
                    `;
                    break;
            }
            
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('testModal').style.display = 'none';
        }

        function addContent() {
            const modalBody = document.getElementById('modalBody');
            const newContent = document.createElement('div');
            newContent.style.cssText = 'background: #f8f9fa; padding: 12px; margin: 8px 0; border-radius: 6px; border-left: 4px solid #007bff;';
            newContent.innerHTML = `<strong>动态添加的内容 ${Date.now()}</strong><br>测试内容动态变化时的居中效果。`;
            modalBody.appendChild(newContent);
        }

        // 点击模态窗口外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('testModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateScreenInfo);
        
        // 初始化
        updateScreenInfo();
    </script>
</body>
</html>
