#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32连接诊断工具
帮助诊断和解决ESP32设备连接WebSocket服务器的问题
"""

import asyncio
import websockets
import json
import socket
import requests
from datetime import datetime

def test_network_connectivity():
    """测试网络连通性"""
    print("=" * 60)
    print("🌐 网络连通性测试")
    print("=" * 60)
    
    esp32_ip = "*************"  # 从错误日志中获取的IP
    server_port = 15000
    
    # 测试1: Ping测试
    print(f"1. 测试与ESP32设备的网络连通性...")
    try:
        import subprocess
        result = subprocess.run(['ping', '-n', '4', esp32_ip], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Ping {esp32_ip} 成功")
            # 提取延迟信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'ms' in line and '时间' in line:
                    print(f"   {line.strip()}")
        else:
            print(f"❌ Ping {esp32_ip} 失败")
            print(f"   错误: {result.stderr}")
    except Exception as e:
        print(f"❌ Ping测试失败: {e}")
    
    # 测试2: 端口连通性
    print(f"\n2. 测试WebSocket服务器端口连通性...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((esp32_ip, server_port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {server_port} 可达")
        else:
            print(f"❌ 端口 {server_port} 不可达")
            print(f"   错误代码: {result}")
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")
    
    # 测试3: 本地WebSocket服务器状态
    print(f"\n3. 测试本地WebSocket服务器状态...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        local_result = sock.connect_ex(('localhost', server_port))
        sock.close()
        
        if local_result == 0:
            print(f"✅ 本地WebSocket服务器运行正常 (端口 {server_port})")
        else:
            print(f"❌ 本地WebSocket服务器未运行")
            print(f"   请启动服务器: python websocket_server_fixed.py")
    except Exception as e:
        print(f"❌ 本地服务器测试失败: {e}")

def analyze_ssl_error():
    """分析SSL错误"""
    print("\n" + "=" * 60)
    print("🔒 SSL/TLS错误分析")
    print("=" * 60)
    
    print("错误分析:")
    print("- mbedtls_ssl_fetch_input error=29312")
    print("  这个错误码通常表示SSL连接中断或数据读取失败")
    print()
    print("- mbedtls_ssl_handshake returned -0x7280")
    print("  SSL握手失败，错误码 -0x7280 = -29312")
    print()
    print("- Certificate verified.")
    print("  证书验证成功，但这很奇怪，因为我们的服务器不使用SSL")
    print()
    
    print("可能的原因:")
    print("1. ESP32尝试建立HTTPS/WSS连接，但服务器只支持HTTP/WS")
    print("2. ESP32代码中WebSocket URL配置错误")
    print("3. 网络中间件（路由器/防火墙）干扰连接")
    print("4. ESP32的SSL库配置问题")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("🛠️ 解决方案")
    print("=" * 60)
    
    print("方案1: 修改ESP32代码使用非安全WebSocket连接")
    print("----------------------------------------")
    print("在ESP32代码中确保使用:")
    print('  const char* websocket_url = "ws://*************:15000/";')
    print("而不是:")
    print('  const char* websocket_url = "wss://*************:15000/";')
    print()
    
    print("方案2: 检查ESP32 WebSocket客户端配置")
    print("----------------------------------------")
    print("确保ESP32代码中:")
    print("1. 使用正确的WebSocket库（推荐 ArduinoWebsockets）")
    print("2. 不启用SSL/TLS选项")
    print("3. 设置正确的服务器IP和端口")
    print()
    
    print("方案3: 网络配置检查")
    print("----------------------------------------")
    print("1. 确保ESP32和服务器在同一网络")
    print("2. 检查防火墙设置")
    print("3. 确认路由器没有阻止WebSocket连接")
    print()
    
    print("方案4: 服务器端添加SSL支持（高级）")
    print("----------------------------------------")
    print("如果必须使用SSL，需要:")
    print("1. 生成SSL证书")
    print("2. 修改WebSocket服务器支持SSL")
    print("3. 在ESP32中配置证书验证")

async def test_websocket_server():
    """测试WebSocket服务器"""
    print("\n" + "=" * 60)
    print("🔌 WebSocket服务器测试")
    print("=" * 60)
    
    uri = "ws://localhost:15000"
    
    try:
        print(f"尝试连接到: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket服务器连接成功")
            
            # 等待欢迎消息
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"📨 收到欢迎消息: {welcome[:100]}...")
                
                # 模拟ESP32设备注册
                register_msg = {
                    "type": "device_register",
                    "data": {
                        "device_id": "ai_xiaozhi_001",
                        "device_name": "AI小智测试设备",
                        "device_type": "esp32",
                        "capabilities": "voice,led"
                    }
                }
                
                await websocket.send(json.dumps(register_msg, ensure_ascii=False))
                print("📤 发送设备注册消息")
                
                # 等待注册响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📨 注册响应: {response}")
                    
                    response_data = json.loads(response)
                    if response_data.get('type') == 'device_register_response':
                        if response_data.get('data', {}).get('success'):
                            print("✅ 设备注册成功 - WebSocket服务器工作正常")
                        else:
                            print("❌ 设备注册失败")
                    
                except asyncio.TimeoutError:
                    print("⏰ 未收到注册响应")
                
            except asyncio.TimeoutError:
                print("⏰ 未收到欢迎消息")
                
    except ConnectionRefusedError:
        print("❌ WebSocket服务器未运行")
        print("   请启动服务器: python websocket_server_fixed.py")
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")

def create_esp32_example_code():
    """创建ESP32示例代码"""
    print("\n" + "=" * 60)
    print("📝 ESP32示例代码")
    print("=" * 60)
    
    example_code = '''
// ESP32 WebSocket客户端示例代码
#include <WiFi.h>
#include <ArduinoWebsockets.h>

const char* ssid = "your_wifi_ssid";
const char* password = "your_wifi_password";

// 重要：使用 ws:// 而不是 wss://
const char* websocket_server = "ws://*************:15000/";

using namespace websockets;
WebsocketsClient client;

void setup() {
    Serial.begin(115200);
    
    // 连接WiFi
    WiFi.begin(ssid, password);
    while (WiFi.status() != WL_CONNECTED) {
        delay(1000);
        Serial.println("连接WiFi中...");
    }
    Serial.println("WiFi连接成功");
    Serial.print("IP地址: ");
    Serial.println(WiFi.localIP());
    
    // 设置WebSocket回调
    client.onMessage(onMessageCallback);
    client.onEvent(onEventsCallback);
    
    // 连接WebSocket服务器
    connectToWebSocket();
}

void loop() {
    client.poll();
    delay(100);
}

void connectToWebSocket() {
    Serial.println("连接WebSocket服务器...");
    
    // 重要：不要使用SSL选项
    bool connected = client.connect(websocket_server);
    
    if (connected) {
        Serial.println("WebSocket连接成功!");
        
        // 发送设备注册消息
        String registerMsg = "{\\"type\\":\\"device_register\\",\\"data\\":{\\"device_id\\":\\"ai_xiaozhi_001\\",\\"device_name\\":\\"AI小智\\",\\"device_type\\":\\"esp32\\",\\"capabilities\\":\\"voice,led\\"}}";
        client.send(registerMsg);
        
    } else {
        Serial.println("WebSocket连接失败!");
        delay(5000);
        connectToWebSocket(); // 重试连接
    }
}

void onMessageCallback(WebsocketsMessage message) {
    Serial.print("收到消息: ");
    Serial.println(message.data());
}

void onEventsCallback(WebsocketsEvent event, String data) {
    if (event == WebsocketsEvent::ConnectionOpened) {
        Serial.println("WebSocket连接已建立");
    } else if (event == WebsocketsEvent::ConnectionClosed) {
        Serial.println("WebSocket连接已关闭");
        delay(5000);
        connectToWebSocket(); // 重新连接
    } else if (event == WebsocketsEvent::GotPing) {
        Serial.println("收到Ping");
    } else if (event == WebsocketsEvent::GotPong) {
        Serial.println("收到Pong");
    }
}
'''
    
    print("关键修改点:")
    print("1. 使用 ws:// 而不是 wss://")
    print("2. 不要启用SSL/TLS选项")
    print("3. 确保IP地址正确: *************")
    print("4. 确保端口正确: 15000")
    print()
    print("完整示例代码已保存到 esp32_websocket_example.ino")
    
    # 保存示例代码到文件
    with open("esp32_websocket_example.ino", "w", encoding="utf-8") as f:
        f.write(example_code)

async def main():
    """主函数"""
    print("🔍 ESP32连接诊断工具")
    print("时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # 1. 网络连通性测试
    test_network_connectivity()
    
    # 2. SSL错误分析
    analyze_ssl_error()
    
    # 3. 提供解决方案
    provide_solutions()
    
    # 4. 测试WebSocket服务器
    await test_websocket_server()
    
    # 5. 创建ESP32示例代码
    create_esp32_example_code()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成")
    print("=" * 60)
    print("主要问题: ESP32尝试建立SSL连接，但服务器不支持SSL")
    print("解决方法: 修改ESP32代码使用 ws:// 而不是 wss://")
    print("参考示例: esp32_websocket_example.ino")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n诊断被用户中断")
    except Exception as e:
        print(f"\n诊断异常: {e}")
        import traceback
        traceback.print_exc()
