2025-08-14 17:51:29,333 - INFO - 🚀 启动强健WebSocket服务器...
2025-08-14 17:51:29,333 - INFO - 📍 监听地址: 0.0.0.0:15000
2025-08-14 17:51:29,339 - ERROR - ❌ 无法启动服务器: [<PERSON>rrno 10048] error while attempting to bind on address ('0.0.0.0', 15000): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-08-14 17:51:29,339 - ERROR - 💡 请检查端口 15000 是否被占用
2025-08-14 17:52:06,775 - INFO - 🚀 启动强健WebSocket服务器...
2025-08-14 17:52:06,775 - INFO - 📍 监听地址: 0.0.0.0:15000
2025-08-14 17:52:06,780 - ERROR - ❌ 无法启动服务器: [<PERSON><PERSON><PERSON> 10048] error while attempting to bind on address ('0.0.0.0', 15000): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-08-14 17:52:06,780 - ERROR - 💡 请检查端口 15000 是否被占用
2025-08-14 17:52:44,089 - INFO - 🚀 启动强健WebSocket服务器...
2025-08-14 17:52:44,089 - INFO - 📍 监听地址: 0.0.0.0:15000
2025-08-14 17:52:44,094 - ERROR - ❌ 无法启动服务器: [Errno 10048] error while attempting to bind on address ('0.0.0.0', 15000): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-08-14 17:52:44,094 - ERROR - 💡 请检查端口 15000 是否被占用
2025-08-14 18:16:53,579 - INFO - 🚀 启动强健WebSocket服务器...
2025-08-14 18:16:53,580 - INFO - 📍 监听地址: 0.0.0.0:15000
2025-08-14 18:16:53,585 - INFO - server listening on 0.0.0.0:15000
2025-08-14 18:16:53,585 - INFO - ✅ WebSocket服务器启动成功: 0.0.0.0:15000
2025-08-14 18:16:53,586 - INFO - 🔧 服务器配置: ping_interval=30s, ping_timeout=10s, max_size=1MB
2025-08-14 18:16:53,586 - INFO - ⏳ 等待客户端连接...
2025-08-14 18:16:53,586 - INFO - 💡 提示: 握手失败错误是正常的，通常由浏览器访问或无效连接引起
2025-08-14 18:17:13,290 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:17:23,586 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:17:36,673 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:17:53,592 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:18:05,177 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:18:23,599 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:18:38,673 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:18:53,602 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:19:17,175 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:19:23,613 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:19:53,625 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:20:00,685 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:20:23,652 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:20:53,664 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:21:19,189 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:21:23,659 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:21:42,676 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:21:53,679 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:22:11,177 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:22:23,688 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:22:44,668 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:22:53,704 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:23:23,178 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:23:23,725 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:23:53,740 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:24:13,027 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:24:23,758 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:24:36,410 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:24:53,759 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:25:04,884 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:25:23,773 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:25:38,512 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:25:53,788 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:26:16,872 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:26:23,798 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:26:53,811 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:27:00,385 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:27:23,819 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:27:53,826 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:28:18,865 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:28:23,822 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:28:42,390 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:28:53,824 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:29:10,866 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:29:23,832 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:29:44,393 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:29:53,841 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:30:22,877 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:30:23,845 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:30:53,846 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:31:13,895 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:31:23,854 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:31:37,571 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:31:53,862 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:32:06,026 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:32:23,879 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:32:39,609 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:32:53,884 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:33:18,047 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:33:23,890 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:33:53,902 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:34:01,530 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 138, in parse
    request_line = yield from parse_line(read_line)
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 314, in parse_line
    raise EOFError("line without CRLF")
EOFError: line without CRLF

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\server.py", line 545, in parse
    request = yield from Request.parse(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\http11.py", line 140, in parse
    raise EOFError("connection closed while reading HTTP request line") from exc
EOFError: connection closed while reading HTTP request line

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\.conda\envs\hello0814\lib\site-packages\websockets\asyncio\server.py", line 207, in handshake
    raise self.protocol.handshake_exc
websockets.exceptions.InvalidMessage: did not receive a valid HTTP request
2025-08-14 18:34:23,905 - INFO - 📊 连接统计: 总连接=0, 成功=0, 失败握手=0, 当前活跃=0, 注册设备=0
2025-08-14 18:34:49,317 - INFO - server closing
2025-08-14 18:34:49,317 - INFO - server closed
2025-08-14 18:34:49,318 - INFO - 🛑 服务器被用户停止
