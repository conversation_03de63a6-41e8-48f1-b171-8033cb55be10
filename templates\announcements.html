<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - AI机器人自动化直播控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/github-style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 导航栏样式 */
        .navbar-brand-github {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            text-decoration: none;
            color: var(--color-fg-default);
            font-weight: 600;
            font-size: 16px;
        }

        .douyin-logo {
            color: var(--color-fg-default);
            transition: color 0.2s ease;
        }

        .navbar-brand-github:hover .douyin-logo {
            color: var(--color-accent-fg);
        }

        /* 导航链接样式 */
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            color: var(--color-fg-default);
            text-decoration: none;
            border-radius: var(--borderRadius-small);
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: var(--color-canvas-subtle);
            color: var(--color-accent-fg);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--color-accent-subtle);
            color: var(--color-accent-fg);
            font-weight: 600;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            padding: var(--base-size-8) var(--base-size-12);
            background: none;
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-small);
            color: var(--color-fg-default);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .dropdown-toggle:hover {
            background-color: var(--color-canvas-subtle);
            border-color: var(--color-accent-emphasis);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--color-canvas-overlay);
            border: 1px solid var(--color-border-default);
            border-radius: var(--borderRadius-medium);
            box-shadow: var(--shadow-large);
            min-width: 180px;
            z-index: 1000;
            display: none;
            margin-top: var(--base-size-4);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--base-size-8);
            padding: var(--base-size-12) var(--base-size-16);
            color: var(--color-fg-default);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--color-canvas-subtle);
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--color-border-default);
            margin: var(--base-size-4) 0;
        }

        .time-display {
            display: flex;
            align-items: center;
            gap: var(--base-size-4);
            color: var(--color-fg-muted);
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--base-size-16);
            margin-bottom: var(--base-size-24);
        }

        .stat-card {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-16);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: var(--base-size-8);
        }

        .stat-label {
            color: var(--color-fg-muted);
            font-size: 0.875rem;
        }

        .announcement-item {
            background: var(--color-canvas-default);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-16);
            margin-bottom: var(--base-size-12);
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--base-size-12);
        }

        .announcement-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-fg-default);
            margin: 0;
        }

        .announcement-status {
            padding: 2px 8px;
            border-radius: var(--border-radius-small);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-published {
            background: var(--color-success-subtle);
            color: var(--color-success-fg);
        }

        .status-draft {
            background: var(--color-neutral-subtle);
            color: var(--color-fg-muted);
        }

        .status-archived {
            background: var(--color-danger-subtle);
            color: var(--color-danger-fg);
        }

        .announcement-content {
            color: var(--color-fg-muted);
            margin-bottom: var(--base-size-12);
            line-height: 1.5;
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }

        .announcement-content.expanded {
            max-height: none;
        }

        .announcement-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--base-size-12);
            font-size: 0.875rem;
            color: var(--color-fg-muted);
            margin-bottom: var(--base-size-12);
        }

        .announcement-actions {
            display: flex;
            gap: var(--base-size-8);
        }

        .filter-tabs {
            display: flex;
            gap: var(--base-size-8);
            margin-bottom: var(--base-size-16);
            border-bottom: 1px solid var(--color-border-default);
        }

        .filter-tab {
            padding: var(--base-size-8) var(--base-size-16);
            border: none;
            background: none;
            color: var(--color-fg-muted);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .filter-tab.active {
            color: var(--color-accent-fg);
            border-bottom-color: var(--color-accent-fg);
        }

        .filter-tab:hover {
            color: var(--color-fg-default);
        }

        .create-form {
            background: var(--color-canvas-subtle);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-medium);
            padding: var(--base-size-20);
            margin-bottom: var(--base-size-24);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--base-size-16);
            margin-bottom: var(--base-size-16);
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: var(--base-size-4);
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: var(--base-size-8);
            border: 1px solid var(--color-border-default);
            border-radius: var(--border-radius-small);
            font-size: 14px;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .empty-state {
            text-align: center;
            padding: var(--base-size-40);
            color: var(--color-fg-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: var(--base-size-16);
            opacity: 0.5;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: var(--base-size-16);
            border-radius: var(--border-radius-medium);
            box-shadow: var(--shadow-large);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--color-success-subtle);
            border: 1px solid var(--color-success-emphasis);
            color: var(--color-success-fg);
        }

        .notification-error {
            background: var(--color-danger-subtle);
            border: 1px solid var(--color-danger-emphasis);
            color: var(--color-danger-fg);
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--base-size-8);
            font-weight: 600;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
        }

        .notification-close:hover {
            opacity: 1;
        }

        .notification-body {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .newly-created {
            animation: highlight-new 3s ease-out;
            border: 2px solid var(--color-success-emphasis) !important;
        }

        @keyframes highlight-new {
            0% {
                background: var(--color-success-subtle);
                transform: scale(1.02);
            }
            100% {
                background: var(--color-canvas-default);
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="github-container">
        <!-- GitHub风格导航栏 -->
        <nav class="github-navbar">
            <div class="github-container">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <a class="navbar-brand-github" href="/">
                        <svg class="douyin-logo" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                        <span>AI机器人自动化直播控制台</span>
                    </a>

                    <!-- 导航链接 -->
                    <div class="navbar-nav" style="display: flex; align-items: center; gap: var(--base-size-20);">
                        <a href="/" class="nav-link">
                            <i class="bi bi-house-fill"></i>
                            <span>首页</span>
                        </a>
                        <a href="/leaderboard" class="nav-link">
                            <i class="bi bi-trophy-fill"></i>
                            <span>直播间排行榜</span>
                        </a>

                        <!-- 管理员专用链接 -->
                        <a href="/users" class="nav-link admin-only">
                            <i class="bi bi-people-fill"></i>
                            <span>用户管理</span>
                        </a>
                        <a href="/invitation-codes" class="nav-link admin-only">
                            <i class="bi bi-key-fill"></i>
                            <span>会员码</span>
                        </a>
                        <a href="/announcements" class="nav-link active admin-only">
                            <i class="bi bi-megaphone-fill"></i>
                            <span>公告管理</span>
                        </a>
                    </div>

                    <div style="display: flex; align-items: center; gap: var(--base-size-16);">
                        <div class="time-display">
                            <i class="bi bi-clock"></i>
                            <span id="currentTime"></span>
                        </div>

                        <!-- 个人中心下拉菜单 -->
                        <div class="dropdown" id="userDropdown">
                            <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                                <i class="bi bi-person-circle"></i>
                                <span id="currentUsername">用户</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="userDropdownMenu">
                                <a href="/profile" class="dropdown-item">
                                    <i class="bi bi-person-gear"></i>
                                    个人设置
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="github-main">
            <div class="github-header">
                <h1>
                    <i class="bi bi-megaphone-fill"></i>
                    公告管理
                </h1>
                <p>管理系统公告，向用户发布重要信息</p>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalAnnouncements">0</div>
                    <div class="stat-label">总公告数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="publishedAnnouncements">0</div>
                    <div class="stat-label">已发布</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="draftAnnouncements">0</div>
                    <div class="stat-label">草稿</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="archivedAnnouncements">0</div>
                    <div class="stat-label">已归档</div>
                </div>
            </div>

            <!-- 创建公告表单 -->
            <div class="create-form">
                <h3>
                    <i class="bi bi-plus-circle"></i>
                    创建公告
                </h3>
                <form id="createAnnouncementForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">标题</label>
                            <input type="text" id="title" name="title" placeholder="请输入公告标题" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="content">内容</label>
                            <textarea id="content" name="content" placeholder="请输入公告内容" required></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="status">状态</label>
                            <select id="status" name="status">
                                <option value="draft">草稿</option>
                                <option value="published">立即发布</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn-github btn-primary">
                        <i class="bi bi-plus"></i>
                        创建公告
                    </button>
                </form>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="published">已发布</button>
                <button class="filter-tab" data-status="draft">草稿</button>
                <button class="filter-tab" data-status="archived">已归档</button>
            </div>

            <!-- 公告列表 -->
            <div id="announcementsList">
                <div class="empty-state">
                    <i class="bi bi-megaphone"></i>
                    <p>正在加载公告...</p>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/js/auth.js"></script>
    <script>
        let allAnnouncements = [];
        let currentFilter = 'all';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查认证和管理员权限
            if (!checkAuth()) {
                window.location.href = '/login';
                return;
            }

            await checkAdminPermission();
            await loadUserInfo();
            await loadAnnouncements();
            
            // 绑定事件
            document.getElementById('createAnnouncementForm').addEventListener('submit', handleCreateAnnouncement);
            
            // 筛选标签事件
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.status;
                    renderAnnouncements();
                });
            });
        });

        // 检查管理员权限
        async function checkAdminPermission() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (!data.success || data.user.role !== 'admin') {
                        alert('您没有访问此页面的权限，需要管理员权限');
                        window.location.href = '/';
                        return;
                    }
                } else {
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('权限检查失败:', error);
                window.location.href = '/login';
            }
        }

        // 加载用户信息
        async function loadUserInfo() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/user-info', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('currentUsername').textContent = data.display_name || data.username;
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }

        // 加载公告列表
        async function loadAnnouncements() {
            try {
                console.log('开始加载公告...');
                const token = localStorage.getItem('auth_token');

                const response = await fetch('/api/announcements?include_drafts=true', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('公告数据:', data);
                    if (data.success) {
                        allAnnouncements = data.announcements;
                        updateStats();
                        renderAnnouncements();
                    } else {
                        showError('加载公告失败: ' + data.message);
                    }
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('API错误:', errorData);
                    showError('加载公告失败: ' + (errorData.message || '未知错误'));
                }
            } catch (error) {
                console.error('加载公告失败:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 更新统计信息
        function updateStats() {
            const total = allAnnouncements.length;
            const published = allAnnouncements.filter(a => a.status === 'published').length;
            const draft = allAnnouncements.filter(a => a.status === 'draft').length;
            const archived = allAnnouncements.filter(a => a.status === 'archived').length;

            document.getElementById('totalAnnouncements').textContent = total;
            document.getElementById('publishedAnnouncements').textContent = published;
            document.getElementById('draftAnnouncements').textContent = draft;
            document.getElementById('archivedAnnouncements').textContent = archived;
        }

        // 渲染公告列表
        function renderAnnouncements() {
            const container = document.getElementById('announcementsList');
            let filteredAnnouncements = allAnnouncements;

            if (currentFilter !== 'all') {
                filteredAnnouncements = allAnnouncements.filter(a => a.status === currentFilter);
            }

            if (filteredAnnouncements.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-megaphone"></i>
                        <p>没有找到公告</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredAnnouncements.map(announcement => `
                <div class="announcement-item" data-id="${announcement.id}">
                    <div class="announcement-header">
                        <h3 class="announcement-title">${escapeHtml(announcement.title)}</h3>
                        <span class="announcement-status status-${announcement.status}">
                            ${getStatusText(announcement.status)}
                        </span>
                    </div>
                    <div class="announcement-content" id="content-${announcement.id}">
                        ${escapeHtml(announcement.content)}
                    </div>
                    <div class="announcement-meta">
                        <div><strong>创建者:</strong> ${announcement.created_by}</div>
                        <div><strong>创建时间:</strong> ${formatDate(announcement.created_at)}</div>
                        <div><strong>更新时间:</strong> ${formatDate(announcement.updated_at)}</div>
                        ${announcement.published_at ? `<div><strong>发布时间:</strong> ${formatDate(announcement.published_at)}</div>` : ''}
                    </div>
                    <div class="announcement-actions">
                        <button class="btn-github btn-sm" onclick="editAnnouncement('${announcement.id}')">
                            <i class="bi bi-pencil"></i>
                            编辑
                        </button>
                        ${announcement.status === 'draft' ?
                            `<button class="btn-github btn-sm btn-success" onclick="publishAnnouncement('${announcement.id}')">
                                <i class="bi bi-send"></i>
                                发布
                            </button>` : ''
                        }
                        ${announcement.status === 'published' ?
                            `<button class="btn-github btn-sm btn-warning" onclick="archiveAnnouncement('${announcement.id}')">
                                <i class="bi bi-archive"></i>
                                归档
                            </button>` : ''
                        }
                        <button class="btn-github btn-sm btn-danger" onclick="deleteAnnouncement('${announcement.id}')">
                            <i class="bi bi-trash"></i>
                            删除
                        </button>
                        <button class="btn-github btn-sm" onclick="toggleContent('${announcement.id}')">
                            <i class="bi bi-eye"></i>
                            <span id="toggle-text-${announcement.id}">展开</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'published': '已发布',
                'draft': '草稿',
                'archived': '已归档'
            };
            return statusMap[status] || status;
        }

        // 格式化日期
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 切换内容显示
        function toggleContent(announcementId) {
            const contentElement = document.getElementById(`content-${announcementId}`);
            const toggleText = document.getElementById(`toggle-text-${announcementId}`);

            if (contentElement.classList.contains('expanded')) {
                contentElement.classList.remove('expanded');
                toggleText.textContent = '展开';
            } else {
                contentElement.classList.add('expanded');
                toggleText.textContent = '收起';
            }
        }

        // 创建公告
        async function handleCreateAnnouncement(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                title: formData.get('title').trim(),
                content: formData.get('content').trim(),
                status: formData.get('status')
            };

            if (!data.title) {
                showError('标题不能为空');
                return;
            }

            if (!data.content) {
                showError('内容不能为空');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/announcements', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess(`🎉 公告创建成功！状态：${getStatusText(data.status)}`);
                    e.target.reset();
                    await loadAnnouncements();
                    highlightNewAnnouncement(result.announcement.id);
                } else {
                    showError('创建失败: ' + result.message);
                }
            } catch (error) {
                console.error('创建公告失败:', error);
                showError('网络错误');
            }
        }

        // 高亮新创建的公告
        function highlightNewAnnouncement(announcementId) {
            setTimeout(() => {
                const announcementElement = document.querySelector(`[data-id="${announcementId}"]`);
                if (announcementElement) {
                    announcementElement.classList.add('newly-created');
                    announcementElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 500);
        }

        // 编辑公告
        function editAnnouncement(announcementId) {
            const announcement = allAnnouncements.find(a => a.id === announcementId);
            if (!announcement) return;

            // 填充表单
            document.getElementById('title').value = announcement.title;
            document.getElementById('content').value = announcement.content;
            document.getElementById('status').value = announcement.status;

            // 修改表单提交行为
            const form = document.getElementById('createAnnouncementForm');
            const submitButton = form.querySelector('button[type="submit"]');

            // 保存原始处理函数
            if (!form.originalHandler) {
                form.originalHandler = form.onsubmit;
            }

            // 设置编辑模式
            form.dataset.editId = announcementId;
            submitButton.innerHTML = '<i class="bi bi-check"></i> 更新公告';

            // 添加取消按钮
            if (!form.querySelector('.cancel-edit')) {
                const cancelButton = document.createElement('button');
                cancelButton.type = 'button';
                cancelButton.className = 'btn-github btn-secondary cancel-edit';
                cancelButton.innerHTML = '<i class="bi bi-x"></i> 取消编辑';
                cancelButton.onclick = cancelEdit;
                submitButton.parentNode.insertBefore(cancelButton, submitButton.nextSibling);
            }

            // 滚动到表单
            form.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // 取消编辑
        function cancelEdit() {
            const form = document.getElementById('createAnnouncementForm');
            const submitButton = form.querySelector('button[type="submit"]');
            const cancelButton = form.querySelector('.cancel-edit');

            // 重置表单
            form.reset();
            delete form.dataset.editId;
            submitButton.innerHTML = '<i class="bi bi-plus"></i> 创建公告';

            // 移除取消按钮
            if (cancelButton) {
                cancelButton.remove();
            }
        }

        // 更新公告处理
        async function handleUpdateAnnouncement(announcementId, data) {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/announcements/${announcementId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess('🎉 公告更新成功！');
                    cancelEdit();
                    await loadAnnouncements();
                    highlightNewAnnouncement(announcementId);
                } else {
                    showError('更新失败: ' + result.message);
                }
            } catch (error) {
                console.error('更新公告失败:', error);
                showError('网络错误');
            }
        }

        // 发布公告
        async function publishAnnouncement(announcementId) {
            if (!confirm('确定要发布这个公告吗？发布后用户将能看到此公告。')) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/announcements/${announcementId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ status: 'published' })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess('🎉 公告发布成功！');
                    await loadAnnouncements();
                } else {
                    showError('发布失败: ' + result.message);
                }
            } catch (error) {
                console.error('发布公告失败:', error);
                showError('网络错误');
            }
        }

        // 归档公告
        async function archiveAnnouncement(announcementId) {
            if (!confirm('确定要归档这个公告吗？归档后用户将无法看到此公告。')) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/announcements/${announcementId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ status: 'archived' })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess('🎉 公告归档成功！');
                    await loadAnnouncements();
                } else {
                    showError('归档失败: ' + result.message);
                }
            } catch (error) {
                console.error('归档公告失败:', error);
                showError('网络错误');
            }
        }

        // 删除公告
        async function deleteAnnouncement(announcementId) {
            const announcement = allAnnouncements.find(a => a.id === announcementId);
            if (!announcement) return;

            if (!confirm(`确定要删除公告"${announcement.title}"吗？此操作不可撤销。`)) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`/api/announcements/${announcementId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const result = await response.json();
                if (result.success) {
                    showSuccess('🎉 公告删除成功！');
                    await loadAnnouncements();
                } else {
                    showError('删除失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除公告失败:', error);
                showError('网络错误');
            }
        }

        // 修改表单提交处理，支持编辑模式
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('createAnnouncementForm');
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const editId = form.dataset.editId;
                const formData = new FormData(e.target);
                const data = {
                    title: formData.get('title').trim(),
                    content: formData.get('content').trim(),
                    status: formData.get('status')
                };

                if (editId) {
                    // 编辑模式
                    await handleUpdateAnnouncement(editId, data);
                } else {
                    // 创建模式
                    await handleCreateAnnouncement(e);
                }
            });
        });

        // 显示成功消息
        function showSuccess(message, options = {}) {
            showNotification(message, 'success', options);
        }

        // 显示错误消息
        function showError(message, options = {}) {
            showNotification(message, 'error', options);
        }

        // 通用通知函数
        function showNotification(message, type = 'success', options = {}) {
            // 移除现有通知
            const existing = document.querySelector('.notification');
            if (existing) {
                existing.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const title = type === 'success' ? '成功' : '错误';
            const icon = type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill';

            notification.innerHTML = `
                <div class="notification-header">
                    <span><i class="bi ${icon}"></i> ${title}</span>
                    <button class="notification-close" onclick="closeNotification(this)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="notification-body">${message}</div>
            `;

            // 添加到页面
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // 自动关闭
            const duration = options.duration || (type === 'success' ? 5000 : 8000);
            setTimeout(() => {
                closeNotification(notification.querySelector('.notification-close'));
            }, duration);
        }

        // 关闭通知
        function closeNotification(button) {
            const notification = button.closest('.notification');
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }

        // 导航栏相关函数
        function toggleUserDropdown() {
            const menu = document.getElementById('userDropdownMenu');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const menu = document.getElementById('userDropdownMenu');

            if (!dropdown.contains(event.target)) {
                menu.classList.remove('show');
            }
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即更新一次
    </script>
</body>
</html>
