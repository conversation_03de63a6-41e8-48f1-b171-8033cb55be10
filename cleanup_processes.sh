#!/bin/bash
# 清理所有相关进程的脚本

echo "🧹 开始清理所有相关进程..."

# 杀死所有相关进程
echo "🔄 正在停止 douyin_bot_system 进程..."
pkill -f douyin_bot_system
sleep 1

echo "🔄 正在停止 websocket 相关进程..."
pkill -f websocket
sleep 1

echo "🔄 正在停止 flask 相关进程..."
pkill -f flask
sleep 1

echo "🔄 正在停止 python 相关进程..."
pkill -f "python.*live_chat_bot"
pkill -f "python.*flask_web_server"
pkill -f "python.*test_websocket"
sleep 1

# 强制杀死顽固进程
echo "💀 强制清理顽固进程..."
pkill -9 -f douyin_bot_system
pkill -9 -f websocket
pkill -9 -f flask
sleep 1

# 检查剩余进程
echo "📊 检查剩余相关进程..."
REMAINING=$(ps aux | grep -E "(douyin|websocket|flask)" | grep -v grep | wc -l)

if [ $REMAINING -eq 0 ]; then
    echo "✅ 所有进程已清理完成"
else
    echo "⚠️ 还有 $REMAINING 个相关进程在运行:"
    ps aux | grep -E "(douyin|websocket|flask)" | grep -v grep
fi

# 检查端口占用
echo "🔍 检查端口占用情况..."
if command -v lsof >/dev/null 2>&1; then
    echo "端口 15000:"
    lsof -i :15000 || echo "  端口 15000 未被占用"
    echo "端口 15008:"
    lsof -i :15008 || echo "  端口 15008 未被占用"
else
    echo "lsof 命令不可用，无法检查端口占用"
fi

echo "🎉 清理完成！"
