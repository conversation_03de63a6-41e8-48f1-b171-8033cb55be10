<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .timestamp {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            padding: 20px;
            background: #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .warning { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 缓存测试页面</h1>
        <p>这个页面用于测试浏览器缓存是否被正确禁用。</p>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            1. 每次刷新页面，时间戳应该更新<br>
            2. 如果时间戳不变，说明页面被缓存了<br>
            3. 如果时间戳每次都变，说明缓存已被禁用
        </div>

        <div class="timestamp" id="pageTimestamp">
            页面加载时间: <span id="loadTime"></span>
        </div>

        <div class="container">
            <h2>🌐 服务器状态测试</h2>
            <button onclick="testServerStatus()">测试服务器连接</button>
            <button onclick="testAPICache()">测试API缓存</button>
            <button onclick="clearBrowserCache()">清除浏览器缓存指南</button>
            
            <div id="serverStatus" class="status" style="display: none;"></div>
        </div>

        <div class="container">
            <h2>📊 缓存检测结果</h2>
            <div id="cacheResults"></div>
        </div>

        <div class="container">
            <h2>🛠️ 解决缓存问题的方法</h2>
            <div class="info">
                <h3>方法1: 强制刷新</h3>
                <ul>
                    <li><strong>Windows:</strong> Ctrl + F5 或 Ctrl + Shift + R</li>
                    <li><strong>Mac:</strong> Cmd + Shift + R</li>
                </ul>

                <h3>方法2: 开发者工具</h3>
                <ul>
                    <li>按 F12 打开开发者工具</li>
                    <li>右键点击刷新按钮，选择"清空缓存并硬性重新加载"</li>
                    <li>或在 Network 标签页勾选 "Disable cache"</li>
                </ul>

                <h3>方法3: 清除浏览器数据</h3>
                <ul>
                    <li>Chrome/Edge: Ctrl + Shift + Delete</li>
                    <li>选择"缓存的图片和文件"</li>
                    <li>点击"清除数据"</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 显示页面加载时间
        function updateTimestamp() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZoneName: 'short'
            });
            document.getElementById('loadTime').textContent = timeString;
        }

        // 测试服务器状态
        async function testServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status warning';
            statusDiv.textContent = '正在测试服务器连接...';

            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 服务器连接正常！响应时间: ' + new Date().toLocaleTimeString();
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 服务器响应异常: ' + response.status;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 服务器连接失败: ' + error.message;
            }
        }

        // 测试API缓存
        async function testAPICache() {
            const resultsDiv = document.getElementById('cacheResults');
            resultsDiv.innerHTML = '<div class="status warning">正在测试API缓存...</div>';

            const testTimes = [];
            const testCount = 3;

            for (let i = 0; i < testCount; i++) {
                try {
                    const startTime = Date.now();
                    const response = await fetch('/api/status?t=' + Date.now());
                    const endTime = Date.now();
                    
                    if (response.ok) {
                        testTimes.push({
                            test: i + 1,
                            responseTime: endTime - startTime,
                            timestamp: new Date().toLocaleTimeString(),
                            headers: {
                                cacheControl: response.headers.get('Cache-Control'),
                                pragma: response.headers.get('Pragma'),
                                expires: response.headers.get('Expires')
                            }
                        });
                    }
                } catch (error) {
                    console.error('API测试失败:', error);
                }
                
                // 等待500ms再进行下一次测试
                if (i < testCount - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            // 显示测试结果
            let resultHTML = '<h3>API缓存测试结果:</h3>';
            testTimes.forEach(test => {
                const cacheHeaders = test.headers.cacheControl || test.headers.pragma || test.headers.expires;
                const cacheStatus = cacheHeaders ? '缓存已禁用' : '可能有缓存';
                
                resultHTML += `
                    <div class="status ${cacheHeaders ? 'success' : 'warning'}">
                        测试 ${test.test}: ${test.timestamp} - 响应时间: ${test.responseTime}ms - ${cacheStatus}
                        <br>缓存头: ${test.headers.cacheControl || '无'}
                    </div>
                `;
            });

            resultsDiv.innerHTML = resultHTML;
        }

        // 显示清除缓存指南
        function clearBrowserCache() {
            alert(`清除浏览器缓存的方法：

1. 强制刷新：
   - Windows: Ctrl + F5 或 Ctrl + Shift + R
   - Mac: Cmd + Shift + R

2. 开发者工具：
   - 按 F12 打开开发者工具
   - 右键点击刷新按钮，选择"清空缓存并硬性重新加载"

3. 清除浏览器数据：
   - Chrome/Edge: Ctrl + Shift + Delete
   - 选择"缓存的图片和文件"
   - 点击"清除数据"`);
        }

        // 页面加载时执行
        window.addEventListener('load', function() {
            updateTimestamp();
            
            // 检测页面是否来自缓存
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation && navigation.transferSize === 0) {
                document.getElementById('cacheResults').innerHTML = 
                    '<div class="status warning">⚠️ 检测到页面可能来自缓存！请尝试强制刷新。</div>';
            } else {
                document.getElementById('cacheResults').innerHTML = 
                    '<div class="status success">✅ 页面正常加载，未检测到缓存问题。</div>';
            }
        });

        // 每秒更新时间戳
        setInterval(updateTimestamp, 1000);
    </script>
</body>
</html>
