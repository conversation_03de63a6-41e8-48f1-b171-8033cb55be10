# 🔒 安全配置指南

## 🚨 重要安全提醒

**在生产环境部署前，必须完成以下安全配置！**

## 🔑 必需的安全配置

### 1. Flask Secret Key 配置

**⚠️ 默认密钥仅用于开发环境，生产环境必须更改！**

**设置方法：**

**Windows:**
```cmd
set FLASK_SECRET_KEY=your-very-strong-secret-key-minimum-32-characters
```

**Linux/macOS:**
```bash
export FLASK_SECRET_KEY="your-very-strong-secret-key-minimum-32-characters"
```

**生成强密钥：**
```python
import secrets
print(secrets.token_urlsafe(32))
```

### 2. 管理员账户安全

**默认管理员账户：**
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 首次登录后立即修改密码！**

**修改步骤：**
1. 访问 Web 管理界面
2. 登录管理员账户
3. 进入用户管理页面
4. 修改管理员密码

### 3. 网络安全配置

**防火墙设置：**
```bash
# 只允许必要的端口
sudo ufw allow 15008/tcp  # Web管理界面
sudo ufw allow 15000/tcp  # WebSocket服务器
sudo ufw enable
```

**端口访问控制：**
- 端口15008：Web管理界面（建议仅内网访问）
- 端口15000：WebSocket服务器（ESP32设备访问）

## 🛡️ 权限管理

### 用户角色说明

**管理员 (admin):**
- 创建/删除房间
- 管理所有设备
- 用户管理
- 系统配置

**普通用户 (user):**
- 管理分配的房间
- 管理分配的设备
- 查看统计数据

### 房间访问控制

**权限检查机制：**
- 用户只能访问分配给自己的房间
- 管理员可以访问所有房间
- 所有敏感操作都需要身份验证

## 🔐 API 安全

### 身份验证

**所有敏感API都需要认证：**
- Bearer Token 认证
- Session 验证
- 权限级别检查

**API访问示例：**
```javascript
fetch('/api/rooms', {
    headers: {
        'Authorization': 'Bearer your-token-here',
        'Content-Type': 'application/json'
    }
})
```

### 输入验证

**已实现的安全措施：**
- 参数类型检查
- 长度限制验证
- 特殊字符过滤
- SQL注入防护

## 🚫 已知安全限制

### 当前版本限制

1. **HTTPS支持：** 需要反向代理配置
2. **速率限制：** 未实现API调用频率限制
3. **文件上传：** 未实现文件上传功能
4. **审计日志：** 基础日志记录，可增强

### 建议的增强措施

1. **配置HTTPS：**
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ... 其他配置
}
```

2. **添加速率限制：**
```python
from flask_limiter import Limiter
limiter = Limiter(app, key_func=get_remote_address)

@app.route('/api/login', methods=['POST'])
@limiter.limit("5 per minute")
def login():
    # 登录逻辑
```

## 📊 安全监控

### 日志监控

**重要安全事件：**
- 登录失败尝试
- 权限验证失败
- 异常API调用
- 设备连接异常

**日志位置：**
- 应用日志：`data/logs/`
- 系统日志：`/var/log/syslog`

### 监控脚本示例

```bash
#!/bin/bash
# 监控登录失败
tail -f data/logs/*.log | grep "登录失败" | while read line; do
    echo "$(date): 检测到登录失败 - $line"
    # 可以添加告警逻辑
done
```

## 🔧 安全配置检查清单

### 部署前检查

- [ ] 已设置强密钥 `FLASK_SECRET_KEY`
- [ ] 已修改默认管理员密码
- [ ] 已配置防火墙规则
- [ ] 已设置适当的文件权限
- [ ] 已配置HTTPS（推荐）
- [ ] 已设置日志轮转
- [ ] 已配置备份策略

### 运行时检查

- [ ] 定期检查用户权限
- [ ] 监控异常登录尝试
- [ ] 检查设备连接状态
- [ ] 审查API访问日志
- [ ] 更新系统和依赖

## 🚨 安全事件响应

### 发现安全问题时

1. **立即行动：**
   - 停止相关服务
   - 隔离受影响系统
   - 保存相关日志

2. **评估影响：**
   - 确定受影响范围
   - 检查数据完整性
   - 评估损失程度

3. **修复措施：**
   - 修复安全漏洞
   - 更新密码和密钥
   - 加强监控措施

4. **恢复服务：**
   - 验证修复效果
   - 逐步恢复服务
   - 持续监控

## 📞 安全支持

**如发现安全问题，请立即联系：**
- 系统管理员
- 技术支持团队

**报告内容应包括：**
- 问题描述
- 影响范围
- 相关日志
- 复现步骤

---

**安全是持续的过程，请定期检查和更新安全配置。**
