#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统启动脚本
启动WebSocket服务器和Flask Web服务器
"""

import subprocess
from subprocess import TimeoutExpired
import sys
import time
import os
import signal
import threading

# 全局变量存储进程列表
processes = []

def cleanup_processes():
    """清理所有进程"""
    print("\n🛑 正在停止所有服务...")

    for process in processes:
        try:
            if process.poll() is None:  # 进程仍在运行
                print(f"🔄 正在停止进程 {process.pid}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                    print(f"✅ 进程 {process.pid} 已正常停止")
                except TimeoutExpired:
                    print(f"⚠️ 进程 {process.pid} 未响应，强制终止...")
                    process.kill()
                    process.wait()
                    print(f"✅ 进程 {process.pid} 已强制停止")
            else:
                print(f"ℹ️ 进程 {process.pid} 已经停止")
        except Exception as e:
            print(f"❌ 停止进程 {process.pid} 时出错: {e}")

    print("👋 系统已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    cleanup_processes()
    sys.exit(0)

def start_websocket_server():
    """启动WebSocket服务器"""
    print("🚀 启动WebSocket服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, "websocket_simple.py"
        ], cwd=os.getcwd())
        print(f"✅ WebSocket服务器已启动 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ 启动WebSocket服务器失败: {e}")
        return None

def start_flask_server():
    """启动Flask Web服务器"""
    print("🚀 启动Flask Web服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, "flask_web_server.py"
        ], cwd=os.getcwd())
        print(f"✅ Flask Web服务器已启动 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ 启动Flask Web服务器失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 多直播间机器人管理系统启动器")
    print("=" * 50)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    global processes

    try:
        # 启动WebSocket服务器
        ws_process = start_websocket_server()
        if ws_process:
            processes.append(ws_process)
            time.sleep(2)  # 等待WebSocket服务器启动
        
        # 启动Flask Web服务器
        flask_process = start_flask_server()
        if flask_process:
            processes.append(flask_process)
            time.sleep(2)  # 等待Flask服务器启动
        
        if processes:
            print("\n✅ 系统启动完成!")
            print("📡 WebSocket服务器: ws://localhost:15000")
            print("🌐 Web管理界面: http://localhost:15008")
            print("\n💡 使用说明:")
            print("1. 打开浏览器访问 http://localhost:15008")
            print("2. 在Web界面中添加直播间和设备")
            print("3. 启动直播间机器人")
            print("\n⏹️ 按Ctrl+C停止所有服务")
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                cleanup_processes()
            except Exception as e:
                print(f"❌ 系统运行异常: {e}")
                cleanup_processes()
        else:
            print("❌ 系统启动失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 系统启动异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
